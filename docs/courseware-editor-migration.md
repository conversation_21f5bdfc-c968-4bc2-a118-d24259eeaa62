# 课件编辑功能独立为 NPM Package 迁移方案

## 1. 项目概述

### 1.1 目标

将约读书房后台集成系统中的课件编辑功能独立为可复用的 NPM Package，实现跨项目复用和独立维护。

### 1.2 范围

-   核心编辑器组件
-   模板系统
-   数据管理
-   API 适配层
-   文件上传功能
-   预览播放功能

## 2. 业务流程分析

### 2.1 完整业务流程

```mermaid
graph TD
    A[进入课件编辑] --> B[加载课件基础信息]
    B --> C[渲染左侧菜单树]
    C --> D[选择教学环节]
    D --> E[加载教学页详情]
    E --> F[渲染编辑器界面]
    F --> G[选择模板类型]
    G --> H[编辑内容]
    H --> I[实时预览]
    I --> J{是否保存}
    J -->|是| K[验证数据]
    K --> L[保存到服务器]
    L --> M[更新预览]
    J -->|否| N[继续编辑]
    N --> H
    M --> O{是否发布}
    O -->|是| P[发布课件]
    O -->|否| Q[返回编辑]
    P --> R[完成]
    Q --> D
```

### 2.2 核心组件功能清单

#### 2.2.1 主编辑器组件 (`contentEdit.vue`)

-   **功能**：课件编辑主界面
-   **职责**：
    -   整体布局管理
    -   组件间通信协调
    -   状态管理
    -   保存和发布控制
-   **关键方法**：
    -   `handleClickMenuCell()` - 处理菜单点击
    -   `handleSaveAll()` - 保存所有数据
    -   `onValueChange()` - 数据变更处理
    -   `loadPageDetail()` - 加载页面详情

#### 2.2.2 菜单管理组件 (`menuView.vue`)

-   **功能**：左侧教学环节菜单树
-   **职责**：
    -   教学环节的增删改查
    -   拖拽排序
    -   模板选择
    -   环节名称编辑
-   **关键方法**：
    -   `fetchPhases()` - 获取教学环节列表
    -   `addPhase()` - 添加新环节
    -   `deletePhase()` - 删除环节
    -   `sortPhases()` - 排序环节
    -   `handleDragEnd()` - 拖拽结束处理

#### 2.2.3 数据编辑组件 (`dataView.vue`)

-   **功能**：右侧内容编辑区域
-   **职责**：
    -   标签页管理（内容编辑、飞飞辅助工具、笔顺工具）
    -   动态模板加载
    -   数据验证
    -   表单状态管理
-   **关键方法**：
    -   `getAllData()` - 获取所有编辑数据
    -   `setActiveIndex()` - 切换标签页
    -   `validateForm()` - 表单验证

#### 2.2.4 菜单项组件 (`menuCell.vue`)

-   **功能**：单个教学环节菜单项
-   **职责**：
    -   环节信息展示
    -   状态指示（已编辑、未编辑）
    -   右键菜单操作
-   **关键方法**：
    -   `handleClick()` - 点击处理
    -   `showContextMenu()` - 显示右键菜单
    -   `updateStatus()` - 更新状态

#### 2.2.5 全屏预览组件 (`fullscreenPreview.vue`)

-   **功能**：课件全屏预览
-   **职责**：
    -   全屏模式切换
    -   播放器初始化
    -   预览数据加载
-   **关键方法**：
    -   `toggleFullscreen()` - 切换全屏
    -   `createPlay()` - 创建播放器
    -   `getCoursewarePreviewData()` - 获取预览数据

#### 2.2.6 模板组件系统

-   **位置**：`components/templates/`
-   **功能**：各种内容模板
-   **包含模板**：
    -   文本模板
    -   图片模板
    -   视频模板
    -   音频模板
    -   互动模板
    -   测试模板

#### 2.2.7 页面工具组件

-   **位置**：`components/pageUtils/`
-   **功能**：编辑辅助工具
-   **包含组件**：
    -   颜色选择器
    -   字体选择器
    -   对齐工具
    -   图层管理

#### 2.2.8 通用组件

-   **位置**：`components/common/`
-   **功能**：通用 UI 组件
-   **包含组件**：
    -   `delayConfirm.vue` - 延迟确认对话框
    -   `phaseModelAlert.vue` - 环节模板选择弹窗

## 3. 数据流程分析

### 3.1 数据流向图

```mermaid
graph LR
    A[用户操作] --> B[组件事件]
    B --> C[状态更新]
    C --> D[数据验证]
    D --> E[API调用]
    E --> F[服务器响应]
    F --> G[状态同步]
    G --> H[UI更新]
    H --> I[预览更新]
```

### 3.2 核心数据结构

#### 3.2.1 课件基础数据

```typescript
interface CoursewareData {
    id: string;
    name: string;
    materielId: string;
    dataTemplateId: string;
    status: 'draft' | 'published';
    version: string;
    createTime: string;
    updateTime: string;
}
```

#### 3.2.2 教学环节数据

```typescript
interface PhaseData {
    id: string;
    stepParent?: string;
    stepName: string;
    templateId: number;
    templateName: string;
    sort: number;
    status: 'edited' | 'unedited';
    pageCount: number;
}
```

#### 3.2.3 教学页数据

```typescript
interface PageData {
    stepId: string;
    templateId: string;
    stepName: string;
    viewUrl: string;
    details: {
        type: string;
        content: any;
        config: any;
    };
    tool: {
        assistant?: any;
        strokeOrder?: any;
    };
    notes?: {
        type: 'text';
        content: string;
    };
}
```

### 3.3 数据流转过程

1. **初始化阶段**

    - 加载课件基础信息 → `fetchCoursewareDetail()`
    - 加载教学环节列表 → `fetchTeachPhases()`
    - 初始化播放器容器

2. **编辑阶段**

    - 选择环节 → 触发 `handleClickMenuCell()`
    - 加载页面详情 → `fetchTeachPageDetail()`
    - 渲染编辑界面 → 动态加载模板组件
    - 数据变更 → 触发 `onValueChange()`
    - 实时预览 → 更新播放器内容

3. **保存阶段**

    - 收集表单数据 → `getAllData()`
    - 数据验证 → 检查必填项和格式
    - 提交保存 → `editTeachPageDetail()`
    - 状态更新 → 更新环节状态

4. **发布阶段**
    - 全量数据检查
    - 发布请求 → `publishCourseware()`
    - 版本更新

## 4. 项目依赖关系分析

### 4.1 核心依赖组件

#### 4.1.1 UI 组件库依赖

```typescript
// Element Plus 组件使用清单
-el -
    upload(文件上传) -
    el -
    form / el -
    form -
    item(表单) -
    el -
    input / el -
    textarea(输入框) -
    el -
    button(按钮) -
    el -
    tabs / el -
    tab -
    pane(标签页) -
    el -
    tree(树形控件) -
    el -
    dialog(对话框) -
    el -
    message -
    box(消息框) -
    el -
    notification(通知) -
    el -
    loading(加载状态);
```

#### 4.1.2 项目内部组件依赖

```typescript
// 需要迁移的内部组件
- NaviBar (导航栏) - 来自 /@/components/NaviBar/
- ImageOss (图片上传) - 来自 /@/components/Upload/ImageOss.vue
- FileOss (文件上传) - 来自 /@/components/Upload/FileOss.vue
- Editor (富文本编辑器) - 来自 /@/components/Editor/
- CodeEditor (代码编辑器) - 来自 /@/components/CodeEditor/
- YdAutocomplete (自动完成) - 来自 /@/components/YdAutocomplete/
```

#### 4.1.3 工具函数依赖

```typescript
// Hooks 依赖
- useMessage() - 来自 /@/hooks/message
- useDict() - 来自 /@/hooks/dict
- useMessageBox() - 来自 /@/hooks/messageBox

// 工具函数依赖
- request() - 来自 /@/utils/request
- Session - 来自 /@/utils/storage
- other - 来自 /@/utils/other
```

#### 4.1.4 状态管理依赖

```typescript
// Stores 依赖
- useTagsViewRoutes - 来自 /@/stores/tagsViewRoutes
```

### 4.2 API 接口依赖

#### 4.2.1 课件相关 API

```typescript
// 来自 /@/api/integraEdu/courseware.ts
-fetchWareBookNameList() -
    获取课件书名列表 -
    fetchCoursewareList() -
    获取课件列表 -
    fetchCoursewareDetail() -
    获取课件详情 -
    fetchTeachPageDetail() -
    获取教学页详情 -
    editTeachPageDetail() -
    修改教学页详情 -
    publishCourseware() -
    发布课件 -
    fetchTeachPhases() -
    获取教学环节列表 -
    addTeachPhase() -
    添加教学环节 -
    editTeachPhase() -
    编辑教学环节 -
    deleteTeachPhase() -
    删除教学环节 -
    sortTeachPhase() -
    排序教学环节 -
    addTeachPhaseFromTemplate() -
    从模板添加环节 -
    modifyWareMaterielStatus() -
    修改资料状态;
```

#### 4.2.2 通用 API

```typescript
// 来自 /@/api/common/
-getUpdateSign() - 获取上传签名;
```

#### 4.2.3 其他业务 API

```typescript
// 来自 /@/api/eduConnect/
-getPictureBookList() - 获取绘本列表(audioPictureBook);
```

### 4.3 第三方库依赖

#### 4.3.1 播放器相关

```typescript
- courseware-player - 课件播放器核心
- SlidePlayer - 幻灯片播放器
- aliyun-aliplayer - 阿里云播放器
- vue3-video-play - Vue3 视频播放组件
```

#### 4.3.2 编辑器相关

```typescript
- @wangeditor/editor - 富文本编辑器
- @wangeditor/editor-for-vue - Vue适配器
- codemirror - 代码编辑器
- json-editor-vue3 - JSON编辑器
```

#### 4.3.3 文件处理相关

```typescript
-ali - oss - 阿里云OSS - aliyun - upload - vod - 阿里云视频上传 - qrcode - 二维码生成 - viewerjs - 图片查看器;
```

#### 4.3.4 交互相关

```typescript
-vue - draggable - plus - 拖拽功能 - vuedraggable - 拖拽组件 - sortablejs - 排序功能 - screenfull - 全屏功能;
```

#### 4.3.5 工具库

```typescript
-lodash -
    工具函数库 -
    moment -
    时间处理 -
    crypto -
    js -
    加密解密 -
    js -
    cookie -
    Cookie操作 -
    js -
    base64 -
    Base64编码 -
    qs -
    查询字符串处理;
```

## 5. 实施步骤

### 5.1 准备阶段 (1-2 天)

#### 5.1.1 环境搭建

-   [ ] 创建新的 NPM 项目目录
-   [ ] 初始化 package.json
-   [ ] 配置 TypeScript 环境
-   [ ] 配置 Vite 构建工具
-   [ ] 设置 ESLint 和 Prettier
-   [ ] 配置 Git 仓库

#### 5.1.2 项目结构设计

-   [ ] 设计目录结构
-   [ ] 定义组件接口规范
-   [ ] 设计 API 适配器接口
-   [ ] 制定编码规范

### 5.2 核心组件迁移 (3-5 天)

#### 5.2.1 基础组件迁移

-   [ ] 迁移 `CoursewareEditor.vue` (主编辑器)
-   [ ] 迁移 `MenuView.vue` (菜单组件)
-   [ ] 迁移 `DataView.vue` (数据编辑组件)
-   [ ] 迁移 `MenuCell.vue` (菜单项组件)
-   [ ] 迁移 `FullscreenPreview.vue` (预览组件)

#### 5.2.2 依赖组件处理

-   [ ] 抽取并迁移 `NaviBar` 组件
-   [ ] 抽取并迁移 `ImageOss` 上传组件
-   [ ] 抽取并迁移 `FileOss` 上传组件
-   [ ] 抽取并迁移 `Editor` 富文本编辑器
-   [ ] 抽取并迁移 `CodeEditor` 代码编辑器
-   [ ] 抽取并迁移 `YdAutocomplete` 自动完成组件

### 5.3 模板系统迁移 (2-3 天)

#### 5.3.1 模板组件迁移

-   [ ] 迁移所有模板组件 (`components/templates/`)
-   [ ] 迁移页面工具组件 (`components/pageUtils/`)
-   [ ] 迁移通用组件 (`components/common/`)

#### 5.3.2 模板系统重构

-   [ ] 实现动态模板加载器
-   [ ] 实现模板注册机制
-   [ ] 实现模板配置系统
-   [ ] 实现模板验证机制

### 5.4 API 适配层开发 (2-3 天)

#### 5.4.1 接口定义

-   [ ] 定义 `CoursewareAPI` 接口
-   [ ] 定义数据类型接口
-   [ ] 定义错误处理接口
-   [ ] 定义配置接口

#### 5.4.2 适配器实现

-   [ ] 实现 REST API 适配器
-   [ ] 实现 GraphQL API 适配器 (可选)
-   [ ] 实现 Mock API 适配器 (用于测试)
-   [ ] 实现适配器工厂

### 5.5 工具函数迁移 (1-2 天)

#### 5.5.1 Hooks 迁移

-   [ ] 迁移 `useMessage` Hook
-   [ ] 迁移 `useDict` Hook
-   [ ] 迁移 `useMessageBox` Hook
-   [ ] 实现 `useApi` Hook
-   [ ] 实现 `usePlayer` Hook

#### 5.5.2 工具函数迁移

-   [ ] 迁移请求工具函数
-   [ ] 迁移存储工具函数
-   [ ] 迁移其他工具函数

### 5.6 状态管理实现 (1-2 天)

#### 5.6.1 状态设计

-   [ ] 设计编辑器状态结构
-   [ ] 设计数据流转机制
-   [ ] 设计错误处理机制

#### 5.6.2 状态管理实现

-   [ ] 实现 `useEditorState` Composable
-   [ ] 实现 `useTemplateState` Composable
-   [ ] 实现 `useValidation` Composable

### 5.7 构建配置 (1 天)

#### 5.7.1 Vite 配置

-   [ ] 配置库模式构建
-   [ ] 配置外部依赖排除
-   [ ] 配置样式处理
-   [ ] 配置 TypeScript 声明文件生成

#### 5.7.2 打包优化

-   [ ] 配置代码分割
-   [ ] 配置 Tree Shaking
-   [ ] 配置压缩优化
-   [ ] 配置 Source Map

### 5.8 测试开发 (2-3 天)

#### 5.8.1 单元测试

-   [ ] 编写组件单元测试
-   [ ] 编写工具函数测试
-   [ ] 编写 API 适配器测试

#### 5.8.2 集成测试

-   [ ] 编写组件集成测试
-   [ ] 编写数据流测试
-   [ ] 编写端到端测试

#### 5.8.3 测试环境

-   [ ] 搭建测试应用
-   [ ] 配置 Mock 数据
-   [ ] 配置测试用例

### 5.9 文档编写 (1-2 天)

#### 5.9.1 API 文档

-   [ ] 编写组件 API 文档
-   [ ] 编写接口文档
-   [ ] 编写配置文档

#### 5.9.2 使用文档

-   [ ] 编写快速开始指南
-   [ ] 编写详细使用教程
-   [ ] 编写最佳实践指南
-   [ ] 编写故障排除指南

### 5.10 发布准备 (1 天)

#### 5.10.1 版本管理

-   [ ] 配置语义化版本
-   [ ] 编写 CHANGELOG
-   [ ] 配置发布脚本

#### 5.10.2 NPM 发布

-   [ ] 配置 NPM 账号
-   [ ] 配置发布流程
-   [ ] 执行首次发布

## 6. 实施后检查清单

### 6.1 功能完整性检查

#### 6.1.1 核心功能

-   [ ] ✅ 课件编辑器正常启动
-   [ ] ✅ 左侧菜单树正常显示
-   [ ] ✅ 教学环节增删改查功能正常
-   [ ] ✅ 拖拽排序功能正常
-   [ ] ✅ 右侧编辑区域正常显示
-   [ ] ✅ 标签页切换功能正常
-   [ ] ✅ 模板动态加载功能正常
-   [ ] ✅ 数据编辑功能正常
-   [ ] ✅ 实时预览功能正常
-   [ ] ✅ 全屏预览功能正常
-   [ ] ✅ 数据保存功能正常
-   [ ] ✅ 数据验证功能正常

#### 6.1.2 模板系统

-   [ ] ✅ 所有模板组件正常加载
-   [ ] ✅ 模板切换功能正常
-   [ ] ✅ 模板配置功能正常
-   [ ] ✅ 模板数据绑定正常
-   [ ] ✅ 模板验证功能正常

#### 6.1.3 文件上传

-   [ ] ✅ 图片上传功能正常
-   [ ] ✅ 文件上传功能正常
-   [ ] ✅ 上传进度显示正常
-   [ ] ✅ 上传错误处理正常
-   [ ] ✅ 文件预览功能正常

#### 6.1.4 播放器功能

-   [ ] ✅ 播放器初始化正常
-   [ ] ✅ 播放器内容更新正常
-   [ ] ✅ 全屏播放功能正常
-   [ ] ✅ 播放器控制功能正常

### 6.2 兼容性检查

#### 6.2.1 浏览器兼容性

-   [ ] ✅ Chrome 最新版本兼容
-   [ ] ✅ Firefox 最新版本兼容
-   [ ] ✅ Safari 最新版本兼容
-   [ ] ✅ Edge 最新版本兼容

#### 6.2.2 Vue 版本兼容性

-   [ ] ✅ Vue 3.3+ 兼容
-   [ ] ✅ Composition API 正常工作
-   [ ] ✅ TypeScript 支持正常

#### 6.2.3 Element Plus 兼容性

-   [ ] ✅ Element Plus 2.4+ 兼容
-   [ ] ✅ 主题样式正常
-   [ ] ✅ 组件功能正常

### 6.3 性能检查

#### 6.3.1 加载性能

-   [ ] ✅ 初始加载时间 < 3 秒
-   [ ] ✅ 模板切换时间 < 1 秒
-   [ ] ✅ 数据保存时间 < 2 秒
-   [ ] ✅ 预览更新时间 < 1 秒

#### 6.3.2 内存性能

-   [ ] ✅ 内存使用合理 (< 100MB)
-   [ ] ✅ 无明显内存泄漏
-   [ ] ✅ 组件销毁正常

#### 6.3.3 打包性能

-   [ ] ✅ 打包体积合理 (< 2MB)
-   [ ] ✅ 代码分割正常
-   [ ] ✅ Tree Shaking 生效

### 6.4 API 适配检查

#### 6.4.1 接口适配

-   [ ] ✅ 所有 API 接口正常调用
-   [ ] ✅ 错误处理机制正常
-   [ ] ✅ 数据格式转换正常
-   [ ] ✅ 请求重试机制正常

#### 6.4.2 数据流检查

-   [ ] ✅ 数据流转正常
-   [ ] ✅ 状态同步正常
-   [ ] ✅ 事件传递正常
-   [ ] ✅ 副作用处理正常

### 6.5 代码质量检查

#### 6.5.1 代码规范

-   [ ] ✅ ESLint 检查通过
-   [ ] ✅ Prettier 格式化正常
-   [ ] ✅ TypeScript 类型检查通过
-   [ ] ✅ 代码注释完整

### 6.6 文档完整性检查

#### 6.6.1 API 文档

-   [ ] ✅ 组件 Props 文档完整
-   [ ] ✅ 组件 Events 文档完整
-   [ ] ✅ 组件 Slots 文档完整
-   [ ] ✅ 组件 Methods 文档完整

#### 6.6.2 使用文档

-   [ ] ✅ 安装指南完整
-   [ ] ✅ 快速开始指南完整
-   [ ] ✅ 配置说明完整
-   [ ] ✅ 示例代码完整
-   [ ] ✅ 故障排除指南完整

### 6.7 发布检查

#### 6.7.1 包管理

-   [ ] ✅ package.json 配置正确
-   [ ] ✅ 依赖版本配置正确
-   [ ] ✅ 入口文件配置正确
-   [ ] ✅ 类型声明文件正确

#### 6.7.2 版本管理

-   [ ] ✅ 版本号符合语义化规范
-   [ ] ✅ CHANGELOG 更新完整
-   [ ] ✅ Git 标签创建正确
-   [ ] ✅ 发布说明完整

#### 6.7.3 NPM 发布

-   [ ] ✅ NPM 包发布成功
-   [ ] ✅ 包信息显示正确
-   [ ] ✅ 下载安装正常
-   [ ] ✅ 使用示例正常

### 6.8 集成测试检查

#### 6.8.1 目标项目集成

-   [ ] ✅ 在目标项目中安装成功
-   [ ] ✅ 在目标项目中导入成功
-   [ ] ✅ 在目标项目中使用正常
-   [ ] ✅ API 适配器配置正常

#### 6.8.2 功能验证

-   [ ] ✅ 所有核心功能在目标项目中正常
-   [ ] ✅ 数据保存在目标项目中正常
-   [ ] ✅ 文件上传在目标项目中正常
-   [ ] ✅ 预览功能在目标项目中正常

## 7. 风险控制

### 7.1 技术风险

-   **依赖版本冲突**：使用 peerDependencies 避免版本冲突
-   **API 不兼容**：提供多种 API 适配器
-   **性能问题**：进行性能测试和优化
-   **浏览器兼容性**：进行跨浏览器测试

### 7.2 业务风险

-   **功能缺失**：详细的功能对比测试
-   **数据丢失**：完善的数据验证和备份机制
-   **用户体验下降**：保持原有的交互体验

### 7.3 项目风险

-   **进度延期**：合理的时间规划和里程碑控制
-   **资源不足**：提前准备必要的开发资源
-   **沟通问题**：建立清晰的沟通机制

## 8. 后续维护

### 8.1 版本管理

-   采用语义化版本控制
-   定期发布更新版本
-   维护向后兼容性

### 8.2 问题反馈

-   建立 Issue 跟踪系统
-   提供技术支持渠道
-   定期收集用户反馈

### 8.3 功能扩展

-   支持插件机制
-   支持自定义模板
-   支持多语言国际化

---

**文档版本**: 1.0  
**创建时间**: 2025-08-04  
**最后更新**: 2024-08-04  
**负责人**: FisherX
