// Composables 入口文件
import type { InjectionKey } from 'vue'
import type { CoursewareAPI, MessageAdapter } from '../types'

// 依赖注入 Keys
export const APIAdapterKey: InjectionKey<CoursewareAPI> = Symbol('api-adapter')
export const MessageAdapterKey: InjectionKey<MessageAdapter> = Symbol('message-adapter')

// 导出 composables（暂时注释掉未实现的）
// export { useEditorState } from './useEditorState'
// export { useTemplateState } from './useTemplateState'
// export { useValidation } from './useValidation'
// export { useApi } from './useApi'
// export { usePlayer } from './usePlayer'
// export { useMessage } from './useMessage'
