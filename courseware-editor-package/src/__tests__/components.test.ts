import { describe, it, expect } from 'vitest'
import { mount } from '@vue/test-utils'
import { ElButton } from 'element-plus'

// 导入要测试的组件
import CoursewareEditorCore from '../components/CoursewareEditorCore.vue'
import MenuView from '../components/MenuView.vue'
import DataView from '../components/DataView.vue'
import MenuCell from '../components/MenuCell.vue'

// Mock API适配器
import { MockAPIAdapter } from '../adapters'

describe('课件编辑器核心组件测试', () => {
  const mockApiAdapter = new MockAPIAdapter({
    baseURL: 'http://localhost:3000',
    headers: {},
    timeout: 5000
  })

  it('CoursewareEditorCore 组件应该正确渲染', () => {
    const wrapper = mount(CoursewareEditorCore, {
      props: {
        coursewareId: 'test-courseware-id',
        api: mockApiAdapter
      },
      global: {
        components: {
          ElButton
        }
      }
    })

    expect(wrapper.exists()).toBe(true)
    expect(wrapper.find('.courseware-editor').exists()).toBe(true)
  })

  it('MenuView 组件应该正确渲染', () => {
    const mockPhases = [
      {
        id: 'phase-1',
        stepName: '导入环节',
        templateId: 1,
        templateName: '文本模板',
        sort: 1,
        status: 'unedited',
        pageCount: 1
      }
    ]

    const wrapper = mount(MenuView, {
      props: {
        phases: mockPhases,
        coursewareId: 'test-courseware-id'
      },
      global: {
        components: {
          ElButton
        }
      }
    })

    expect(wrapper.exists()).toBe(true)
    expect(wrapper.findAll('.menu-item')).toHaveLength(1)
  })

  it('DataView 组件应该正确渲染', () => {
    const mockPhaseData = {
      id: 'phase-1',
      stepName: '导入环节',
      templateId: 1,
      templateName: '文本模板',
      sort: 1,
      status: 'unedited',
      pageCount: 1
    }

    const wrapper = mount(DataView, {
      props: {
        phaseData: mockPhaseData,
        pageData: null,
        templateConfig: {
          coursewareId: 'test-courseware-id',
          materielId: 'test-material-id',
          stepId: 'test-step-id',
          templateId: 1
        }
      },
      global: {
        components: {
          ElButton
        }
      }
    })

    expect(wrapper.exists()).toBe(true)
    expect(wrapper.find('.data-view').exists()).toBe(true)
  })

  it('MenuCell 组件应该正确渲染', () => {
    const mockPhase = {
      id: 'phase-1',
      stepName: '导入环节',
      templateId: 1,
      templateName: '文本模板',
      sort: 1,
      status: 'unedited',
      pageCount: 1
    }

    const wrapper = mount(MenuCell, {
      props: {
        phase: mockPhase,
        isActive: false
      },
      global: {
        components: {
          ElButton
        }
      }
    })

    expect(wrapper.exists()).toBe(true)
    expect(wrapper.text()).toContain('导入环节')
  })
})

describe('API适配器测试', () => {
  it('MockAPIAdapter 应该正确工作', async () => {
    const adapter = new MockAPIAdapter({
      baseURL: 'http://localhost:3000',
      headers: {},
      timeout: 5000
    })

    // 测试获取课件详情
    const coursewareDetail = await adapter.fetchCoursewareDetail('test-id')
    expect(coursewareDetail).toHaveProperty('id', 'test-id')
    expect(coursewareDetail).toHaveProperty('name', 'Mock课件')

    // 测试获取教学环节
    const phases = await adapter.fetchTeachPhases('test-courseware-id')
    expect(Array.isArray(phases)).toBe(true)
    expect(phases).toHaveLength(1)
    expect(phases[0]).toHaveProperty('stepName', '导入环节')

    // 测试获取页面详情
    const pageDetail = await adapter.fetchTeachPageDetail('test-step-id')
    expect(pageDetail).toHaveProperty('stepId', 'test-step-id')
    expect(pageDetail).toHaveProperty('stepName', 'Mock环节')
  })
})
