import { config } from '@vue/test-utils'
import { vi } from 'vitest'

// Mock Element Plus
vi.mock('element-plus', () => ({
  ElButton: { name: 'ElButton', template: '<button><slot /></button>' },
  ElInput: { name: 'ElInput', template: '<input />' },
  ElDialog: { name: 'ElDialog', template: '<div><slot /></div>' },
  ElMessage: {
    success: vi.fn(),
    error: vi.fn(),
    warning: vi.fn(),
    info: vi.fn()
  },
  ElMessageBox: {
    confirm: vi.fn(),
    alert: vi.fn(),
    prompt: vi.fn()
  },
  ElNotification: {
    success: vi.fn(),
    error: vi.fn(),
    warning: vi.fn(),
    info: vi.fn()
  }
}))

// Mock Element Plus Icons
vi.mock('@element-plus/icons-vue', () => ({
  View: { name: 'View', template: '<i class="el-icon-view"></i>' },
  Edit: { name: 'Edit', template: '<i class="el-icon-edit"></i>' },
  Delete: { name: 'Delete', template: '<i class="el-icon-delete"></i>' },
  Plus: { name: 'Plus', template: '<i class="el-icon-plus"></i>' },
  Picture: { name: 'Picture', template: '<i class="el-icon-picture"></i>' },
  VideoPlay: { name: 'VideoPlay', template: '<i class="el-icon-video-play"></i>' }
}))

// 全局配置
config.global.stubs = {
  transition: false,
  'transition-group': false
}

// Mock window对象
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(),
    removeListener: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn()
  }))
})

// Mock ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn()
}))

// Mock IntersectionObserver
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn()
}))