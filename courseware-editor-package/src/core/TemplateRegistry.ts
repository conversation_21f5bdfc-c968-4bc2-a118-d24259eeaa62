import type { TemplateData, TemplateRegistry } from '../types/template'

export class DefaultTemplateRegistry implements TemplateRegistry {
    private templates = new Map<string, TemplateData>()

    register(template: TemplateData): void {
        this.templates.set(template.config.id, template)
    }

    unregister(templateId: string): void {
        this.templates.delete(templateId)
    }

    get(templateId: string): TemplateData | undefined {
        return this.templates.get(templateId)
    }

    getAll(): TemplateData[] {
        return Array.from(this.templates.values())
    }

    getByCategory(category: string): TemplateData[] {
        return this.getAll().filter(template => template.config.category === category)
    }

    getByType(type: string): TemplateData[] {
        return this.getAll().filter(template => template.config.type === type)
    }

    // 批量注册模板
    registerBatch(templates: TemplateData[]): void {
        templates.forEach(template => this.register(template))
    }

    // 清空所有模板
    clear(): void {
        this.templates.clear()
    }

    // 检查模板是否存在
    has(templateId: string): boolean {
        return this.templates.has(templateId)
    }

    // 获取模板数量
    size(): number {
        return this.templates.size
    }
}

// 全局模板注册器实例
export const templateRegistry = new DefaultTemplateRegistry()