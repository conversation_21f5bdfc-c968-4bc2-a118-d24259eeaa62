import { defineAsyncComponent, type Component } from 'vue'
import type { TemplateRenderer, TemplateSchema } from '../types/template'
import { templateRegistry } from './TemplateRegistry'
import Ajv from 'ajv'

export class DefaultTemplateRenderer implements TemplateRenderer {
    private ajv = new Ajv()
    private componentCache = new Map<string, Component>()

    async render(templateId: string, data: Record<string, any>): Promise<Component | null> {
        const template = templateRegistry.get(templateId)
        if (!template) {
            console.warn(`Template ${templateId} not found`)
            return null
        }

        // 验证数据
        const isValid = await this.validate(templateId, data)
        if (!isValid) {
            console.warn(`Invalid data for template ${templateId}`)
            return null
        }

        // 从缓存获取组件
        if (this.componentCache.has(templateId)) {
            return this.componentCache.get(templateId)!
        }

        // 动态加载模板组件
        const component = await this.loadTemplateComponent(templateId)
        if (component) {
            this.componentCache.set(templateId, component)
        }

        return component
    }

    async validate(templateId: string, data: Record<string, any>): Promise<boolean> {
        const schema = this.getSchema(templateId)
        if (!schema) return true

        const validate = this.ajv.compile(schema)
        return validate(data)
    }

    getSchema(templateId: string): TemplateSchema | undefined {
        const template = templateRegistry.get(templateId)
        return template?.schema
    }

    private async loadTemplateComponent(templateId: string): Promise<Component | null> {
        try {
            // 动态导入模板组件
            const module = await import(`../components/templates/${templateId}.vue`)
            return module.default
        } catch (error) {
            // 如果找不到对应的Vue文件，尝试使用通用渲染器
            console.warn(`Template component ${templateId}.vue not found, using generic renderer`)
            return this.createGenericComponent(templateId)
        }
    }

    private createGenericComponent(templateId: string): Component {
        const template = templateRegistry.get(templateId)
        if (!template) return null

        return defineAsyncComponent(async () => {
            const { createApp, h } = await import('vue')
            
            return {
                name: `Template_${templateId}`,
                props: {
                    data: {
                        type: Object,
                        default: () => ({})
                    }
                },
                setup(props) {
                    return () => this.renderComponents(template.components, props.data)
                }
            }
        })
    }

    private renderComponents(components: any[], data: Record<string, any>): any {
        return components.map(component => {
            const { type, props, children, style } = component
            
            // 处理props中的数据绑定
            const processedProps = this.processProps(props, data)
            
            // 处理样式
            const processedStyle = this.processStyle(style, data)
            
            // 递归渲染子组件
            const childNodes = children ? this.renderComponents(children, data) : []
            
            return h(type, {
                ...processedProps,
                style: processedStyle
            }, childNodes)
        })
    }

    private processProps(props: Record<string, any>, data: Record<string, any>): Record<string, any> {
        const processed = { ...props }
        
        Object.keys(processed).forEach(key => {
            const value = processed[key]
            if (typeof value === 'string' && value.startsWith('{{') && value.endsWith('}}')) {
                // 简单的模板变量替换
                const varName = value.slice(2, -2).trim()
                processed[key] = data[varName] || value
            }
        })
        
        return processed
    }

    private processStyle(style: Record<string, any>, data: Record<string, any>): Record<string, any> {
        if (!style) return {}
        
        const processed = { ...style }
        
        Object.keys(processed).forEach(key => {
            const value = processed[key]
            if (typeof value === 'string' && value.startsWith('{{') && value.endsWith('}}')) {
                const varName = value.slice(2, -2).trim()
                processed[key] = data[varName] || value
            }
        })
        
        return processed
    }

    // 清空组件缓存
    clearCache(): void {
        this.componentCache.clear()
    }

    // 预加载模板组件
    async preloadTemplate(templateId: string): Promise<void> {
        if (!this.componentCache.has(templateId)) {
            const component = await this.loadTemplateComponent(templateId)
            if (component) {
                this.componentCache.set(templateId, component)
            }
        }
    }
}

// 全局模板渲染器实例
export const templateRenderer = new DefaultTemplateRenderer()