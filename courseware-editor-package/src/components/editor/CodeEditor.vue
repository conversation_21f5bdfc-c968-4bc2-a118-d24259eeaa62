<template>
    <div class="code-editor" ref="editorRef"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { EditorView, basicSetup } from 'codemirror'
import { EditorState } from '@codemirror/state'
import { javascript } from '@codemirror/lang-javascript'
import { python } from '@codemirror/lang-python'
import { html } from '@codemirror/lang-html'
import { css } from '@codemirror/lang-css'
import { json } from '@codemirror/lang-json'
import { xml } from '@codemirror/lang-xml'
import { oneDark } from '@codemirror/theme-one-dark'

// 语言映射
const languageMap = {
    javascript: javascript(),
    typescript: javascript({ typescript: true }),
    python: python(),
    html: html(),
    css: css(),
    json: json(),
    xml: xml()
}

// Props
const props = withDefaults(defineProps<{
    modelValue?: string
    language?: keyof typeof languageMap
    theme?: 'light' | 'dark'
    height?: string | number
    readonly?: boolean
    lineNumbers?: boolean
    foldGutter?: boolean
    placeholder?: string
}>(), {
    modelValue: '',
    language: 'javascript',
    theme: 'light',
    height: '300px',
    readonly: false,
    lineNumbers: true,
    foldGutter: true,
    placeholder: '请输入代码...'
})

// Events
const emit = defineEmits<{
    'update:modelValue': [value: string]
    'change': [value: string]
    'focus': []
    'blur': []
}>()

// Refs
const editorRef = ref<HTMLElement>()
const editorView = ref<EditorView>()

// 创建编辑器状态
const createEditorState = (content: string) => {
    const extensions = [
        basicSetup,
        languageMap[props.language] || languageMap.javascript,
        EditorView.updateListener.of((update) => {
            if (update.docChanged) {
                const value = update.state.doc.toString()
                emit('update:modelValue', value)
                emit('change', value)
            }
        }),
        EditorView.focusChangeEffect.of((state, focusing) => {
            if (focusing) {
                emit('focus')
            } else {
                emit('blur')
            }
        })
    ]

    // 添加主题
    if (props.theme === 'dark') {
        extensions.push(oneDark)
    }

    // 只读模式
    if (props.readonly) {
        extensions.push(EditorState.readOnly.of(true))
    }

    return EditorState.create({
        doc: content,
        extensions
    })
}

// 初始化编辑器
const initEditor = async () => {
    await nextTick()
    
    if (!editorRef.value) return

    const state = createEditorState(props.modelValue)
    
    editorView.value = new EditorView({
        state,
        parent: editorRef.value
    })
}

// 销毁编辑器
const destroyEditor = () => {
    if (editorView.value) {
        editorView.value.destroy()
        editorView.value = undefined
    }
}

// 重新创建编辑器（当语言或主题改变时）
const recreateEditor = async () => {
    const currentValue = editorView.value?.state.doc.toString() || props.modelValue
    destroyEditor()
    await nextTick()
    const state = createEditorState(currentValue)
    
    if (editorRef.value) {
        editorView.value = new EditorView({
            state,
            parent: editorRef.value
        })
    }
}

// 监听内容变化
watch(() => props.modelValue, (newValue) => {
    if (editorView.value && newValue !== editorView.value.state.doc.toString()) {
        const transaction = editorView.value.state.update({
            changes: {
                from: 0,
                to: editorView.value.state.doc.length,
                insert: newValue
            }
        })
        editorView.value.dispatch(transaction)
    }
})

// 监听语言变化
watch(() => props.language, () => {
    recreateEditor()
})

// 监听主题变化
watch(() => props.theme, () => {
    recreateEditor()
})

// 监听只读状态变化
watch(() => props.readonly, () => {
    recreateEditor()
})

// 生命周期
onMounted(() => {
    initEditor()
})

onUnmounted(() => {
    destroyEditor()
})

// 暴露方法
defineExpose({
    getEditor: () => editorView.value,
    getValue: () => editorView.value?.state.doc.toString() || '',
    setValue: (value: string) => {
        if (editorView.value) {
            const transaction = editorView.value.state.update({
                changes: {
                    from: 0,
                    to: editorView.value.state.doc.length,
                    insert: value
                }
            })
            editorView.value.dispatch(transaction)
        }
    },
    focus: () => editorView.value?.focus(),
    insertText: (text: string) => {
        if (editorView.value) {
            const selection = editorView.value.state.selection.main
            const transaction = editorView.value.state.update({
                changes: {
                    from: selection.from,
                    to: selection.to,
                    insert: text
                }
            })
            editorView.value.dispatch(transaction)
        }
    }
})
</script>

<style scoped>
.code-editor {
    height: v-bind(height);
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    overflow: hidden;
}

:deep(.cm-editor) {
    height: 100%;
}

:deep(.cm-scroller) {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 14px;
    line-height: 1.5;
}

:deep(.cm-focused) {
    outline: none;
}

:deep(.cm-editor.cm-focused) {
    border-color: #409eff;
}
</style>