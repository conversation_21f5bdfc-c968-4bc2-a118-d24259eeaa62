<template>
    <div class="editor-container">
        <div class="editor-toolbar" ref="toolbarRef"></div>
        <div class="editor-content" ref="editorRef"></div>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue'
import { createEditor, createToolbar } from '@wangeditor/editor'
import type { IDomEditor, IEditorConfig, IToolbarConfig } from '@wangeditor/editor'

// Props
const props = withDefaults(defineProps<{
    modelValue?: string
    placeholder?: string
    mode?: 'default' | 'simple'
    height?: string | number
    disabled?: boolean
    config?: Partial<IEditorConfig>
}>(), {
    modelValue: '',
    placeholder: '请输入内容...',
    mode: 'default',
    height: '300px',
    disabled: false
})

// Events
const emit = defineEmits<{
    'update:modelValue': [value: string]
    'change': [value: string]
    'focus': [editor: IDomEditor]
    'blur': [editor: IDomEditor]
}>()

// Refs
const toolbarRef = ref()
const editorRef = ref()
const editor = ref<IDomEditor>()
const toolbar = ref()

// 编辑器配置
const editorConfig: Partial<IEditorConfig> = {
    placeholder: props.placeholder,
    readOnly: props.disabled,
    ...props.config,
    onCreated(editor: IDomEditor) {
        // 编辑器创建完成
        props.config?.onCreated?.(editor)
    },
    onChange(editor: IDomEditor) {
        const html = editor.getHtml()
        emit('update:modelValue', html)
        emit('change', html)
        props.config?.onChange?.(editor)
    },
    onFocus(editor: IDomEditor) {
        emit('focus', editor)
        props.config?.onFocus?.(editor)
    },
    onBlur(editor: IDomEditor) {
        emit('blur', editor)
        props.config?.onBlur?.(editor)
    }
}

// 工具栏配置
const toolbarConfig: Partial<IToolbarConfig> = {
    excludeKeys: props.mode === 'simple' ? [
        'group-video',
        'fullScreen',
        'insertTable',
        'codeBlock'
    ] : []
}

// 初始化编辑器
const initEditor = () => {
    if (!editorRef.value || !toolbarRef.value) return

    // 创建编辑器
    editor.value = createEditor({
        selector: editorRef.value,
        config: editorConfig,
        mode: 'default'
    })

    // 创建工具栏
    toolbar.value = createToolbar({
        editor: editor.value,
        selector: toolbarRef.value,
        config: toolbarConfig,
        mode: 'default'
    })

    // 设置初始内容
    if (props.modelValue) {
        editor.value.setHtml(props.modelValue)
    }
}

// 销毁编辑器
const destroyEditor = () => {
    if (editor.value) {
        editor.value.destroy()
        editor.value = undefined
    }
    if (toolbar.value) {
        toolbar.value.destroy()
        toolbar.value = undefined
    }
}

// 监听内容变化
watch(() => props.modelValue, (newValue) => {
    if (editor.value && newValue !== editor.value.getHtml()) {
        editor.value.setHtml(newValue || '')
    }
})

// 监听禁用状态
watch(() => props.disabled, (disabled) => {
    if (editor.value) {
        if (disabled) {
            editor.value.disable()
        } else {
            editor.value.enable()
        }
    }
})

// 生命周期
onMounted(() => {
    initEditor()
})

onUnmounted(() => {
    destroyEditor()
})

// 暴露方法
defineExpose({
    getEditor: () => editor.value,
    getHtml: () => editor.value?.getHtml() || '',
    getText: () => editor.value?.getText() || '',
    setHtml: (html: string) => editor.value?.setHtml(html),
    insertText: (text: string) => editor.value?.insertText(text),
    focus: () => editor.value?.focus(),
    blur: () => editor.value?.blur()
})
</script>

<style scoped>
.editor-container {
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    overflow: hidden;
}

.editor-toolbar {
    border-bottom: 1px solid #e4e7ed;
}

.editor-content {
    height: v-bind(height);
    overflow-y: auto;
}

:deep(.w-e-text-container) {
    background-color: #fff;
}

:deep(.w-e-text-placeholder) {
    color: #c0c4cc;
}

:deep(.w-e-toolbar) {
    background-color: #fafafa;
}

:deep(.w-e-toolbar .w-e-bar-item button) {
    color: #606266;
}

:deep(.w-e-toolbar .w-e-bar-item button:hover) {
    color: #409eff;
    background-color: #ecf5ff;
}
</style>