<template>
    <el-autocomplete
        v-model="inputValue"
        :fetch-suggestions="fetchSuggestions"
        :placeholder="placeholder"
        :disabled="disabled"
        :clearable="clearable"
        :trigger-on-focus="triggerOnFocus"
        :select-when-unmatched="selectWhenUnmatched"
        :highlight-first-item="highlightFirstItem"
        :fit-input-width="fitInputWidth"
        :debounce="debounce"
        @select="handleSelect"
        @change="handleChange"
        @input="handleInput"
        @focus="handleFocus"
        @blur="handleBlur"
        @clear="handleClear"
    >
        <template #default="{ item }">
            <slot name="suggestion" :item="item">
                <div class="suggestion-item">
                    <div class="suggestion-label">{{ item.label || item.value }}</div>
                    <div v-if="item.description" class="suggestion-description">
                        {{ item.description }}
                    </div>
                </div>
            </slot>
        </template>

        <template #prefix v-if="$slots.prefix">
            <slot name="prefix"></slot>
        </template>

        <template #suffix v-if="$slots.suffix">
            <slot name="suffix"></slot>
        </template>

        <template #prepend v-if="$slots.prepend">
            <slot name="prepend"></slot>
        </template>

        <template #append v-if="$slots.append">
            <slot name="append"></slot>
        </template>
    </el-autocomplete>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'

// 建议项接口
interface SuggestionItem {
    value: string
    label?: string
    description?: string
    [key: string]: any
}

// Props
const props = withDefaults(defineProps<{
    modelValue?: string
    placeholder?: string
    disabled?: boolean
    clearable?: boolean
    triggerOnFocus?: boolean
    selectWhenUnmatched?: boolean
    highlightFirstItem?: boolean
    fitInputWidth?: boolean
    debounce?: number
    dataSource?: SuggestionItem[] | ((query: string) => Promise<SuggestionItem[]>)
    filterMethod?: (query: string, item: SuggestionItem) => boolean
    remote?: boolean
    remoteMethod?: (query: string) => Promise<SuggestionItem[]>
}>(), {
    modelValue: '',
    placeholder: '请输入',
    disabled: false,
    clearable: true,
    triggerOnFocus: true,
    selectWhenUnmatched: false,
    highlightFirstItem: true,
    fitInputWidth: true,
    debounce: 300,
    remote: false
})

// Events
const emit = defineEmits<{
    'update:modelValue': [value: string]
    'select': [item: SuggestionItem]
    'change': [value: string]
    'input': [value: string]
    'focus': [event: FocusEvent]
    'blur': [event: FocusEvent]
    'clear': []
}>()

// 状态
const inputValue = ref(props.modelValue)
const suggestions = ref<SuggestionItem[]>([])

// 计算属性
const computedValue = computed({
    get: () => inputValue.value,
    set: (value: string) => {
        inputValue.value = value
        emit('update:modelValue', value)
    }
})

// 监听modelValue变化
watch(() => props.modelValue, (newValue) => {
    inputValue.value = newValue
})

// 获取建议列表
const fetchSuggestions = async (query: string, callback: (suggestions: SuggestionItem[]) => void) => {
    try {
        let results: SuggestionItem[] = []

        if (props.remote && props.remoteMethod) {
            // 远程搜索
            results = await props.remoteMethod(query)
        } else if (typeof props.dataSource === 'function') {
            // 异步数据源
            results = await props.dataSource(query)
        } else if (Array.isArray(props.dataSource)) {
            // 本地数据源
            results = filterLocalData(query, props.dataSource)
        }

        suggestions.value = results
        callback(results)
    } catch (error) {
        console.error('获取建议列表失败:', error)
        callback([])
    }
}

// 过滤本地数据
const filterLocalData = (query: string, dataSource: SuggestionItem[]): SuggestionItem[] => {
    if (!query) return dataSource

    const filterFn = props.filterMethod || defaultFilterMethod
    return dataSource.filter(item => filterFn(query, item))
}

// 默认过滤方法
const defaultFilterMethod = (query: string, item: SuggestionItem): boolean => {
    const searchText = query.toLowerCase()
    const itemValue = (item.value || '').toLowerCase()
    const itemLabel = (item.label || '').toLowerCase()
    
    return itemValue.includes(searchText) || itemLabel.includes(searchText)
}

// 事件处理
const handleSelect = (item: SuggestionItem) => {
    emit('select', item)
}

const handleChange = (value: string) => {
    emit('change', value)
}

const handleInput = (value: string) => {
    computedValue.value = value
    emit('input', value)
}

const handleFocus = (event: FocusEvent) => {
    emit('focus', event)
}

const handleBlur = (event: FocusEvent) => {
    emit('blur', event)
}

const handleClear = () => {
    computedValue.value = ''
    emit('clear')
}

// 暴露方法
defineExpose({
    focus: () => {
        // 这里需要获取到el-autocomplete的引用并调用focus方法
    },
    blur: () => {
        // 这里需要获取到el-autocomplete的引用并调用blur方法
    }
})
</script>

<style scoped>
.suggestion-item {
    padding: 4px 0;
}

.suggestion-label {
    font-size: 14px;
    color: #303133;
    line-height: 1.5;
}

.suggestion-description {
    font-size: 12px;
    color: #909399;
    line-height: 1.4;
    margin-top: 2px;
}

:deep(.el-autocomplete) {
    width: 100%;
}
</style>