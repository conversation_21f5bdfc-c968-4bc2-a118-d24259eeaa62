<template>
  <div class="data-view h-full flex flex-col">
    <!-- 标签页 -->
    <div class="flex-none">
      <el-tabs v-model="activeTab" @tab-change="handleTabChange">
        <el-tab-pane label="内容编辑" name="content">
          <template #label>
            <span class="flex items-center">
              <el-icon class="mr-1"><Edit /></el-icon>
              内容编辑
            </span>
          </template>
        </el-tab-pane>
        <el-tab-pane label="飞飞辅助工具" name="assistant">
          <template #label>
            <span class="flex items-center">
              <el-icon class="mr-1"><MagicStick /></el-icon>
              飞飞辅助工具
            </span>
          </template>
        </el-tab-pane>
        <el-tab-pane label="笔顺工具" name="strokeOrder">
          <template #label>
            <span class="flex items-center">
              <el-icon class="mr-1"><Brush /></el-icon>
              笔顺工具
            </span>
          </template>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 内容区域 -->
    <div class="flex-1 overflow-y-auto p-4">
      <!-- 内容编辑标签页 -->
      <div v-show="activeTab === 'content'" class="h-full">
        <component
          v-if="currentTemplate"
          :is="currentTemplate"
          :data="formData"
          :config="templateConfig"
          @change="handleDataChange"
        />
        <div v-else class="flex items-center justify-center h-full text-gray-500">
          <div class="text-center">
            <el-icon size="48" class="mb-4"><Document /></el-icon>
            <p>请选择教学环节开始编辑</p>
          </div>
        </div>
      </div>

      <!-- 飞飞辅助工具标签页 -->
      <div v-show="activeTab === 'assistant'" class="h-full">
        <FeifeiAuxiliaryTool
          :data="assistantData"
          :config="templateConfig"
          @change="handleAssistantChange"
        />
      </div>

      <!-- 笔顺工具标签页 -->
      <div v-show="activeTab === 'strokeOrder'" class="h-full">
        <StrokeOrderTool
          :data="strokeOrderData"
          :config="templateConfig"
          @change="handleStrokeOrderChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, defineAsyncComponent } from 'vue'
import { Edit, MagicStick, Brush, Document } from '@element-plus/icons-vue'
import FeifeiAuxiliaryTool from './pageUtils/FeifeiAuxiliaryTool.vue'
import StrokeOrderTool from './pageUtils/StrokeOrderTool.vue'
import type { PhaseData, PageData } from '../types'

// 模板配置接口
interface TemplateConfig {
  coursewareId: string
  materielId: string
  stepId: string
  templateId: number
}

// Props
const props = defineProps<{
  coursewareId: string
  materielId: string
  menuData?: PhaseData
  phaseData?: PhaseData
  data?: PageData
  stepId?: string
}>()

// Events
const emit = defineEmits<{
  change: [data: any, type: string]
}>()

// 状态
const activeTab = ref('content')
const formData = ref<any>({})
const assistantData = ref<any>()
const strokeOrderData = ref<any>()

// 模板配置
const templateConfig = computed<TemplateConfig>(() => ({
  coursewareId: props.coursewareId,
  materielId: props.materielId,
  stepId: props.stepId || '',
  templateId: props.phaseData?.templateId || 0
}))

// 动态加载当前模板组件
const currentTemplate = computed(() => {
  if (!props.phaseData?.templateId) return null

  // 根据模板ID动态导入对应的模板组件
  const templateId = props.phaseData.templateId

  // 模板映射表 - 将模板ID映射到具体的模板组件
  const templateMap: Record<number, () => Promise<any>> = {
    0: () => import('../templates/FullScreenImageTemplate.vue'), // 全屏图
    1: () => import('../templates/ImageTemplate.vue'), // 单图
    2: () => import('../templates/TextTemplate.vue'), // 纯文字
    3: () => import('../templates/ImageTemplate.vue'), // 双图 (暂用单图模板)
    4: () => import('../templates/ImageTemplate.vue'), // 三图 (暂用单图模板)
    5: () => import('../templates/TextTemplate.vue'), // 阅读时间 (暂用文本模板)
    6: () => import('../templates/VideoTemplate.vue'), // 视频
    7: () => import('../templates/TextTemplate.vue'), // 开放性问题 (暂用文本模板)
    8: () => import('../templates/ImageTemplate.vue'), // 轮播图 (暂用图片模板)
    9: () => import('../templates/ImageTemplate.vue'), // 图片翻转 (暂用图片模板)
    10: () => import('../templates/TextTemplate.vue'), // 笔记单 (暂用文本模板)
    11: () => import('../templates/TextTemplate.vue'), // 选择题 (暂用文本模板)
    12: () => import('../templates/FullScreenImageTemplate.vue'), // 全屏图
    13: () => import('../templates/VideoTemplate.vue'), // 全屏视频
    14: () => import('../templates/VideoTemplate.vue'), // 有声绘本 (暂用视频模板)
    15: () => import('../templates/TextTemplate.vue') // 投票 (暂用文本模板)
  }

  const templateLoader = templateMap[templateId]

  return defineAsyncComponent(() =>
    templateLoader
      ? templateLoader().catch(() => import('../templates/DefaultTemplate.vue'))
      : import('../templates/DefaultTemplate.vue')
  )
})

// 监听数据变化
watch(
  () => props.data,
  newData => {
    if (newData) {
      formData.value = newData.details || {}
      assistantData.value = newData.tool?.assistant
      strokeOrderData.value = newData.tool?.strokeOrder
    }
  },
  { immediate: true, deep: true }
)

// 处理标签页切换
const handleTabChange = (tabName: string) => {
  activeTab.value = tabName
}

// 处理内容数据变化
const handleDataChange = (data: any) => {
  formData.value = { ...formData.value, ...data }
  emit(
    'change',
    {
      details: formData.value
    },
    'content'
  )
}

// 处理辅助工具数据变化
const handleAssistantChange = (data: any) => {
  assistantData.value = data
  emit(
    'change',
    {
      tool: {
        assistant: data,
        strokeOrder: strokeOrderData.value
      }
    },
    'assistant'
  )
}

// 处理笔顺工具数据变化
const handleStrokeOrderChange = (data: any) => {
  strokeOrderData.value = data
  emit(
    'change',
    {
      tool: {
        assistant: assistantData.value,
        strokeOrder: data
      }
    },
    'strokeOrder'
  )
}

// 获取所有数据
const getAllData = () => {
  return {
    details: formData.value,
    tool: {
      assistant: assistantData.value,
      strokeOrder: strokeOrderData.value
    }
  }
}

// 验证表单
const validateForm = () => {
  // 这里实现表单验证逻辑
  return true
}

// 设置活动标签页
const setActiveIndex = (index: string) => {
  activeTab.value = index
}

// 暴露方法
defineExpose({
  getAllData,
  validateForm,
  setActiveIndex
})
</script>

<style scoped>
.data-view {
  background: white;
}

:deep(.el-tabs__header) {
  margin: 0;
  border-bottom: 1px solid #e4e7ed;
}

:deep(.el-tabs__content) {
  padding: 0;
}
</style>
