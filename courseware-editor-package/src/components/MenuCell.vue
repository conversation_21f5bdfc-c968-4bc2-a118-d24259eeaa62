<template>
  <div
    class="menu-cell"
    :class="{
      active: active,
      edited: phase.status === 'edited',
      unedited: phase.status === 'unedited'
    }"
    @click="handleClick"
    @contextmenu.prevent="showContextMenu"
  >
    <div class="drag-handle">
      <el-icon><Rank /></el-icon>
    </div>

    <div class="cell-content">
      <div class="phase-info">
        <div class="phase-name" :title="phase.stepName">
          {{ phase.stepName }}
        </div>
        <div class="phase-meta">
          <span class="template-name">{{ phase.templateName }}</span>
          <span class="page-count">{{ phase.pageCount }}页</span>
        </div>
      </div>

      <div class="status-indicator">
        <el-icon v-if="phase.status === 'edited'" class="status-icon edited">
          <Check />
        </el-icon>
        <el-icon v-else class="status-icon unedited">
          <Warning />
        </el-icon>
      </div>
    </div>

    <!-- 右键菜单 -->
    <el-dropdown ref="contextMenuRef" trigger="contextmenu" @command="handleCommand">
      <span></span>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item command="edit">
            <el-icon><Edit /></el-icon>
            编辑环节
          </el-dropdown-item>
          <el-dropdown-item command="copy">
            <el-icon><CopyDocument /></el-icon>
            复制环节
          </el-dropdown-item>
          <el-dropdown-item command="delete" divided>
            <el-icon><Delete /></el-icon>
            删除环节
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>

    <!-- 编辑对话框 -->
    <el-dialog v-model="showEditDialog" title="编辑环节" width="400px">
      <el-form :model="editForm" label-width="80px">
        <el-form-item label="环节名称">
          <el-input v-model="editForm.stepName" placeholder="请输入环节名称" />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showEditDialog = false">取消</el-button>
        <el-button type="primary" @click="handleEditConfirm">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { Rank, Check, Warning, Edit, Delete, CopyDocument } from '@element-plus/icons-vue'
import type { PhaseData } from '../types'

// Props
const props = defineProps<{
  phase: PhaseData
  active?: boolean
}>()

// Events
const emit = defineEmits<{
  click: [phase: PhaseData]
  delete: [phase: PhaseData]
  edit: [phase: PhaseData]
  copy: [phase: PhaseData]
}>()

// 响应式数据
const contextMenuRef = ref()
const showEditDialog = ref(false)
const editForm = reactive({
  stepName: ''
})

// 方法
const handleClick = () => {
  emit('click', props.phase)
}

const showContextMenu = (event: MouseEvent) => {
  // Element Plus的dropdown组件会自动处理右键菜单显示
}

const handleCommand = (command: string) => {
  switch (command) {
    case 'edit':
      handleEdit()
      break
    case 'copy':
      emit('copy', props.phase)
      break
    case 'delete':
      emit('delete', props.phase)
      break
  }
}

const handleEdit = () => {
  editForm.stepName = props.phase.stepName
  showEditDialog.value = true
}

const handleEditConfirm = () => {
  const updatedPhase = {
    ...props.phase,
    stepName: editForm.stepName
  }
  emit('edit', updatedPhase)
  showEditDialog.value = false
}
</script>

<style lang="scss" scoped>
.menu-cell {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: #f5f7fa;
  }

  &.active {
    background-color: #e6f7ff;
    border-left: 3px solid #1890ff;
  }

  &.edited {
    .status-icon.edited {
      color: #52c41a;
    }
  }

  &.unedited {
    .status-icon.unedited {
      color: #faad14;
    }
  }
}

.drag-handle {
  margin-right: 8px;
  color: #bfbfbf;
  cursor: grab;

  &:active {
    cursor: grabbing;
  }
}

.cell-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-width: 0;
}

.phase-info {
  flex: 1;
  min-width: 0;
}

.phase-name {
  font-size: 14px;
  font-weight: 500;
  color: #262626;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.phase-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #8c8c8c;
}

.template-name {
  background-color: #f0f0f0;
  padding: 2px 6px;
  border-radius: 2px;
}

.status-indicator {
  margin-left: 8px;
}

.status-icon {
  font-size: 16px;
}
</style>
