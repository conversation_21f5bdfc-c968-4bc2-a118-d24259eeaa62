<template>
  <div class="courseware-editor-core">
    <!-- 头部插槽 -->
    <slot name="header" :config="config" :state="state" :toggle-menu="toggleMenu" />

    <div class="editor-container" :class="{ 'menu-collapsed': !state.menuShow }">
      <!-- 左侧菜单 -->
      <div v-show="state.menuShow" class="menu-panel">
        <MenuView
          :courseware-id="config.coursewareId"
          :api="config.api"
          :message-adapter="messageAdapter"
          @phase-click="handlePhaseClick"
          @phase-add="handlePhaseAdd"
          @phase-delete="handlePhaseDelete"
          @phase-sort="handlePhaseSort"
        />
      </div>

      <!-- 右侧编辑区域 -->
      <div v-show="state.dataShow" class="data-panel">
        <DataView
          v-if="state.selectMenuData"
          :phase-data="state.selectMenuData"
          :page-data="state.pageData"
          :api="config.api"
          :message-adapter="messageAdapter"
          @data-change="handleDataChange"
          @save-success="handleSaveSuccess"
          @save-error="handleSaveError"
        />
      </div>
    </div>

    <!-- 全屏预览 -->
    <FullscreenPreview
      ref="fullscreenPreviewRef"
      :courseware-id="config.coursewareId"
      :api="config.api"
      :player-config="config.playerConfig"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, provide, onMounted } from 'vue'
import MenuView from './MenuView.vue'
import DataView from './DataView.vue'
import FullscreenPreview from './FullscreenPreview.vue'
import { MessageAdapterKey, APIAdapterKey } from '../composables'
import type { CoursewareEditorConfig, MessageAdapter, EditorState } from '../types'

// Props
const props = defineProps<{
  config: CoursewareEditorConfig
  messageAdapter: MessageAdapter
}>()

// Events
const emit = defineEmits<{
  'save-success': [data: any]
  'save-error': [error: Error]
  'data-change': [data: any]
  'phase-click': [phase: any]
}>()

// Refs
const fullscreenPreviewRef = ref()

// 编辑器状态
const state = reactive<EditorState>({
  pageLoading: false,
  saveLoading: false,
  selectMenuData: undefined,
  phaseData: undefined,
  pageData: undefined,
  menuShow: true,
  dataShow: true,
  pageRemarkContent: '',
  stepId: undefined
})

// 提供依赖注入
provide(MessageAdapterKey, props.messageAdapter)
provide(APIAdapterKey, props.config.api)

// 方法
const toggleMenu = () => {
  state.menuShow = !state.menuShow
}

const handlePhaseClick = async (phase: any) => {
  try {
    state.pageLoading = true
    state.selectMenuData = phase
    state.stepId = phase.id

    // 加载页面详情
    const pageData = await props.config.api.fetchTeachPageDetail(phase.id)
    state.pageData = pageData
    state.dataShow = true

    emit('phase-click', phase)
  } catch (error) {
    props.messageAdapter.error('加载页面详情失败')
    console.error('Load page detail error:', error)
  } finally {
    state.pageLoading = false
  }
}

const handlePhaseAdd = (phase: any) => {
  // 处理环节添加
  console.log('Phase added:', phase)
}

const handlePhaseDelete = (phaseId: string) => {
  // 如果删除的是当前选中的环节，清空右侧
  if (state.selectMenuData?.id === phaseId) {
    state.selectMenuData = undefined
    state.pageData = undefined
    state.dataShow = false
  }
}

const handlePhaseSort = (phases: any[]) => {
  // 处理环节排序
  console.log('Phases sorted:', phases)
}

const handleDataChange = (data: any) => {
  emit('data-change', data)
}

const handleSaveSuccess = (data: any) => {
  emit('save-success', data)
}

const handleSaveError = (error: Error) => {
  emit('save-error', error)
}

// 暴露方法
const toggleFullscreen = () => {
  fullscreenPreviewRef.value?.toggle()
}

const saveAll = async () => {
  // 保存所有数据的逻辑
  try {
    state.saveLoading = true
    // 实现保存逻辑
    props.messageAdapter.success('保存成功')
  } catch (error) {
    props.messageAdapter.error('保存失败')
    throw error
  } finally {
    state.saveLoading = false
  }
}

// 暴露给父组件的方法
defineExpose({
  toggleFullscreen,
  saveAll,
  state
})

// 初始化
onMounted(() => {
  // 初始化逻辑
})
</script>

<style lang="scss" scoped>
.courseware-editor-core {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.editor-container {
  flex: 1;
  display: flex;
  min-height: 0;

  &.menu-collapsed .menu-panel {
    width: 0;
    overflow: hidden;
  }
}

.menu-panel {
  width: 300px;
  border-right: 1px solid #e4e7ed;
  transition: width 0.3s ease;
  overflow: hidden;
}

.data-panel {
  flex: 1;
  min-width: 0;
}
</style>
