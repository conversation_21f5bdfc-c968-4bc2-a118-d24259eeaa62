<template>
    <div v-if="visible" class="fullscreen-preview">
        <div class="preview-container">
            <!-- 头部控制栏 -->
            <div class="preview-header">
                <div class="header-left">
                    <h3 class="preview-title">课件预览</h3>
                </div>
                <div class="header-right">
                    <el-button type="primary" @click="togglePlay">
                        <el-icon><VideoPlay v-if="!playing" /><VideoPause v-else /></el-icon>
                        {{ playing ? '暂停' : '播放' }}
                    </el-button>
                    <el-button @click="exitFullscreen">
                        <el-icon><Close /></el-icon>
                        退出全屏
                    </el-button>
                </div>
            </div>

            <!-- 预览内容区域 -->
            <div class="preview-content">
                <div class="player-container" ref="playerContainerRef">
                    <!-- 播放器将在这里初始化 -->
                </div>
            </div>

            <!-- 底部控制栏 -->
            <div class="preview-footer">
                <div class="progress-container">
                    <el-slider
                        v-model="currentProgress"
                        :max="totalProgress"
                        :show-tooltip="false"
                        @change="handleProgressChange"
                    />
                </div>
                <div class="time-info">
                    <span>{{ formatTime(currentTime) }} / {{ formatTime(totalTime) }}</span>
                </div>
            </div>
        </div>

        <!-- 加载状态 -->
        <div v-if="loading" class="preview-loading">
            <el-loading-spinner />
            <p>正在加载预览数据...</p>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, nextTick } from 'vue'
import { VideoPlay, VideoPause, Close } from '@element-plus/icons-vue'
import { SlidePlayer } from 'courseware-player'
import type { CoursewareAPIAdapter } from '../types'

// Props
const props = defineProps<{
    coursewareId: string
    materielId: string
    apiAdapter?: CoursewareAPIAdapter
}>()

// Events
const emit = defineEmits<{
    exitFullscreen: []
}>()

// 状态
const visible = ref(false)
const loading = ref(false)
const playing = ref(false)
const currentProgress = ref(0)
const totalProgress = ref(100)
const currentTime = ref(0)
const totalTime = ref(0)

// Refs
const playerContainerRef = ref()
const player = ref<any>()

// 预览数据
const previewData = reactive({
    coursewareData: null,
    phaseData: [],
    pageData: []
})

// 打开全屏预览
const open = async () => {
    visible.value = true
    loading.value = true
    
    try {
        await loadPreviewData()
        await nextTick()
        await initPlayer()
    } catch (error) {
        console.error('加载预览数据失败:', error)
        ElMessage.error('加载预览数据失败')
    } finally {
        loading.value = false
    }
}

// 关闭全屏预览
const close = () => {
    visible.value = false
    destroyPlayer()
    emit('exitFullscreen')
}

// 退出全屏
const exitFullscreen = () => {
    close()
}

// 加载预览数据
const loadPreviewData = async () => {
    if (!props.apiAdapter) return

    try {
        // 获取课件详情
        const coursewareDetail = await props.apiAdapter.fetchCoursewareDetail(props.coursewareId)
        previewData.coursewareData = coursewareDetail

        // 获取教学环节列表
        const phases = await props.apiAdapter.fetchTeachPhases(props.coursewareId)
        previewData.phaseData = phases

        // 获取所有页面数据
        const pagePromises = phases.map(phase => 
            props.apiAdapter!.fetchTeachPageDetail(phase.id)
        )
        const pages = await Promise.all(pagePromises)
        previewData.pageData = pages.flat()

    } catch (error) {
        console.error('加载预览数据失败:', error)
        throw error
    }
}

// 初始化播放器
const initPlayer = async () => {
    if (!playerContainerRef.value || !previewData.pageData.length) return

    try {
        // 构建播放器数据
        const playerData = buildPlayerData()
        
        // 创建播放器实例
        player.value = new SlidePlayer({
            container: playerContainerRef.value,
            data: playerData,
            autoplay: false,
            controls: true,
            onProgress: handlePlayerProgress,
            onTimeUpdate: handleTimeUpdate,
            onEnded: handlePlayerEnded
        })

        // 初始化播放器
        await player.value.init()
        
    } catch (error) {
        console.error('初始化播放器失败:', error)
        throw error
    }
}

// 构建播放器数据
const buildPlayerData = () => {
    return {
        courseware: previewData.coursewareData,
        phases: previewData.phaseData.map(phase => ({
            ...phase,
            pages: previewData.pageData.filter(page => page.stepId === phase.id)
        }))
    }
}

// 销毁播放器
const destroyPlayer = () => {
    if (player.value) {
        player.value.destroy()
        player.value = null
    }
}

// 切换播放状态
const togglePlay = () => {
    if (!player.value) return

    if (playing.value) {
        player.value.pause()
    } else {
        player.value.play()
    }
    playing.value = !playing.value
}

// 处理进度变化
const handleProgressChange = (value: number) => {
    if (!player.value) return
    
    const time = (value / totalProgress.value) * totalTime.value
    player.value.seekTo(time)
}

// 处理播放器进度更新
const handlePlayerProgress = (progress: number) => {
    currentProgress.value = progress
}

// 处理播放器时间更新
const handleTimeUpdate = (current: number, total: number) => {
    currentTime.value = current
    totalTime.value = total
}

// 处理播放结束
const handlePlayerEnded = () => {
    playing.value = false
    currentProgress.value = 0
    currentTime.value = 0
}

// 格式化时间
const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

// 键盘事件处理
const handleKeydown = (event: KeyboardEvent) => {
    if (!visible.value) return

    switch (event.key) {
        case 'Escape':
            exitFullscreen()
            break
        case ' ':
            event.preventDefault()
            togglePlay()
            break
        case 'ArrowLeft':
            event.preventDefault()
            if (player.value) player.value.prev()
            break
        case 'ArrowRight':
            event.preventDefault()
            if (player.value) player.value.next()
            break
    }
}

// 生命周期
onMounted(() => {
    document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
    document.removeEventListener('keydown', handleKeydown)
    destroyPlayer()
})

// 暴露方法
defineExpose({
    open,
    close,
    visible
})
</script>

<style scoped>
.fullscreen-preview {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: #000;
    z-index: 9999;
    display: flex;
    flex-direction: column;
}

.preview-container {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.preview-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 24px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
}

.preview-title {
    margin: 0;
    font-size: 18px;
    font-weight: 500;
}

.header-right {
    display: flex;
    gap: 12px;
}

.preview-content {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 24px;
}

.player-container {
    width: 100%;
    height: 100%;
    max-width: 1200px;
    max-height: 800px;
    background: #fff;
    border-radius: 8px;
    overflow: hidden;
}

.preview-footer {
    display: flex;
    align-items: center;
    padding: 16px 24px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    gap: 16px;
}

.progress-container {
    flex: 1;
}

.time-info {
    font-size: 14px;
    color: #ccc;
}

.preview-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: white;
}

.preview-loading p {
    margin-top: 16px;
    font-size: 16px;
}

:deep(.el-slider__runway) {
    background-color: rgba(255, 255, 255, 0.3);
}

:deep(.el-slider__bar) {
    background-color: #409eff;
}

:deep(.el-slider__button) {
    border-color: #409eff;
}
</style>