<template>
    <el-dialog
        v-model="visible"
        title="选择环节模板"
        width="800px"
        :close-on-click-modal="false"
        @close="handleClose"
    >
        <div class="phase-model-content">
            <!-- 搜索栏 -->
            <div class="search-bar">
                <el-input
                    v-model="searchKeyword"
                    placeholder="搜索模板名称"
                    clearable
                    @input="handleSearch"
                >
                    <template #prefix>
                        <el-icon><Search /></el-icon>
                    </template>
                </el-input>
            </div>

            <!-- 模板分类 -->
            <div class="category-tabs">
                <el-tabs v-model="activeCategory" @tab-change="handleCategoryChange">
                    <el-tab-pane
                        v-for="category in categories"
                        :key="category.id"
                        :label="category.name"
                        :name="category.id"
                    />
                </el-tabs>
            </div>

            <!-- 模板列表 -->
            <div class="template-list" v-loading="loading">
                <div v-if="filteredTemplates.length === 0" class="empty-state">
                    <el-empty description="暂无模板数据" />
                </div>
                <div v-else class="template-grid">
                    <div
                        v-for="template in filteredTemplates"
                        :key="template.id"
                        class="template-item"
                        :class="{ 'selected': selectedTemplate?.id === template.id }"
                        @click="selectTemplate(template)"
                    >
                        <div class="template-preview">
                            <img
                                v-if="template.thumbnail"
                                :src="template.thumbnail"
                                :alt="template.name"
                                class="template-image"
                            />
                            <div v-else class="template-placeholder">
                                <el-icon size="48"><Document /></el-icon>
                            </div>
                        </div>
                        <div class="template-info">
                            <h4 class="template-name">{{ template.name }}</h4>
                            <p class="template-description">{{ template.description }}</p>
                            <div class="template-meta">
                                <span class="template-type">{{ template.typeName }}</span>
                                <span class="template-pages">{{ template.pageCount }}页</span>
                            </div>
                        </div>
                        <div v-if="selectedTemplate?.id === template.id" class="selected-indicator">
                            <el-icon><Check /></el-icon>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <template #footer>
            <div class="dialog-footer">
                <el-button @click="handleClose">取消</el-button>
                <el-button 
                    type="primary" 
                    @click="handleConfirm"
                    :disabled="!selectedTemplate"
                >
                    确定
                </el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { Search, Document, Check } from '@element-plus/icons-vue'
import type { CoursewareAPIAdapter } from '../../types'

// 模板接口
interface TemplateData {
    id: string
    name: string
    description: string
    thumbnail?: string
    typeName: string
    pageCount: number
    categoryId: string
}

// 分类接口
interface CategoryData {
    id: string
    name: string
}

// Props
const props = defineProps<{
    apiAdapter?: CoursewareAPIAdapter
}>()

// Events
const emit = defineEmits<{
    confirm: [template: TemplateData]
    cancel: []
}>()

// 状态
const visible = ref(false)
const loading = ref(false)
const searchKeyword = ref('')
const activeCategory = ref('all')
const selectedTemplate = ref<TemplateData>()

// 数据
const templates = ref<TemplateData[]>([])
const categories = ref<CategoryData[]>([
    { id: 'all', name: '全部' },
    { id: 'text', name: '文本模板' },
    { id: 'image', name: '图片模板' },
    { id: 'video', name: '视频模板' },
    { id: 'audio', name: '音频模板' },
    { id: 'interactive', name: '互动模板' },
    { id: 'test', name: '测试模板' }
])

// 计算属性
const filteredTemplates = computed(() => {
    let result = templates.value

    // 按分类筛选
    if (activeCategory.value !== 'all') {
        result = result.filter(template => template.categoryId === activeCategory.value)
    }

    // 按关键词搜索
    if (searchKeyword.value) {
        const keyword = searchKeyword.value.toLowerCase()
        result = result.filter(template => 
            template.name.toLowerCase().includes(keyword) ||
            template.description.toLowerCase().includes(keyword)
        )
    }

    return result
})

// 打开对话框
const open = async () => {
    visible.value = true
    selectedTemplate.value = undefined
    await loadTemplates()
}

// 关闭对话框
const close = () => {
    visible.value = false
    selectedTemplate.value = undefined
}

// 加载模板数据
const loadTemplates = async () => {
    if (!props.apiAdapter) return

    loading.value = true
    try {
        // 这里应该调用API获取模板列表
        // const templateList = await props.apiAdapter.fetchTemplateList()
        // templates.value = templateList

        // 临时使用模拟数据
        templates.value = [
            {
                id: '1',
                name: '基础文本模板',
                description: '适用于纯文本内容的展示',
                thumbnail: '',
                typeName: '文本',
                pageCount: 1,
                categoryId: 'text'
            },
            {
                id: '2',
                name: '图文混排模板',
                description: '图片和文字混合排版',
                thumbnail: '',
                typeName: '图文',
                pageCount: 2,
                categoryId: 'image'
            },
            {
                id: '3',
                name: '视频播放模板',
                description: '视频内容播放展示',
                thumbnail: '',
                typeName: '视频',
                pageCount: 1,
                categoryId: 'video'
            },
            {
                id: '4',
                name: '音频播放模板',
                description: '音频内容播放展示',
                thumbnail: '',
                typeName: '音频',
                pageCount: 1,
                categoryId: 'audio'
            },
            {
                id: '5',
                name: '选择题模板',
                description: '单选或多选题目',
                thumbnail: '',
                typeName: '测试',
                pageCount: 1,
                categoryId: 'test'
            }
        ]
    } catch (error) {
        console.error('加载模板失败:', error)
        ElMessage.error('加载模板失败')
    } finally {
        loading.value = false
    }
}

// 选择模板
const selectTemplate = (template: TemplateData) => {
    selectedTemplate.value = template
}

// 处理搜索
const handleSearch = () => {
    // 搜索逻辑已在计算属性中处理
}

// 处理分类切换
const handleCategoryChange = (categoryId: string) => {
    activeCategory.value = categoryId
}

// 处理确认
const handleConfirm = () => {
    if (!selectedTemplate.value) return
    
    emit('confirm', selectedTemplate.value)
    close()
}

// 处理关闭
const handleClose = () => {
    emit('cancel')
    close()
}

// 暴露方法
defineExpose({
    open,
    close
})
</script>

<style scoped>
.phase-model-content {
    max-height: 600px;
    display: flex;
    flex-direction: column;
}

.search-bar {
    margin-bottom: 16px;
}

.category-tabs {
    margin-bottom: 16px;
}

.template-list {
    flex: 1;
    overflow-y: auto;
}

.template-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 16px;
}

.template-item {
    border: 2px solid #e4e7ed;
    border-radius: 8px;
    padding: 12px;
    cursor: pointer;
    transition: all 0.3s;
    position: relative;
}

.template-item:hover {
    border-color: #409eff;
    box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

.template-item.selected {
    border-color: #409eff;
    background-color: #f0f9ff;
}

.template-preview {
    width: 100%;
    height: 120px;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 12px;
    background: #f5f7fa;
    display: flex;
    align-items: center;
    justify-content: center;
}

.template-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.template-placeholder {
    color: #c0c4cc;
}

.template-info {
    text-align: center;
}

.template-name {
    font-size: 14px;
    font-weight: 500;
    margin: 0 0 8px 0;
    color: #303133;
}

.template-description {
    font-size: 12px;
    color: #909399;
    margin: 0 0 8px 0;
    line-height: 1.4;
}

.template-meta {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: #606266;
}

.selected-indicator {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 20px;
    height: 20px;
    background: #409eff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.empty-state {
    text-align: center;
    padding: 40px 0;
}

.dialog-footer {
    text-align: center;
}

:deep(.el-tabs__header) {
    margin: 0;
}

:deep(.el-tabs__content) {
    padding: 0;
}
</style>