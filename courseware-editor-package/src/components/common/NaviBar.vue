<template>
  <el-page-header @back="goBack">
    <template #content>
      <div class="flex items-center">
        <slot name="title" />
        <span class="text-lg font-semibold line-clamp-1" v-if="title">{{ title }}</span>
        <span 
          class="text-base ml-3 line-clamp-1 text-gray-600" 
          v-if="subTitle"
        >
          {{ subTitle }}
        </span>
      </div>
    </template>
    <template #extra>
      <slot name="extra" />
    </template>
  </el-page-header>
</template>

<script setup lang="ts">
// Props
const props = defineProps<{
  title?: string
  subTitle?: string
  destroy?: boolean
  from?: string
}>()

// Events
const emit = defineEmits<{
  back: []
}>()

// 返回处理
const goBack = () => {
  emit('back')
  
  // 如果有指定的返回路径，可以在父组件中处理
  // 这里只发出事件，让父组件决定如何处理返回逻辑
}

// 暴露方法
defineExpose({
  back: goBack
})
</script>

<style scoped>
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.font-semibold {
  font-weight: 600;
}

.text-gray-600 {
  color: #6b7280;
}
</style>
