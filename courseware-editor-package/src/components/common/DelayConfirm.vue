<template>
    <el-dialog
        v-model="visible"
        :title="title"
        width="400px"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :show-close="false"
        center
    >
        <div class="delay-confirm-content">
            <div class="message">{{ message }}</div>
            <div v-if="countdown > 0" class="countdown">
                {{ countdown }}秒后自动{{ confirmText }}
            </div>
        </div>

        <template #footer>
            <div class="dialog-footer">
                <el-button @click="handleCancel">{{ cancelText }}</el-button>
                <el-button 
                    type="primary" 
                    @click="handleConfirm"
                    :disabled="countdown > 0"
                >
                    {{ confirmText }}{{ countdown > 0 ? ` (${countdown})` : '' }}
                </el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
import { ref, onUnmounted } from 'vue'

// Props
const props = withDefaults(defineProps<{
    title?: string
    message?: string
    confirmText?: string
    cancelText?: string
}>(), {
    title: '确认',
    message: '确定要执行此操作吗？',
    confirmText: '确定',
    cancelText: '取消'
})

// Events
const emit = defineEmits<{
    confirm: []
    cancel: []
}>()

// 状态
const visible = ref(false)
const countdown = ref(0)
const timer = ref<NodeJS.Timeout>()

// 打开对话框
const open = (delaySeconds: number = 0) => {
    visible.value = true
    countdown.value = delaySeconds
    
    if (delaySeconds > 0) {
        startCountdown()
    }
}

// 关闭对话框
const close = () => {
    visible.value = false
    stopCountdown()
}

// 开始倒计时
const startCountdown = () => {
    if (timer.value) {
        clearInterval(timer.value)
    }
    
    timer.value = setInterval(() => {
        countdown.value--
        
        if (countdown.value <= 0) {
            stopCountdown()
        }
    }, 1000)
}

// 停止倒计时
const stopCountdown = () => {
    if (timer.value) {
        clearInterval(timer.value)
        timer.value = undefined
    }
    countdown.value = 0
}

// 处理确认
const handleConfirm = () => {
    if (countdown.value > 0) return
    
    emit('confirm')
    close()
}

// 处理取消
const handleCancel = () => {
    emit('cancel')
    close()
}

// 组件卸载时清理定时器
onUnmounted(() => {
    stopCountdown()
})

// 暴露方法
defineExpose({
    open,
    close
})
</script>

<style scoped>
.delay-confirm-content {
    text-align: center;
    padding: 20px 0;
}

.message {
    font-size: 16px;
    color: #606266;
    margin-bottom: 16px;
}

.countdown {
    font-size: 14px;
    color: #909399;
    font-weight: 500;
}

.dialog-footer {
    text-align: center;
}

.dialog-footer .el-button {
    margin: 0 8px;
}
</style>