<template>
    <div class="file-oss-upload">
        <el-upload
            :action="uploadUrl"
            :data="uploadData"
            :headers="uploadHeaders"
            :before-upload="beforeUpload"
            :on-success="handleSuccess"
            :on-error="handleError"
            :on-progress="handleProgress"
            :show-file-list="false"
            :accept="accept"
            :disabled="uploading"
        >
            <el-button type="primary" :loading="uploading">
                <el-icon class="mr-1"><Upload /></el-icon>
                {{ uploading ? '上传中...' : '选择文件' }}
            </el-button>
        </el-upload>

        <!-- 文件列表 -->
        <div v-if="fileList.length > 0" class="file-list mt-3">
            <div v-for="(file, index) in fileList" :key="index" class="file-item">
                <div class="file-info">
                    <el-icon class="file-icon"><Document /></el-icon>
                    <span class="file-name">{{ file.name }}</span>
                    <span class="file-size">{{ formatFileSize(file.size) }}</span>
                </div>
                <div class="file-actions">
                    <el-button size="small" type="primary" @click="downloadFile(file)">
                        <el-icon><Download /></el-icon>
                    </el-button>
                    <el-button size="small" type="danger" @click="removeFile(index)">
                        <el-icon><Delete /></el-icon>
                    </el-button>
                </div>
            </div>
        </div>

        <!-- 上传进度 -->
        <div v-if="uploading" class="upload-progress mt-3">
            <el-progress :percentage="uploadProgress" />
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { Upload, Document, Download, Delete } from '@element-plus/icons-vue';
import type { UploadProps, UploadFile } from 'element-plus';

// 文件信息接口
interface FileInfo {
    name: string;
    url: string;
    size: number;
    type: string;
}

// Props
const props = defineProps<{
    modelValue?: FileInfo[];
    accept?: string;
    maxSize?: number; // MB
    maxCount?: number;
    ossConfig?: {
        accessKeyId: string;
        accessKeySecret: string;
        bucket: string;
        region: string;
        dir?: string;
    };
}>();

// Events
const emit = defineEmits<{
    'update:modelValue': [value: FileInfo[]];
    'upload-success': [file: FileInfo];
    'upload-error': [error: Error];
}>();

// 状态
const uploading = ref(false);
const uploadProgress = ref(0);
const fileList = ref<FileInfo[]>([]);

// 监听modelValue变化
watch(
    () => props.modelValue,
    (newValue) => {
        if (newValue) {
            fileList.value = [...newValue];
        }
    },
    { immediate: true }
);

// 计算属性
const uploadUrl = computed(() => {
    return '/api/upload/file';
});

const uploadData = computed(() => {
    return {
        dir: props.ossConfig?.dir || 'courseware/files',
    };
});

const uploadHeaders = computed(() => {
    return {
        Authorization: `Bearer ${getToken()}`,
    };
});

// 获取token
const getToken = () => {
    return localStorage.getItem('token') || '';
};

// 上传前检查
const beforeUpload: UploadProps['beforeUpload'] = (file) => {
    // 检查文件数量
    const maxCount = props.maxCount || 10;
    if (fileList.value.length >= maxCount) {
        ElMessage.error(`最多只能上传 ${maxCount} 个文件!`);
        return false;
    }

    // 检查文件大小
    const maxSize = props.maxSize || 10; // 默认10MB
    const isLtMaxSize = file.size / 1024 / 1024 < maxSize;
    if (!isLtMaxSize) {
        ElMessage.error(`文件大小不能超过 ${maxSize}MB!`);
        return false;
    }

    uploading.value = true;
    uploadProgress.value = 0;

    return true;
};

// 上传成功
const handleSuccess = (response: any, file: UploadFile) => {
    uploading.value = false;
    uploadProgress.value = 100;

    const fileUrl = response.data?.url || response.url;
    if (fileUrl) {
        const newFile: FileInfo = {
            name: file.name,
            url: fileUrl,
            size: file.size || 0,
            type: file.type || '',
        };

        fileList.value.push(newFile);
        emit('update:modelValue', [...fileList.value]);
        emit('upload-success', newFile);
        ElMessage.success('文件上传成功!');
    } else {
        ElMessage.error('上传失败，未获取到文件地址');
    }
};

// 上传失败
const handleError = (error: Error) => {
    uploading.value = false;
    uploadProgress.value = 0;
    emit('upload-error', error);
    ElMessage.error('文件上传失败!');
};

// 上传进度
const handleProgress = (event: any) => {
    uploadProgress.value = Math.round(event.percent);
};

// 下载文件
const downloadFile = (file: FileInfo) => {
    const link = document.createElement('a');
    link.href = file.url;
    link.download = file.name;
    link.target = '_blank';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
};

// 删除文件
const removeFile = (index: number) => {
    ElMessageBox.confirm('确定要删除这个文件吗？', '确认删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    })
        .then(() => {
            fileList.value.splice(index, 1);
            emit('update:modelValue', [...fileList.value]);
            ElMessage.success('文件已删除');
        })
        .catch(() => {
            // 取消删除
        });
};

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};
</script>

<style scoped>
.file-oss-upload {
    width: 100%;
}

.file-list {
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    overflow: hidden;
}

.file-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    border-bottom: 1px solid #e4e7ed;
}

.file-item:last-child {
    border-bottom: none;
}

.file-info {
    display: flex;
    align-items: center;
    flex: 1;
}

.file-icon {
    color: #409eff;
    margin-right: 8px;
}

.file-name {
    font-weight: 500;
    margin-right: 12px;
}

.file-size {
    color: #909399;
    font-size: 12px;
}

.file-actions {
    display: flex;
    gap: 8px;
}

.upload-progress {
    margin-top: 12px;
}
</style>
