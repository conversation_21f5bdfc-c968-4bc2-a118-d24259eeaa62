<template>
    <div class="image-oss-upload">
        <el-upload
            :action="uploadUrl"
            :data="uploadData"
            :headers="uploadHeaders"
            :before-upload="beforeUpload"
            :on-success="handleSuccess"
            :on-error="handleError"
            :on-progress="handleProgress"
            :show-file-list="false"
            :accept="accept"
            :disabled="uploading"
        >
            <div class="upload-area" :class="{ 'is-uploading': uploading }">
                <div v-if="!imageUrl && !uploading" class="upload-placeholder">
                    <el-icon size="48"><Plus /></el-icon>
                    <div class="upload-text">点击上传图片</div>
                </div>
                <div v-else-if="uploading" class="upload-progress">
                    <el-progress
                        type="circle"
                        :percentage="uploadProgress"
                        :width="60"
                    />
                    <div class="upload-text">上传中...</div>
                </div>
                <div v-else class="image-preview">
                    <img :src="imageUrl" :alt="fileName" />
                    <div class="image-overlay">
                        <el-button size="small" type="primary" @click.stop="previewImage">
                            <el-icon><View /></el-icon>
                        </el-button>
                        <el-button size="small" type="danger" @click.stop="removeImage">
                            <el-icon><Delete /></el-icon>
                        </el-button>
                    </div>
                </div>
            </div>
        </el-upload>

        <!-- 图片预览对话框 -->
        <el-dialog v-model="previewVisible" title="图片预览" width="60%">
            <img :src="imageUrl" style="width: 100%" />
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Plus, View, Delete } from '@element-plus/icons-vue'
import type { UploadProps, UploadFile } from 'element-plus'

// Props
const props = defineProps<{
    modelValue?: string
    accept?: string
    maxSize?: number // MB
    ossConfig?: {
        accessKeyId: string
        accessKeySecret: string
        bucket: string
        region: string
        dir?: string
    }
}>()

// Events
const emit = defineEmits<{
    'update:modelValue': [value: string]
    'upload-success': [url: string, file: UploadFile]
    'upload-error': [error: Error]
}>()

// 状态
const uploading = ref(false)
const uploadProgress = ref(0)
const previewVisible = ref(false)
const fileName = ref('')

// 计算属性
const imageUrl = computed(() => props.modelValue)

const uploadUrl = computed(() => {
    // 这里应该返回OSS上传的URL或者后端代理上传的URL
    return '/api/upload/image'
})

const uploadData = computed(() => {
    // 上传时需要的额外数据
    return {
        dir: props.ossConfig?.dir || 'courseware/images'
    }
})

const uploadHeaders = computed(() => {
    // 上传时需要的请求头
    return {
        'Authorization': `Bearer ${getToken()}`
    }
})

// 获取token的方法（需要根据实际情况实现）
const getToken = () => {
    // 这里应该返回实际的认证token
    return localStorage.getItem('token') || ''
}

// 上传前的检查
const beforeUpload: UploadProps['beforeUpload'] = (file) => {
    // 检查文件类型
    const isImage = file.type.startsWith('image/')
    if (!isImage) {
        ElMessage.error('只能上传图片文件!')
        return false
    }

    // 检查文件大小
    const maxSize = props.maxSize || 5 // 默认5MB
    const isLtMaxSize = file.size / 1024 / 1024 < maxSize
    if (!isLtMaxSize) {
        ElMessage.error(`图片大小不能超过 ${maxSize}MB!`)
        return false
    }

    uploading.value = true
    uploadProgress.value = 0
    fileName.value = file.name

    return true
}

// 上传成功
const handleSuccess = (response: any, file: UploadFile) => {
    uploading.value = false
    uploadProgress.value = 100

    // 根据实际的响应格式获取图片URL
    const imageUrl = response.data?.url || response.url
    if (imageUrl) {
        emit('update:modelValue', imageUrl)
        emit('upload-success', imageUrl, file)
        ElMessage.success('图片上传成功!')
    } else {
        ElMessage.error('上传失败，未获取到图片地址')
    }
}

// 上传失败
const handleError = (error: Error) => {
    uploading.value = false
    uploadProgress.value = 0
    emit('upload-error', error)
    ElMessage.error('图片上传失败!')
}

// 上传进度
const handleProgress = (event: any) => {
    uploadProgress.value = Math.round(event.percent)
}

// 预览图片
const previewImage = () => {
    previewVisible.value = true
}

// 删除图片
const removeImage = () => {
    ElMessageBox.confirm('确定要删除这张图片吗？', '确认删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        emit('update:modelValue', '')
        ElMessage.success('图片已删除')
    }).catch(() => {
        // 取消删除
    })
}
</script>

<style scoped>
.image-oss-upload {
    display: inline-block;
}

.upload-area {
    width: 120px;
    height: 120px;
    border: 2px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
}

.upload-area:hover {
    border-color: #409eff;
}

.upload-area.is-uploading {
    border-color: #409eff;
    background-color: #f5f7fa;
}

.upload-placeholder {
    text-align: center;
    color: #8c939d;
}

.upload-text {
    font-size: 12px;
    margin-top: 8px;
}

.upload-progress {
    text-align: center;
}

.image-preview {
    width: 100%;
    height: 100%;
    position: relative;
}

.image-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    opacity: 0;
    transition: opacity 0.3s;
}

.image-preview:hover .image-overlay {
    opacity: 1;
}
</style>