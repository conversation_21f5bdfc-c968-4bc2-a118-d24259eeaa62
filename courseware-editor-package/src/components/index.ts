// 导出所有核心组件
export { default as CoursewareEditorCore } from './CoursewareEditorCore.vue'
export { default as MenuView } from './MenuView.vue'
export { default as DataView } from './DataView.vue'
export { default as MenuCell } from './MenuCell.vue'
export { default as FullscreenPreview } from './FullscreenPreview.vue'

// 导出通用组件
export { default as DelayConfirm } from './common/DelayConfirm.vue'
export { default as PhaseModelAlert } from './common/PhaseModelAlert.vue'
export { default as NaviBar } from './common/NaviBar.vue'

// 导出上传组件
export { default as ImageOss } from './upload/ImageOss.vue'
export { default as FileOss } from './upload/FileOss.vue'

// 导出编辑器组件
export { default as Editor } from './editor/Editor.vue'
export { default as CodeEditor } from './editor/CodeEditor.vue'
export { default as YdAutocomplete } from './editor/YdAutocomplete.vue'

// 导出页面工具组件
export { default as FeifeiAuxiliaryTool } from './pageUtils/FeifeiAuxiliaryTool.vue'
export { default as StrokeOrderTool } from './pageUtils/StrokeOrderTool.vue'

// 导出模板组件
export { default as TextTemplate } from './templates/TextTemplate.vue'
export { default as ImageTemplate } from './templates/ImageTemplate.vue'
export { default as VideoTemplate } from './templates/VideoTemplate.vue'
export { default as FullScreenImageTemplate } from './templates/FullScreenImageTemplate.vue'
export { default as DefaultTemplate } from './templates/DefaultTemplate.vue'
