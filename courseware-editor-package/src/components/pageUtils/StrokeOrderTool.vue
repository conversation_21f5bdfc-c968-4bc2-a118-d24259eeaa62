<template>
  <div class="h-full flex flex-col">
    <el-row type="flex" align="middle" class="border-b h-12 px-6 bg-[#f9f9f9] flex-shrink-0">
      <span>是否启用笔顺工具</span>
      <el-switch v-model="form.enable" class="ml-2" active-value :active-color="'#13ce66'" />
      <el-button :disabled="!form.enable" type="primary" size="small" @click="addStroke" class="ml-5" plain>
        增加笔顺
      </el-button>
    </el-row>
    <div class="flex-1 h-full overflow-hidden">
      <div v-loading="loading" class="h-full overflow-y-auto">
        <el-form ref="strokeForm" :model="form" :rules="rules" label-position="top" class="px-6">
          <div
            v-for="(item, index) in form.content"
            :key="index"
            class="flex items-start mb-4 border-b border-gray-300 py-6 gap-x-4"
          >
            <el-form-item
              :label="`笔顺${index + 1}文字`"
              :prop="'content.' + index + '.word'"
              :rules="rules.word"
            >
              <el-input
                v-model="item.word"
                :disabled="!form.enable"
                style="width: 220px"
                maxlength="4"
                placeholder="请输入文字"
                show-word-limit
                type="text"
              />
            </el-form-item>
            <el-form-item :label="'笔顺图片'" :prop="'content.' + index + '.url'" :rules="rules.imagePath">
              <ImageOss
                service-name="teaching"
                v-model:relative-path="item.url"
                v-model:full-path="item.path"
                :disabled="!form.enable"
                module-name="courseware"
                :file-type="['jpg', 'png', 'jpeg']"
                :fileSize="10"
                width="80px"
                height="80px"
                :iconSize="20"
              />
            </el-form-item>
            <el-button
              :disabled="!form.enable"
              type="danger"
              icon="Delete"
              size="small"
              @click="removeStroke(index)"
              class="mt-8"
            >
              删除
            </el-button>
          </div>
          
          <div v-if="form.content.length === 0 && form.enable" class="text-center py-8 text-gray-500">
            <el-icon size="48" class="mb-4"><Edit /></el-icon>
            <p>暂无笔顺数据，点击"增加笔顺"开始添加</p>
          </div>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, inject } from 'vue'
import { Delete, Edit } from '@element-plus/icons-vue'
import ImageOss from '../upload/ImageOss.vue'
import { MessageAdapterKey } from '../../composables'
import type { MessageAdapter } from '../../types'

// Props
const props = defineProps<{
  data?: any
  config?: any
}>()

// Events
const emit = defineEmits<{
  change: [data: any]
}>()

// 依赖注入
const messageAdapter = inject(MessageAdapterKey) as MessageAdapter

// 表单引用
const strokeForm = ref()
const loading = ref(false)

// 表单数据
const form = reactive({
  enable: false,
  content: [] as Array<{
    word: string
    url: string
    path: string
  }>
})

// 验证规则
const rules = {
  word: [
    {
      validator: (rule: any, value: string, callback: Function) => {
        if (form.enable && !value?.trim()) {
          callback(new Error('请输入文字'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  imagePath: [
    {
      validator: (rule: any, value: string, callback: Function) => {
        if (form.enable && !value?.trim()) {
          callback(new Error('请上传笔顺图片'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 监听数据变化
watch(() => props.data, (newData) => {
  if (newData) {
    form.enable = newData.enable || false
    form.content = newData.content || []
  }
}, { immediate: true, deep: true })

// 监听表单数据变化，发出事件
watch(form, () => {
  emit('change', { ...form })
}, { deep: true })

// 添加笔顺
const addStroke = () => {
  form.content.push({
    word: '',
    url: '',
    path: ''
  })
}

// 删除笔顺
const removeStroke = (index: number) => {
  form.content.splice(index, 1)
}

// 验证表单
const validateForm = async () => {
  if (!strokeForm.value) return true
  
  try {
    await strokeForm.value.validate()
    return true
  } catch (error) {
    return false
  }
}

// 暴露方法
defineExpose({
  validateForm
})
</script>

<style scoped>
.ml-5 {
  margin-left: 1.25rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mt-8 {
  margin-top: 2rem;
}

.py-8 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}
</style>
