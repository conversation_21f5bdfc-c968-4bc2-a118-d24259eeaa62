<template>
  <div class="h-full flex flex-col">
    <el-row type="flex" align="middle" class="border-b h-12 px-6 bg-[#f9f9f9] flex-shrink-0">
      <span>是否启用飞飞辅助工具</span>
      <el-switch v-model="assistant.enable" class="ml-2" active-value :active-color="'#13ce66'" />
    </el-row>
    <div class="flex-1 h-full overflow-hidden">
      <div class="h-full overflow-y-auto">
        <el-form ref="dataFormRef" :model="assistant" label-position="top" :rules="rules">
          <el-row>
            <el-col :span="24">
              <el-form-item label="内容" prop="content.text" :rules="rules.text">
                <el-input
                  :disabled="!assistant.enable"
                  v-model="assistant.content.text"
                  :maxlength="config?.feifei?.feifeiInputMaxLength || 500"
                  show-word-limit
                  placeholder="请输入内容"
                  :autosize="{ minRows: 3 }"
                  type="textarea"
                />
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="音频" prop="content.url" :rules="rules.url">
                <div
                  v-if="assistant.content.url"
                  class="w-full flex items-center gap-[3px] text-gray-600"
                >
                  <el-icon>
                    <Document />
                  </el-icon>
                  <span class="line-clamp-1 overflow-hidden">{{
                    assistant.content.fileName || assistant.content.url
                  }}</span>
                  <el-icon
                    class="cursor-pointer text-red-400"
                    :disabled="!assistant.enable"
                    @click="handleDeleteAssistant"
                  >
                    <CircleClose />
                  </el-icon>
                </div>
                <FileOss
                  v-else
                  moduleName="courseware"
                  service-name="teaching"
                  :file-type="config?.feifei?.videoUploadType || ['mp3', 'wav']"
                  :file-size="config?.feifei?.videoUploadSize || 10"
                  :disabled="!assistant.enable"
                  @success="handleAssistantUpload"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, inject } from 'vue'
import { Document, CircleClose } from '@element-plus/icons-vue'
import FileOss from '../upload/FileOss.vue'
import { MessageAdapterKey } from '../../composables'
import type { MessageAdapter } from '../../types'

// Props
const props = defineProps<{
  data?: any
  config?: any
}>()

// Events
const emit = defineEmits<{
  change: [data: any]
}>()

// 依赖注入
const messageAdapter = inject(MessageAdapterKey) as MessageAdapter

// 表单引用
const dataFormRef = ref()

// 助手数据
const assistant = reactive({
  enable: false,
  content: {
    text: '',
    url: '',
    fileName: ''
  }
})

// 验证规则
const rules = {
  text: [
    {
      validator: (rule: any, value: string, callback: Function) => {
        if (assistant.enable && !value?.trim()) {
          callback(new Error('请输入内容'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  url: [
    {
      validator: (rule: any, value: string, callback: Function) => {
        if (assistant.enable && !value?.trim()) {
          callback(new Error('请上传音频文件'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 监听数据变化
watch(() => props.data, (newData) => {
  if (newData) {
    Object.assign(assistant, {
      enable: newData.enable || false,
      content: {
        text: newData.content?.text || '',
        url: newData.content?.url || '',
        fileName: newData.content?.fileName || ''
      }
    })
  }
}, { immediate: true, deep: true })

// 监听助手数据变化，发出事件
watch(assistant, () => {
  emit('change', { ...assistant })
}, { deep: true })

// 处理音频上传成功
const handleAssistantUpload = (fileInfo: any) => {
  assistant.content.url = fileInfo.url
  assistant.content.fileName = fileInfo.fileName
  messageAdapter?.success('音频上传成功')
}

// 删除音频
const handleDeleteAssistant = () => {
  assistant.content.url = ''
  assistant.content.fileName = ''
}

// 验证表单
const validateForm = async () => {
  if (!dataFormRef.value) return true
  
  try {
    await dataFormRef.value.validate()
    return true
  } catch (error) {
    return false
  }
}

// 暴露方法
defineExpose({
  validateForm
})
</script>

<style scoped>
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
