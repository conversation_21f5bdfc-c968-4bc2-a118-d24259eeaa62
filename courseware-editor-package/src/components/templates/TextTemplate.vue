<template>
  <el-form ref="dataFormRef" :model="form" :rules="rules" label-position="top">
    <el-collapse v-model="activeNames">
      <el-collapse-item title="基本内容" name="1">
        <el-row :gutter="12">
          <el-col :span="12">
            <el-form-item label="标题">
              <el-input
                v-model="form.title.content"
                :maxlength="config?.titleMaxLength || 50"
                show-word-limit
                placeholder="请输入标题"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="背景图片" prop="background.url">
              <ImageOss
                v-model:relative-path="form.background.url"
                v-model:full-path="form.background.path"
                :service-name="config?.imageUploadOssName || 'teaching'"
                :module-name="config?.imageUploadOssModuleName || 'courseware'"
                :file-type="config?.backgroundUploadType || ['jpg', 'png', 'jpeg']"
                :file-size="config?.imageUploadSize || 10"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-collapse-item>
      <el-collapse-item title="内容文本" name="2">
        <el-row :gutter="12">
          <el-col :span="24">
            <el-form-item label="文本内容" prop="paragraph.content">
              <Editor
                v-model:get-html="form.paragraph.content"
                :mode="editorMode"
                :height="'400'"
                :placeholder="'请输入文本内容...'"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-collapse-item>
      <el-collapse-item title="使用说明" name="3">
        <div class="mt-2 ml-2">{{ remark || '此模板暂无操作说明，敬请谅解!' }}</div>
      </el-collapse-item>
    </el-collapse>
  </el-form>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import ImageOss from '../upload/ImageOss.vue'
import Editor from '../editor/Editor.vue'

// 类型定义
interface TextType {
  content: string
  color?: string
  fontSize?: string
}

interface TextareaType {
  content: string
  color?: string
  fontSize?: string
}

interface PublicType {
  background: {
    url: string
    path: string
  }
}

interface DetailType extends PublicType {
  paragraph: TextareaType
  title: TextType
  notes: TextType
}

// Props
const props = defineProps<{
  data?: DetailType
  config?: any
  remark?: string
}>()

// Events
const emit = defineEmits<{
  change: [data: DetailType]
}>()

// 状态
const activeNames = ref(['1', '2'])
const editorMode = ref<'simple' | 'default'>('simple')
const dataFormRef = ref()

// 表单数据
const form = reactive<DetailType>({
  title: { content: '' },
  paragraph: { content: '' },
  notes: { content: '' },
  background: { url: '', path: '' },
  ...props.data
})

// 验证规则
const rules = {
  'background.url': [
    {
      validator: (_rule: any, value: any, callback: any) => {
        if (value === '') {
          callback(new Error('请上传背景图片'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  'paragraph.content': [
    {
      validator: (_rule: any, value: any, callback: any) => {
        if (value === '') {
          callback(new Error('请输入文本内容'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 监听表单数据变化
watch(
  form,
  () => {
    emit('change', { ...form })
  },
  { deep: true }
)

// 监听props变化
watch(
  () => props.data,
  newData => {
    if (newData) {
      Object.assign(form, newData)
    }
  },
  { immediate: true, deep: true }
)
</script>

<style scoped>
.mt-2 {
  margin-top: 0.5rem;
}

.ml-2 {
  margin-left: 0.5rem;
}
</style>
