<template>
    <div class="text-template" :style="containerStyle">
        <div class="text-content" :style="textStyle">
            <h1 v-if="data.title" class="text-title" :style="titleStyle">
                {{ data.title }}
            </h1>
            <div class="text-body" :style="bodyStyle" v-html="data.content"></div>
            <div v-if="data.footer" class="text-footer" :style="footerStyle">
                {{ data.footer }}
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

const props = withDefaults(defineProps<{
    data: {
        title?: string
        content: string
        footer?: string
        style?: {
            backgroundColor?: string
            padding?: string
            textAlign?: 'left' | 'center' | 'right'
            fontSize?: string
            color?: string
            titleColor?: string
            footerColor?: string
        }
    }
}>(), {
    data: () => ({
        content: '',
        style: {}
    })
})

const containerStyle = computed(() => ({
    backgroundColor: props.data.style?.backgroundColor || '#ffffff',
    padding: props.data.style?.padding || '20px',
    textAlign: props.data.style?.textAlign || 'left',
    minHeight: '200px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center'
}))

const textStyle = computed(() => ({
    width: '100%',
    maxWidth: '800px'
}))

const titleStyle = computed(() => ({
    color: props.data.style?.titleColor || props.data.style?.color || '#333333',
    fontSize: '24px',
    fontWeight: 'bold',
    marginBottom: '16px',
    textAlign: props.data.style?.textAlign || 'left'
}))

const bodyStyle = computed(() => ({
    color: props.data.style?.color || '#666666',
    fontSize: props.data.style?.fontSize || '16px',
    lineHeight: '1.6',
    marginBottom: '16px'
}))

const footerStyle = computed(() => ({
    color: props.data.style?.footerColor || props.data.style?.color || '#999999',
    fontSize: '14px',
    textAlign: props.data.style?.textAlign || 'left'
}))
</script>

<style scoped>
.text-template {
    width: 100%;
    height: 100%;
}

.text-content {
    word-wrap: break-word;
}

.text-title {
    margin: 0 0 16px 0;
}

.text-body {
    margin: 0 0 16px 0;
}

.text-body :deep(p) {
    margin: 0 0 12px 0;
}

.text-body :deep(p:last-child) {
    margin-bottom: 0;
}

.text-footer {
    margin: 0;
}
</style>