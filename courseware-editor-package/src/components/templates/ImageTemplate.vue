<template>
    <div class="image-template" :style="containerStyle">
        <div class="image-content">
            <div v-if="data.title" class="image-title" :style="titleStyle">
                {{ data.title }}
            </div>
            
            <div class="image-wrapper" :style="imageWrapperStyle">
                <img
                    v-if="data.src"
                    :src="data.src"
                    :alt="data.alt || data.title"
                    :style="imageStyle"
                    @load="handleImageLoad"
                    @error="handleImageError"
                />
                <div v-else class="image-placeholder" :style="placeholderStyle">
                    <el-icon size="48"><Picture /></el-icon>
                    <p>请选择图片</p>
                </div>
            </div>
            
            <div v-if="data.description" class="image-description" :style="descriptionStyle">
                {{ data.description }}
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Picture } from '@element-plus/icons-vue'

const props = withDefaults(defineProps<{
    data: {
        title?: string
        src?: string
        alt?: string
        description?: string
        style?: {
            backgroundColor?: string
            padding?: string
            textAlign?: 'left' | 'center' | 'right'
            imageWidth?: string
            imageHeight?: string
            objectFit?: 'contain' | 'cover' | 'fill' | 'scale-down'
            borderRadius?: string
            titleColor?: string
            descriptionColor?: string
        }
    }
}>(), {
    data: () => ({
        style: {}
    })
})

const emit = defineEmits<{
    imageLoad: [event: Event]
    imageError: [event: Event]
}>()

const containerStyle = computed(() => ({
    backgroundColor: props.data.style?.backgroundColor || '#ffffff',
    padding: props.data.style?.padding || '20px',
    textAlign: props.data.style?.textAlign || 'center',
    minHeight: '200px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center'
}))

const titleStyle = computed(() => ({
    color: props.data.style?.titleColor || '#333333',
    fontSize: '20px',
    fontWeight: 'bold',
    marginBottom: '16px',
    textAlign: props.data.style?.textAlign || 'center'
}))

const imageWrapperStyle = computed(() => ({
    display: 'inline-block',
    maxWidth: '100%',
    marginBottom: props.data.description ? '16px' : '0'
}))

const imageStyle = computed(() => ({
    width: props.data.style?.imageWidth || 'auto',
    height: props.data.style?.imageHeight || 'auto',
    maxWidth: '100%',
    maxHeight: '400px',
    objectFit: props.data.style?.objectFit || 'contain',
    borderRadius: props.data.style?.borderRadius || '4px',
    display: 'block'
}))

const placeholderStyle = computed(() => ({
    width: props.data.style?.imageWidth || '300px',
    height: props.data.style?.imageHeight || '200px',
    backgroundColor: '#f5f7fa',
    border: '2px dashed #dcdfe6',
    borderRadius: props.data.style?.borderRadius || '4px',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    color: '#c0c4cc'
}))

const descriptionStyle = computed(() => ({
    color: props.data.style?.descriptionColor || '#666666',
    fontSize: '14px',
    lineHeight: '1.5',
    textAlign: props.data.style?.textAlign || 'center'
}))

const handleImageLoad = (event: Event) => {
    emit('imageLoad', event)
}

const handleImageError = (event: Event) => {
    emit('imageError', event)
}
</script>

<style scoped>
.image-template {
    width: 100%;
    height: 100%;
}

.image-content {
    max-width: 100%;
}

.image-title {
    margin: 0 0 16px 0;
}

.image-wrapper {
    margin: 0 0 16px 0;
}

.image-wrapper:last-child {
    margin-bottom: 0;
}

.image-placeholder {
    margin: 0;
}

.image-placeholder p {
    margin: 8px 0 0 0;
    font-size: 14px;
}

.image-description {
    margin: 0;
}
</style>