<template>
    <div class="video-template" :style="containerStyle">
        <div class="video-content">
            <div v-if="data.title" class="video-title" :style="titleStyle">
                {{ data.title }}
            </div>
            
            <div class="video-wrapper" :style="videoWrapperStyle">
                <video
                    v-if="data.src"
                    ref="videoRef"
                    :src="data.src"
                    :poster="data.poster"
                    :controls="data.controls !== false"
                    :autoplay="data.autoplay === true"
                    :loop="data.loop === true"
                    :muted="data.muted === true"
                    :style="videoStyle"
                    @loadstart="handleLoadStart"
                    @loadeddata="handleLoadedData"
                    @error="handleError"
                    @play="handlePlay"
                    @pause="handlePause"
                    @ended="handleEnded"
                >
                    您的浏览器不支持视频播放
                </video>
                <div v-else class="video-placeholder" :style="placeholderStyle">
                    <el-icon size="48"><VideoPlay /></el-icon>
                    <p>请选择视频文件</p>
                </div>
            </div>
            
            <div v-if="data.description" class="video-description" :style="descriptionStyle">
                {{ data.description }}
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { VideoPlay } from '@element-plus/icons-vue'

const props = withDefaults(defineProps<{
    data: {
        title?: string
        src?: string
        poster?: string
        description?: string
        controls?: boolean
        autoplay?: boolean
        loop?: boolean
        muted?: boolean
        style?: {
            backgroundColor?: string
            padding?: string
            textAlign?: 'left' | 'center' | 'right'
            videoWidth?: string
            videoHeight?: string
            borderRadius?: string
            titleColor?: string
            descriptionColor?: string
        }
    }
}>(), {
    data: () => ({
        controls: true,
        autoplay: false,
        loop: false,
        muted: false,
        style: {}
    })
})

const emit = defineEmits<{
    loadStart: [event: Event]
    loadedData: [event: Event]
    error: [event: Event]
    play: [event: Event]
    pause: [event: Event]
    ended: [event: Event]
}>()

const videoRef = ref<HTMLVideoElement>()

const containerStyle = computed(() => ({
    backgroundColor: props.data.style?.backgroundColor || '#ffffff',
    padding: props.data.style?.padding || '20px',
    textAlign: props.data.style?.textAlign || 'center',
    minHeight: '200px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center'
}))

const titleStyle = computed(() => ({
    color: props.data.style?.titleColor || '#333333',
    fontSize: '20px',
    fontWeight: 'bold',
    marginBottom: '16px',
    textAlign: props.data.style?.textAlign || 'center'
}))

const videoWrapperStyle = computed(() => ({
    display: 'inline-block',
    maxWidth: '100%',
    marginBottom: props.data.description ? '16px' : '0'
}))

const videoStyle = computed(() => ({
    width: props.data.style?.videoWidth || '100%',
    height: props.data.style?.videoHeight || 'auto',
    maxWidth: '100%',
    maxHeight: '400px',
    borderRadius: props.data.style?.borderRadius || '4px',
    display: 'block'
}))

const placeholderStyle = computed(() => ({
    width: props.data.style?.videoWidth || '400px',
    height: props.data.style?.videoHeight || '225px',
    backgroundColor: '#f5f7fa',
    border: '2px dashed #dcdfe6',
    borderRadius: props.data.style?.borderRadius || '4px',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    color: '#c0c4cc'
}))

const descriptionStyle = computed(() => ({
    color: props.data.style?.descriptionColor || '#666666',
    fontSize: '14px',
    lineHeight: '1.5',
    textAlign: props.data.style?.textAlign || 'center'
}))

// 事件处理
const handleLoadStart = (event: Event) => emit('loadStart', event)
const handleLoadedData = (event: Event) => emit('loadedData', event)
const handleError = (event: Event) => emit('error', event)
const handlePlay = (event: Event) => emit('play', event)
const handlePause = (event: Event) => emit('pause', event)
const handleEnded = (event: Event) => emit('ended', event)

// 暴露方法
defineExpose({
    play: () => videoRef.value?.play(),
    pause: () => videoRef.value?.pause(),
    getCurrentTime: () => videoRef.value?.currentTime || 0,
    setCurrentTime: (time: number) => {
        if (videoRef.value) {
            videoRef.value.currentTime = time
        }
    },
    getDuration: () => videoRef.value?.duration || 0,
    getVolume: () => videoRef.value?.volume || 1,
    setVolume: (volume: number) => {
        if (videoRef.value) {
            videoRef.value.volume = Math.max(0, Math.min(1, volume))
        }
    }
})
</script>

<style scoped>
.video-template {
    width: 100%;
    height: 100%;
}

.video-content {
    max-width: 100%;
}

.video-title {
    margin: 0 0 16px 0;
}

.video-wrapper {
    margin: 0 0 16px 0;
}

.video-wrapper:last-child {
    margin-bottom: 0;
}

.video-placeholder {
    margin: 0;
}

.video-placeholder p {
    margin: 8px 0 0 0;
    font-size: 14px;
}

.video-description {
    margin: 0;
}
</style>