<template>
  <div class="default-template p-6">
    <div class="text-center py-12">
      <el-icon size="64" class="text-gray-400 mb-4">
        <Document />
      </el-icon>
      <h3 class="text-lg font-medium text-gray-600 mb-2">模板加载中...</h3>
      <p class="text-gray-500">
        模板ID: {{ templateId }}
      </p>
      <p class="text-sm text-gray-400 mt-4">
        如果持续显示此页面，请检查模板配置是否正确
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Document } from '@element-plus/icons-vue'

// Props
const props = defineProps<{
  data?: any
  config?: any
  templateId?: number
}>()

// Events
const emit = defineEmits<{
  change: [data: any]
}>()
</script>

<style scoped>
.default-template {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
