<template>
  <div class="menu-view">
    <div class="menu-header">
      <el-button type="primary" size="small" @click="handleAddPhase">
        <el-icon><Plus /></el-icon>
        添加环节
      </el-button>
    </div>

    <div class="menu-content">
      <el-scrollbar>
        <draggable v-model="phaseList" item-key="id" handle=".drag-handle" @end="handleDragEnd">
          <template #item="{ element }">
            <MenuCell
              :key="element.id"
              :phase="element"
              :active="activePhaseId === element.id"
              @click="handlePhaseClick"
              @delete="handleDeletePhase"
              @edit="handleEditPhase"
            />
          </template>
        </draggable>
      </el-scrollbar>
    </div>

    <!-- 添加环节对话框 -->
    <PhaseModelAlert
      v-model="showAddDialog"
      :courseware-id="coursewareId"
      :api="api"
      @confirm="handleAddConfirm"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { Plus } from '@element-plus/icons-vue'
import draggable from 'vuedraggable'
import MenuCell from './MenuCell.vue'
import PhaseModelAlert from './common/PhaseModelAlert.vue'
import type { PhaseData, CoursewareAPI, MessageAdapter } from '../types'

// Props
const props = defineProps<{
  coursewareId: string
  api: CoursewareAPI
  messageAdapter: MessageAdapter
}>()

// Events
const emit = defineEmits<{
  'phase-click': [phase: PhaseData]
  'phase-add': [phase: PhaseData]
  'phase-delete': [phaseId: string]
  'phase-sort': [phases: PhaseData[]]
}>()

// 响应式数据
const phaseList = ref<PhaseData[]>([])
const activePhaseId = ref<string>()
const showAddDialog = ref(false)
const loading = ref(false)

// 方法
const fetchPhases = async () => {
  try {
    loading.value = true
    const phases = await props.api.fetchTeachPhases(props.coursewareId)
    phaseList.value = phases.sort((a, b) => a.sort - b.sort)
  } catch (error) {
    props.messageAdapter.error('获取教学环节失败')
    console.error('Fetch phases error:', error)
  } finally {
    loading.value = false
  }
}

const handlePhaseClick = (phase: PhaseData) => {
  activePhaseId.value = phase.id
  emit('phase-click', phase)
}

const handleAddPhase = () => {
  showAddDialog.value = true
}

const handleAddConfirm = async (templateData: any) => {
  try {
    const newPhase = await props.api.addTeachPhaseFromTemplate({
      coursewareId: props.coursewareId,
      ...templateData,
      sort: phaseList.value.length + 1
    })

    phaseList.value.push(newPhase)
    props.messageAdapter.success('添加环节成功')
    emit('phase-add', newPhase)
  } catch (error) {
    props.messageAdapter.error('添加环节失败')
    console.error('Add phase error:', error)
  }
}

const handleDeletePhase = async (phase: PhaseData) => {
  try {
    const confirmed = await props.messageAdapter.confirm(
      `确定要删除环节"${phase.stepName}"吗？`,
      '删除确认'
    )

    if (!confirmed) return

    await props.api.deleteTeachPhase(phase.id)

    const index = phaseList.value.findIndex(p => p.id === phase.id)
    if (index > -1) {
      phaseList.value.splice(index, 1)
    }

    props.messageAdapter.success('删除环节成功')
    emit('phase-delete', phase.id)
  } catch (error) {
    props.messageAdapter.error('删除环节失败')
    console.error('Delete phase error:', error)
  }
}

const handleEditPhase = async (phase: PhaseData) => {
  try {
    await props.api.editTeachPhase(phase)
    props.messageAdapter.success('修改环节成功')
  } catch (error) {
    props.messageAdapter.error('修改环节失败')
    console.error('Edit phase error:', error)
  }
}

const handleDragEnd = async () => {
  try {
    // 更新排序
    phaseList.value.forEach((phase, index) => {
      phase.sort = index + 1
    })

    await props.api.sortTeachPhase(phaseList.value)
    props.messageAdapter.success('排序更新成功')
    emit('phase-sort', phaseList.value)
  } catch (error) {
    props.messageAdapter.error('排序更新失败')
    console.error('Sort phases error:', error)
    // 重新获取数据
    await fetchPhases()
  }
}

// 监听coursewareId变化
watch(
  () => props.coursewareId,
  () => {
    if (props.coursewareId) {
      fetchPhases()
    }
  },
  { immediate: true }
)

// 生命周期
onMounted(() => {
  if (props.coursewareId) {
    fetchPhases()
  }
})
</script>

<style lang="scss" scoped>
.menu-view {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.menu-header {
  padding: 16px;
  border-bottom: 1px solid #e4e7ed;
}

.menu-content {
  flex: 1;
  min-height: 0;
}
</style>
