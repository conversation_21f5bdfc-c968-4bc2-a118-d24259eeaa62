import type {
    CoursewareAPIAdapter,
    CoursewareDetail,
    CoursewareListParams,
    CoursewareListResponse,
    PhaseData,
    AddPhaseData,
    EditPhaseData,
    SortPhaseData,
    AddPhaseFromTemplateData,
    PageData,
    EditPageData,
    TemplateListParams,
    TemplateListResponse,
    TemplateDetail,
    UploadOptions,
    UploadResponse,
    UploadSignParams,
    UploadSignResponse,
    PublishResponse,
    ModifyStatusData,
    PreviewData
} from '../types/api'

export interface APIConfig {
    baseURL: string
    timeout?: number
    headers?: Record<string, string>
    withCredentials?: boolean
}

export class DefaultAPIAdapter implements CoursewareAPIAdapter {
    private config: APIConfig
    private requestInstance: any

    constructor(config: APIConfig) {
        this.config = {
            timeout: 10000,
            withCredentials: true,
            ...config
        }
        this.initRequest()
    }

    private initRequest() {
        // 这里可以使用axios或其他HTTP客户端
        // 为了简化，这里使用fetch的封装
        this.requestInstance = this.createRequestInstance()
    }

    private createRequestInstance() {
        return async (url: string, options: RequestInit = {}) => {
            const fullUrl = `${this.config.baseURL}${url}`
            
            const defaultOptions: RequestInit = {
                headers: {
                    'Content-Type': 'application/json',
                    ...this.config.headers
                },
                credentials: this.config.withCredentials ? 'include' : 'same-origin',
                ...options
            }

            try {
                const response = await fetch(fullUrl, defaultOptions)
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`)
                }

                const data = await response.json()
                
                // 假设API返回格式为 { code: 0, data: any, msg: string }
                if (data.code !== 0) {
                    throw new Error(data.msg || 'API请求失败')
                }

                return data.data
            } catch (error) {
                console.error('API请求失败:', error)
                throw error
            }
        }
    }

    // 课件相关
    async fetchCoursewareDetail(coursewareId: string): Promise<CoursewareDetail> {
        return this.requestInstance(`/teaching/coursewareData/${coursewareId}`, {
            method: 'GET'
        })
    }

    async fetchCoursewareList(params?: CoursewareListParams): Promise<CoursewareListResponse> {
        const queryString = params ? '?' + new URLSearchParams(params as any).toString() : ''
        return this.requestInstance(`/teaching/coursewareData${queryString}`, {
            method: 'GET'
        })
    }

    // 教学环节相关
    async fetchTeachPhases(coursewareId: string): Promise<PhaseData[]> {
        return this.requestInstance(`/teaching/coursewareDataStep/list/${coursewareId}`, {
            method: 'GET'
        })
    }

    async addTeachPhase(data: AddPhaseData): Promise<PhaseData> {
        return this.requestInstance('/teaching/coursewareDataStep', {
            method: 'POST',
            body: JSON.stringify(data)
        })
    }

    async editTeachPhase(data: EditPhaseData): Promise<PhaseData> {
        return this.requestInstance('/teaching/coursewareDataStep', {
            method: 'PUT',
            body: JSON.stringify(data)
        })
    }

    async deleteTeachPhase(phaseId: string): Promise<void> {
        return this.requestInstance(`/teaching/coursewareDataStep/${phaseId}`, {
            method: 'DELETE'
        })
    }

    async sortTeachPhase(data: SortPhaseData): Promise<void> {
        return this.requestInstance('/teaching/coursewareDataStep/sort', {
            method: 'PUT',
            body: JSON.stringify(data)
        })
    }

    async addTeachPhaseFromTemplate(data: AddPhaseFromTemplateData): Promise<PhaseData> {
        return this.requestInstance('/teaching/coursewareDataStep/fromTemplate', {
            method: 'POST',
            body: JSON.stringify(data)
        })
    }

    // 教学页相关
    async fetchTeachPageDetail(phaseId: string): Promise<PageData> {
        return this.requestInstance(`/teaching/coursewareDataStepDetails/${phaseId}`, {
            method: 'GET'
        })
    }

    async editTeachPageDetail(data: EditPageData): Promise<PageData> {
        return this.requestInstance('/teaching/coursewareDataStepDetails', {
            method: 'PUT',
            body: JSON.stringify(data)
        })
    }

    // 模板相关
    async fetchTemplateList(params?: TemplateListParams): Promise<TemplateListResponse> {
        const queryString = params ? '?' + new URLSearchParams(params as any).toString() : ''
        return this.requestInstance(`/teaching/templates${queryString}`, {
            method: 'GET'
        })
    }

    async fetchTemplateDetail(templateId: string): Promise<TemplateDetail> {
        return this.requestInstance(`/teaching/templates/${templateId}`, {
            method: 'GET'
        })
    }

    // 文件上传相关
    async uploadFile(file: File, options?: UploadOptions): Promise<UploadResponse> {
        const formData = new FormData()
        formData.append('file', file)
        
        if (options) {
            Object.keys(options).forEach(key => {
                formData.append(key, String(options[key]))
            })
        }

        return this.requestInstance('/common/upload', {
            method: 'POST',
            headers: {
                // 不设置Content-Type，让浏览器自动设置multipart/form-data
            },
            body: formData
        })
    }

    async getUploadSign(params?: UploadSignParams): Promise<UploadSignResponse> {
        const queryString = params ? '?' + new URLSearchParams(params as any).toString() : ''
        return this.requestInstance(`/common/getUpdateSign${queryString}`, {
            method: 'GET'
        })
    }

    // 发布相关
    async publishCourseware(coursewareId: string): Promise<PublishResponse> {
        return this.requestInstance('/teaching/coursewareDataPub/publishData', {
            method: 'POST',
            body: JSON.stringify({ coursewareId })
        })
    }

    async modifyWareMaterielStatus(data: ModifyStatusData): Promise<void> {
        return this.requestInstance('/teaching/materiel/status', {
            method: 'PUT',
            body: JSON.stringify(data)
        })
    }

    // 预览相关
    async getCoursewarePreviewData(coursewareId: string): Promise<PreviewData> {
        return this.requestInstance(`/teaching/courseware/preview/${coursewareId}`, {
            method: 'GET'
        })
    }

    // 工具方法
    updateConfig(newConfig: Partial<APIConfig>) {
        this.config = { ...this.config, ...newConfig }
        this.initRequest()
    }

    getConfig(): APIConfig {
        return { ...this.config }
    }
}