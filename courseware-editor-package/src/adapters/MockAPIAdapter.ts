import type {
    CoursewareAPIAdapter,
    CoursewareDetail,
    CoursewareListParams,
    CoursewareListResponse,
    PhaseData,
    AddPhaseData,
    EditPhaseData,
    SortPhaseData,
    AddPhaseFromTemplateData,
    PageData,
    EditPageData,
    TemplateListParams,
    TemplateListResponse,
    TemplateDetail,
    UploadOptions,
    UploadResponse,
    UploadSignParams,
    UploadSignResponse,
    PublishResponse,
    ModifyStatusData,
    PreviewData
} from '../types/api'

export class MockAPIAdapter implements CoursewareAPIAdapter {
    private delay = 500 // 模拟网络延迟

    private async mockDelay<T>(data: T): Promise<T> {
        return new Promise(resolve => {
            setTimeout(() => resolve(data), this.delay)
        })
    }

    async fetchCoursewareDetail(coursewareId: string): Promise<CoursewareDetail> {
        return this.mockDelay({
            id: coursewareId,
            name: '示例课件',
            materielId: 'material-001',
            dataTemplateId: 'template-001',
            status: 'draft',
            version: '1.0.0',
            canPublish: true,
            createTime: '2024-01-01 10:00:00',
            updateTime: '2024-01-01 12:00:00'
        })
    }

    async fetchCoursewareList(params?: CoursewareListParams): Promise<CoursewareListResponse> {
        return this.mockDelay({
            list: [
                {
                    id: 'courseware-001',
                    name: '示例课件1',
                    materielId: 'material-001',
                    dataTemplateId: 'template-001',
                    status: 'draft',
                    version: '1.0.0',
                    canPublish: true,
                    createTime: '2024-01-01 10:00:00',
                    updateTime: '2024-01-01 12:00:00'
                },
                {
                    id: 'courseware-002',
                    name: '示例课件2',
                    materielId: 'material-002',
                    dataTemplateId: 'template-002',
                    status: 'published',
                    version: '1.1.0',
                    canPublish: false,
                    createTime: '2024-01-02 10:00:00',
                    updateTime: '2024-01-02 12:00:00'
                }
            ],
            total: 2,
            page: params?.page || 1,
            size: params?.size || 10
        })
    }

    async fetchTeachPhases(coursewareId: string): Promise<PhaseData[]> {
        return this.mockDelay([
            {
                id: 'phase-001',
                stepName: '导入环节',
                templateId: 1,
                templateName: '文本模板',
                sort: 1,
                status: 'edited',
                pageCount: 1
            },
            {
                id: 'phase-002',
                stepName: '新授环节',
                templateId: 2,
                templateName: '图片模板',
                sort: 2,
                status: 'unedited',
                pageCount: 2
            },
            {
                id: 'phase-003',
                stepName: '练习环节',
                templateId: 3,
                templateName: '视频模板',
                sort: 3,
                status: 'edited',
                pageCount: 1
            }
        ])
    }

    async addTeachPhase(data: AddPhaseData): Promise<PhaseData> {
        return this.mockDelay({
            id: `phase-${Date.now()}`,
            stepName: data.stepName,
            templateId: data.templateId || 1,
            templateName: '文本模板',
            sort: data.sort || 1,
            status: 'unedited',
            pageCount: 1
        })
    }

    async editTeachPhase(data: EditPhaseData): Promise<PhaseData> {
        return this.mockDelay({
            id: data.id,
            stepName: data.stepName || '编辑后的环节',
            templateId: data.templateId || 1,
            templateName: '文本模板',
            sort: data.sort || 1,
            status: 'edited',
            pageCount: 1
        })
    }

    async deleteTeachPhase(phaseId: string): Promise<void> {
        return this.mockDelay(undefined)
    }

    async sortTeachPhase(data: SortPhaseData): Promise<void> {
        return this.mockDelay(undefined)
    }

    async addTeachPhaseFromTemplate(data: AddPhaseFromTemplateData): Promise<PhaseData> {
        return this.mockDelay({
            id: `phase-${Date.now()}`,
            stepName: data.stepName || '从模板创建的环节',
            templateId: parseInt(data.templateId),
            templateName: '模板名称',
            sort: 1,
            status: 'unedited',
            pageCount: 1
        })
    }

    async fetchTeachPageDetail(phaseId: string): Promise<PageData> {
        return this.mockDelay({
            stepId: phaseId,
            templateId: 'text-template',
            stepName: '示例环节',
            viewUrl: '/preview/page',
            details: {
                type: 'text',
                content: {
                    title: '示例标题',
                    content: '这是示例内容',
                    style: {
                        backgroundColor: '#ffffff',
                        color: '#333333'
                    }
                },
                config: {}
            },
            tool: {
                assistant: null,
                strokeOrder: null
            },
            notes: {
                type: 'text',
                content: '这是备注内容'
            }
        })
    }

    async editTeachPageDetail(data: EditPageData): Promise<PageData> {
        return this.mockDelay({
            stepId: data.stepId,
            templateId: 'text-template',
            stepName: '编辑后的环节',
            viewUrl: '/preview/page',
            details: data.details,
            tool: data.tool || {},
            notes: data.notes
        })
    }

    async fetchTemplateList(params?: TemplateListParams): Promise<TemplateListResponse> {
        return this.mockDelay({
            list: [
                {
                    id: 'text-template',
                    name: '文本模板',
                    type: 'text',
                    category: 'basic',
                    description: '用于展示文本内容',
                    thumbnail: '',
                    pageCount: 1
                },
                {
                    id: 'image-template',
                    name: '图片模板',
                    type: 'image',
                    category: 'media',
                    description: '用于展示图片内容',
                    thumbnail: '',
                    pageCount: 1
                },
                {
                    id: 'video-template',
                    name: '视频模板',
                    type: 'video',
                    category: 'media',
                    description: '用于播放视频内容',
                    thumbnail: '',
                    pageCount: 1
                }
            ],
            total: 3,
            page: params?.page || 1,
            size: params?.size || 10
        })
    }

    async fetchTemplateDetail(templateId: string): Promise<TemplateDetail> {
        return this.mockDelay({
            id: templateId,
            name: '模板名称',
            type: 'text',
            category: 'basic',
            description: '模板描述',
            thumbnail: '',
            pageCount: 1,
            config: {}
        })
    }

    async uploadFile(file: File, options?: UploadOptions): Promise<UploadResponse> {
        return this.mockDelay({
            url: `https://example.com/uploads/${file.name}`,
            filename: file.name,
            size: file.size,
            type: file.type
        })
    }

    async getUploadSign(params?: UploadSignParams): Promise<UploadSignResponse> {
        return this.mockDelay({
            policy: 'mock-policy',
            signature: 'mock-signature',
            accessKeyId: 'mock-access-key',
            host: 'https://mock-oss.example.com',
            expire: Date.now() + 3600000,
            dir: 'uploads/'
        })
    }

    async publishCourseware(coursewareId: string): Promise<PublishResponse> {
        return this.mockDelay({
            version: '1.0.0',
            isFirstPublish: true,
            publishTime: new Date().toISOString()
        })
    }

    async modifyWareMaterielStatus(data: ModifyStatusData): Promise<void> {
        return this.mockDelay(undefined)
    }

    async getCoursewarePreviewData(coursewareId: string): Promise<PreviewData> {
        const courseware = await this.fetchCoursewareDetail(coursewareId)
        const phases = await this.fetchTeachPhases(coursewareId)
        const pages = await Promise.all(
            phases.map(phase => this.fetchTeachPageDetail(phase.id))
        )

        return {
            courseware,
            phases,
            pages
        }
    }

    // 设置延迟时间
    setDelay(delay: number) {
        this.delay = delay
    }
}