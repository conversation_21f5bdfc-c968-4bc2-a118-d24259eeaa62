// API适配器工厂
import type { CoursewareAPI } from '../types'

export interface APIAdapterConfig {
  baseURL: string
  headers?: Record<string, string>
  timeout?: number
}

export abstract class BaseAPIAdapter implements CoursewareAPI {
  protected config: APIAdapterConfig

  constructor(config: APIAdapterConfig) {
    this.config = config
  }

  // 抽象方法，由具体适配器实现
  abstract fetchCoursewareDetail(id: string): Promise<any>
  abstract fetchTeachPhases(coursewareId: string): Promise<any>
  abstract fetchTeachPageDetail(stepId: string): Promise<any>
  abstract editTeachPageDetail(data: any): Promise<void>
  abstract publishCourseware(id: string): Promise<void>
  abstract addTeachPhase(data: any): Promise<any>
  abstract editTeachPhase(data: any): Promise<void>
  abstract deleteTeachPhase(id: string): Promise<void>
  abstract sortTeachPhase(phases: any[]): Promise<void>
  abstract getUploadSign(): Promise<any>
}

// REST API适配器
export class Rest<PERSON>IAdapter extends BaseAPIAdapter {
  async fetchCoursewareDetail(id: string) {
    // 实现REST API调用
    throw new Error('Method not implemented.')
  }

  async fetchTeachPhases(coursewareId: string) {
    throw new Error('Method not implemented.')
  }

  async fetchTeachPageDetail(stepId: string) {
    throw new Error('Method not implemented.')
  }

  async editTeachPageDetail(data: any) {
    throw new Error('Method not implemented.')
  }

  async publishCourseware(id: string) {
    throw new Error('Method not implemented.')
  }

  async addTeachPhase(data: any) {
    throw new Error('Method not implemented.')
  }

  async editTeachPhase(data: any) {
    throw new Error('Method not implemented.')
  }

  async deleteTeachPhase(id: string) {
    throw new Error('Method not implemented.')
  }

  async sortTeachPhase(phases: any[]) {
    throw new Error('Method not implemented.')
  }

  async getUploadSign() {
    throw new Error('Method not implemented.')
  }
}

// Mock API适配器（用于测试）
export class MockAPIAdapter extends BaseAPIAdapter {
  async fetchCoursewareDetail(id: string) {
    return {
      id,
      name: 'Mock课件',
      materielId: 'mock-material',
      dataTemplateId: 'mock-template',
      status: 'draft',
      version: '1.0.0',
      createTime: new Date().toISOString(),
      updateTime: new Date().toISOString()
    }
  }

  async fetchTeachPhases(coursewareId: string) {
    return [
      {
        id: 'phase-1',
        stepName: '导入环节',
        templateId: 1,
        templateName: '文本模板',
        sort: 1,
        status: 'unedited',
        pageCount: 1
      }
    ]
  }

  async fetchTeachPageDetail(stepId: string) {
    return {
      stepId,
      templateId: '1',
      stepName: 'Mock环节',
      viewUrl: '',
      details: {
        type: 'text',
        content: {},
        config: {}
      },
      tool: {}
    }
  }

  async editTeachPageDetail(data: any) {
    console.log('Mock save:', data)
  }

  async publishCourseware(id: string) {
    console.log('Mock publish:', id)
  }

  async addTeachPhase(data: any) {
    return { ...data, id: 'new-phase-' + Date.now() }
  }

  async editTeachPhase(data: any) {
    console.log('Mock edit phase:', data)
  }

  async deleteTeachPhase(id: string) {
    console.log('Mock delete phase:', id)
  }

  async sortTeachPhase(phases: any[]) {
    console.log('Mock sort phases:', phases)
  }

  async getUploadSign() {
    return { sign: 'mock-sign', token: 'mock-token' }
  }
}