// 主入口文件
export { default as CoursewareEditor } from './components/CoursewareEditorCore.vue'
export { default as CoursewareEditorCore } from './components/CoursewareEditorCore.vue'
export { default as MenuView } from './components/MenuView.vue'
export { default as DataView } from './components/DataView.vue'
export { default as FullscreenPreview } from './components/FullscreenPreview.vue'

// 导出类型定义
export * from './types'

// 导出API适配器
export { DefaultAPIAdapter, RestAPIAdapter, MockAPIAdapter } from './adapters'
export type { APIConfig } from './adapters'

// 导出组件
export * from './components'

// Vue插件安装函数
import type { App } from 'vue'
import CoursewareEditorCore from './components/CoursewareEditorCore.vue'

export default {
  install(app: App) {
    app.component('CoursewareEditor', CoursewareEditorCore)
    app.component('CoursewareEditorCore', CoursewareEditorCore)
  }
}
