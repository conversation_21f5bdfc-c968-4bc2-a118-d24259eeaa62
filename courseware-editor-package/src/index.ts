// 主入口文件
export { default as CoursewareEditor } from './components/CoursewareEditor.vue';
export { default as CoursewareEditorCore } from './components/CoursewareEditorCore.vue';
export { default as MenuView } from './components/MenuView.vue';
export { default as DataView } from './components/DataView.vue';
export { default as FullscreenPreview } from './components/FullscreenPreview.vue';

// 导出类型定义
export * from './types';

// 导出API适配器
export { RestAPIAdapter } from './adapters/RestAPIAdapter';
export { MockAPIAdapter } from './adapters/MockAPIAdapter';

// 导出消息适配器
export { ElementPlusMessageAdapter } from './adapters/ElementPlusMessageAdapter';

// 导出工具函数
export * from './utils';

// 导出Hooks
export * from './composables';

// Vue插件安装函数
import type { App } from 'vue';
import CoursewareEditor from './components/CoursewareEditor.vue';

export default {
    install(app: App) {
        app.component('CoursewareEditor', CoursewareEditor);
        app.component('CoursewareEditorCore', CoursewareEditor);
    },
};
