@import './variables.scss';

// 基础样式重置
* {
  box-sizing: border-box;
}

// 课件编辑器容器样式
.courseware-editor {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  color: var(--ce-text-color-primary);
  background-color: var(--ce-bg-color);

  // 头部样式
  &__header {
    flex-shrink: 0;
    border-bottom: 1px solid var(--ce-border-color);
    background-color: var(--ce-bg-color);
    z-index: $ce-index-top;
  }

  // 主体内容样式
  &__body {
    flex: 1;
    display: flex;
    overflow: hidden;
  }

  // 左侧菜单样式
  &__menu {
    flex-shrink: 0;
    width: 300px;
    border-right: 1px solid var(--ce-border-color);
    background-color: var(--ce-bg-color);
    overflow: hidden;
    transition: $ce-transition-all;

    &--collapsed {
      width: 0;
      border-right: none;
    }
  }

  // 右侧编辑区域样式
  &__content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  // 编辑器工具栏样式
  &__toolbar {
    flex-shrink: 0;
    padding: $ce-spacing-md;
    border-bottom: 1px solid var(--ce-border-color);
    background-color: var(--ce-fill-color-lighter);
  }

  // 编辑器主体样式
  &__editor {
    flex: 1;
    overflow: auto;
    padding: $ce-spacing-lg;
  }

  // 预览区域样式
  &__preview {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: $ce-index-popper;
    background-color: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;

    &-content {
      width: 90%;
      height: 90%;
      background-color: var(--ce-bg-color);
      border-radius: $ce-border-radius-base;
      overflow: hidden;
      box-shadow: $ce-box-shadow-dark;
    }

    &-close {
      position: absolute;
      top: 20px;
      right: 20px;
      color: #fff;
      font-size: 24px;
      cursor: pointer;
      z-index: 1;

      &:hover {
        opacity: 0.8;
      }
    }
  }
}

// 菜单项样式
.courseware-menu-item {
  padding: $ce-spacing-md;
  border-bottom: 1px solid var(--ce-border-color-lighter);
  cursor: pointer;
  transition: $ce-transition-all;
  user-select: none;

  &:hover {
    background-color: var(--ce-fill-color-light);
  }

  &--active {
    background-color: var(--ce-color-primary-light-9);
    border-left: 3px solid var(--ce-color-primary);
  }

  &--edited {
    .courseware-menu-item__status {
      color: var(--ce-color-success);
    }
  }

  &--unedited {
    .courseware-menu-item__status {
      color: var(--ce-color-warning);
    }
  }

  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: $ce-spacing-xs;
  }

  &__title {
    font-weight: 500;
    color: var(--ce-text-color-primary);
    font-size: $ce-font-size-base;
  }

  &__status {
    font-size: $ce-font-size-small;
    font-weight: 400;
  }

  &__template {
    color: var(--ce-text-color-secondary);
    font-size: $ce-font-size-small;
  }

  &__actions {
    opacity: 0;
    transition: $ce-transition-fade;
  }

  &:hover &__actions {
    opacity: 1;
  }
}

// 模板组件通用样式
.template-component {
  width: 100%;
  min-height: 200px;
  border: 1px solid var(--ce-border-color-lighter);
  border-radius: $ce-border-radius-base;
  overflow: hidden;
  transition: $ce-transition-all;

  &:hover {
    border-color: var(--ce-color-primary-light-5);
    box-shadow: $ce-box-shadow-lighter;
  }

  &--active {
    border-color: var(--ce-color-primary);
    box-shadow: 0 0 0 2px var(--ce-color-primary-light-8);
  }
}

// 工具提示样式
.courseware-tooltip {
  background-color: var(--ce-text-color-primary);
  color: #fff;
  padding: $ce-spacing-xs $ce-spacing-sm;
  border-radius: $ce-border-radius-small;
  font-size: $ce-font-size-small;
  white-space: nowrap;
  z-index: $ce-index-popper;
}

// 加载状态样式
.courseware-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: $ce-spacing-xl;
  color: var(--ce-text-color-secondary);

  &__spinner {
    width: 20px;
    height: 20px;
    border: 2px solid var(--ce-border-color);
    border-top-color: var(--ce-color-primary);
    border-radius: 50%;
    animation: courseware-spin 1s linear infinite;
    margin-right: $ce-spacing-sm;
  }
}

@keyframes courseware-spin {
  to {
    transform: rotate(360deg);
  }
}

// 错误状态样式
.courseware-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: $ce-spacing-xl;
  color: var(--ce-color-danger);
  text-align: center;

  &__icon {
    font-size: 48px;
    margin-bottom: $ce-spacing-md;
  }

  &__message {
    font-size: $ce-font-size-base;
    margin-bottom: $ce-spacing-sm;
  }

  &__detail {
    font-size: $ce-font-size-small;
    color: var(--ce-text-color-secondary);
  }
}

// 空状态样式
.courseware-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: $ce-spacing-xl;
  color: var(--ce-text-color-secondary);
  text-align: center;

  &__icon {
    font-size: 48px;
    margin-bottom: $ce-spacing-md;
    opacity: 0.5;
  }

  &__message {
    font-size: $ce-font-size-base;
    margin-bottom: $ce-spacing-sm;
  }

  &__action {
    margin-top: $ce-spacing-md;
  }
}

// 响应式设计
@media (max-width: $ce-md) {
  .courseware-editor {
    &__menu {
      width: 250px;
    }
  }
}

@media (max-width: $ce-sm) {
  .courseware-editor {
    &__menu {
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      z-index: $ce-index-top + 1;
      width: 280px;
      box-shadow: $ce-box-shadow-dark;

      &--collapsed {
        transform: translateX(-100%);
      }
    }

    &__preview {
      &-content {
        width: 95%;
        height: 95%;
      }
    }
  }
}

// 打印样式
@media print {
  .courseware-editor {
    &__header,
    &__menu,
    &__toolbar {
      display: none !important;
    }

    &__content {
      width: 100% !important;
    }

    &__editor {
      padding: 0 !important;
    }
  }
}