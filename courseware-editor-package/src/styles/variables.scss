// 颜色变量
:root {
  // 主色调
  --ce-color-primary: #409eff;
  --ce-color-primary-light-3: #79bbff;
  --ce-color-primary-light-5: #a0cfff;
  --ce-color-primary-light-7: #c6e2ff;
  --ce-color-primary-light-8: #d9ecff;
  --ce-color-primary-light-9: #ecf5ff;
  --ce-color-primary-dark-2: #337ecc;

  // 成功色
  --ce-color-success: #67c23a;
  --ce-color-success-light-3: #95d475;
  --ce-color-success-light-5: #b3e19d;
  --ce-color-success-light-7: #d1edc4;
  --ce-color-success-light-8: #e1f3d8;
  --ce-color-success-light-9: #f0f9eb;
  --ce-color-success-dark-2: #529b2e;

  // 警告色
  --ce-color-warning: #e6a23c;
  --ce-color-warning-light-3: #eebe77;
  --ce-color-warning-light-5: #f3d19e;
  --ce-color-warning-light-7: #f8e3c5;
  --ce-color-warning-light-8: #faecd8;
  --ce-color-warning-light-9: #fdf6ec;
  --ce-color-warning-dark-2: #b88230;

  // 危险色
  --ce-color-danger: #f56c6c;
  --ce-color-danger-light-3: #f89898;
  --ce-color-danger-light-5: #fab6b6;
  --ce-color-danger-light-7: #fcd3d3;
  --ce-color-danger-light-8: #fde2e2;
  --ce-color-danger-light-9: #fef0f0;
  --ce-color-danger-dark-2: #c45656;

  // 信息色
  --ce-color-info: #909399;
  --ce-color-info-light-3: #b1b3b8;
  --ce-color-info-light-5: #c8c9cc;
  --ce-color-info-light-7: #dedfe0;
  --ce-color-info-light-8: #e9e9eb;
  --ce-color-info-light-9: #f4f4f5;
  --ce-color-info-dark-2: #73767a;

  // 文本色
  --ce-text-color-primary: #303133;
  --ce-text-color-regular: #606266;
  --ce-text-color-secondary: #909399;
  --ce-text-color-placeholder: #a8abb2;
  --ce-text-color-disabled: #c0c4cc;

  // 边框色
  --ce-border-color: #dcdfe6;
  --ce-border-color-light: #e4e7ed;
  --ce-border-color-lighter: #ebeef5;
  --ce-border-color-extra-light: #f2f6fc;
  --ce-border-color-dark: #d4d7de;
  --ce-border-color-darker: #cdd0d6;

  // 填充色
  --ce-fill-color: #f0f2f5;
  --ce-fill-color-light: #f5f7fa;
  --ce-fill-color-lighter: #fafafa;
  --ce-fill-color-extra-light: #fafcff;
  --ce-fill-color-dark: #ebedf0;
  --ce-fill-color-darker: #e6e8eb;
  --ce-fill-color-blank: #ffffff;

  // 背景色
  --ce-bg-color: #ffffff;
  --ce-bg-color-page: #f2f3f5;
  --ce-bg-color-overlay: #ffffff;
}

// 尺寸变量
$ce-border-radius-base: 4px;
$ce-border-radius-small: 2px;
$ce-border-radius-round: 20px;
$ce-border-radius-circle: 100%;

// 字体大小
$ce-font-size-extra-large: 20px;
$ce-font-size-large: 18px;
$ce-font-size-medium: 16px;
$ce-font-size-base: 14px;
$ce-font-size-small: 13px;
$ce-font-size-extra-small: 12px;

// 间距
$ce-spacing-xs: 4px;
$ce-spacing-sm: 8px;
$ce-spacing-md: 12px;
$ce-spacing-lg: 16px;
$ce-spacing-xl: 20px;
$ce-spacing-xxl: 24px;

// 阴影
$ce-box-shadow-base: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
$ce-box-shadow-light: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
$ce-box-shadow-lighter: 0 2px 4px 0 rgba(0, 0, 0, 0.12);
$ce-box-shadow-dark: 0 4px 8px 0 rgba(0, 0, 0, 0.12);

// 过渡动画
$ce-transition-duration: 0.3s;
$ce-transition-duration-fast: 0.2s;
$ce-transition-function-ease-in-out-bezier: cubic-bezier(0.645, 0.045, 0.355, 1);
$ce-transition-function-fast-bezier: cubic-bezier(0.23, 1, 0.32, 1);
$ce-transition-all: all $ce-transition-duration $ce-transition-function-ease-in-out-bezier;
$ce-transition-fade: opacity $ce-transition-duration $ce-transition-function-fast-bezier;
$ce-transition-md-fade: transform $ce-transition-duration $ce-transition-function-fast-bezier, opacity $ce-transition-duration $ce-transition-function-fast-bezier;
$ce-transition-fade-linear: opacity $ce-transition-duration-fast linear;
$ce-transition-border: border-color $ce-transition-duration-fast $ce-transition-function-ease-in-out-bezier;
$ce-transition-box-shadow: box-shadow $ce-transition-duration-fast $ce-transition-function-ease-in-out-bezier;
$ce-transition-color: color $ce-transition-duration-fast $ce-transition-function-ease-in-out-bezier;

// 断点
$ce-sm: 768px;
$ce-md: 992px;
$ce-lg: 1200px;
$ce-xl: 1920px;

// Z-index层级
$ce-index-normal: 1;
$ce-index-top: 1000;
$ce-index-popper: 2000;