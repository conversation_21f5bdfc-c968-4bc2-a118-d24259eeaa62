import { templateRegistry } from '../core/TemplateRegistry'
import type { TemplateData } from '../types/template'

// 文本模板配置
const textTemplate: TemplateData = {
    config: {
        id: 'text-template',
        name: '文本模板',
        type: 'text',
        category: 'basic',
        description: '用于展示纯文本内容的基础模板',
        version: '1.0.0',
        author: 'Courseware Editor'
    },
    components: [
        {
            id: 'text-container',
            type: 'div',
            props: {
                class: 'text-template'
            },
            children: [
                {
                    id: 'text-title',
                    type: 'h1',
                    props: {
                        class: 'text-title'
                    }
                },
                {
                    id: 'text-content',
                    type: 'div',
                    props: {
                        class: 'text-content'
                    }
                }
            ]
        }
    ],
    schema: {
        type: 'object',
        properties: {
            title: {
                type: 'string',
                title: '标题',
                description: '文本标题'
            },
            content: {
                type: 'string',
                title: '内容',
                description: '文本内容，支持HTML格式'
            },
            footer: {
                type: 'string',
                title: '页脚',
                description: '页脚文本'
            },
            style: {
                type: 'object',
                title: '样式设置',
                properties: {
                    backgroundColor: {
                        type: 'string',
                        title: '背景颜色',
                        format: 'color',
                        default: '#ffffff'
                    },
                    padding: {
                        type: 'string',
                        title: '内边距',
                        default: '20px'
                    },
                    textAlign: {
                        type: 'string',
                        title: '文本对齐',
                        enum: ['left', 'center', 'right'],
                        default: 'left'
                    },
                    fontSize: {
                        type: 'string',
                        title: '字体大小',
                        default: '16px'
                    },
                    color: {
                        type: 'string',
                        title: '文字颜色',
                        format: 'color',
                        default: '#333333'
                    }
                }
            }
        },
        required: ['content']
    },
    defaultData: {
        title: '标题文本',
        content: '这里是文本内容，可以包含<strong>HTML标签</strong>。',
        style: {
            backgroundColor: '#ffffff',
            padding: '20px',
            textAlign: 'left',
            fontSize: '16px',
            color: '#333333'
        }
    }
}

// 图片模板配置
const imageTemplate: TemplateData = {
    config: {
        id: 'image-template',
        name: '图片模板',
        type: 'image',
        category: 'media',
        description: '用于展示图片内容的模板',
        version: '1.0.0',
        author: 'Courseware Editor'
    },
    components: [
        {
            id: 'image-container',
            type: 'div',
            props: {
                class: 'image-template'
            },
            children: [
                {
                    id: 'image-title',
                    type: 'h2',
                    props: {
                        class: 'image-title'
                    }
                },
                {
                    id: 'image-element',
                    type: 'img',
                    props: {
                        class: 'image-element'
                    }
                },
                {
                    id: 'image-description',
                    type: 'p',
                    props: {
                        class: 'image-description'
                    }
                }
            ]
        }
    ],
    schema: {
        type: 'object',
        properties: {
            title: {
                type: 'string',
                title: '标题',
                description: '图片标题'
            },
            src: {
                type: 'string',
                title: '图片地址',
                description: '图片的URL地址',
                format: 'uri'
            },
            alt: {
                type: 'string',
                title: '替代文本',
                description: '图片的替代文本'
            },
            description: {
                type: 'string',
                title: '描述',
                description: '图片描述文本'
            },
            style: {
                type: 'object',
                title: '样式设置',
                properties: {
                    backgroundColor: {
                        type: 'string',
                        title: '背景颜色',
                        format: 'color',
                        default: '#ffffff'
                    },
                    padding: {
                        type: 'string',
                        title: '内边距',
                        default: '20px'
                    },
                    textAlign: {
                        type: 'string',
                        title: '对齐方式',
                        enum: ['left', 'center', 'right'],
                        default: 'center'
                    },
                    imageWidth: {
                        type: 'string',
                        title: '图片宽度',
                        default: 'auto'
                    },
                    imageHeight: {
                        type: 'string',
                        title: '图片高度',
                        default: 'auto'
                    },
                    objectFit: {
                        type: 'string',
                        title: '图片适应方式',
                        enum: ['contain', 'cover', 'fill', 'scale-down'],
                        default: 'contain'
                    },
                    borderRadius: {
                        type: 'string',
                        title: '圆角',
                        default: '4px'
                    }
                }
            }
        },
        required: ['src']
    },
    defaultData: {
        title: '图片标题',
        src: '',
        alt: '图片',
        description: '这里是图片的描述文本',
        style: {
            backgroundColor: '#ffffff',
            padding: '20px',
            textAlign: 'center',
            imageWidth: 'auto',
            imageHeight: 'auto',
            objectFit: 'contain',
            borderRadius: '4px'
        }
    }
}

// 视频模板配置
const videoTemplate: TemplateData = {
    config: {
        id: 'video-template',
        name: '视频模板',
        type: 'video',
        category: 'media',
        description: '用于播放视频内容的模板',
        version: '1.0.0',
        author: 'Courseware Editor'
    },
    components: [
        {
            id: 'video-container',
            type: 'div',
            props: {
                class: 'video-template'
            },
            children: [
                {
                    id: 'video-title',
                    type: 'h2',
                    props: {
                        class: 'video-title'
                    }
                },
                {
                    id: 'video-element',
                    type: 'video',
                    props: {
                        class: 'video-element',
                        controls: true
                    }
                },
                {
                    id: 'video-description',
                    type: 'p',
                    props: {
                        class: 'video-description'
                    }
                }
            ]
        }
    ],
    schema: {
        type: 'object',
        properties: {
            title: {
                type: 'string',
                title: '标题',
                description: '视频标题'
            },
            src: {
                type: 'string',
                title: '视频地址',
                description: '视频的URL地址',
                format: 'uri'
            },
            poster: {
                type: 'string',
                title: '封面图片',
                description: '视频封面图片URL',
                format: 'uri'
            },
            description: {
                type: 'string',
                title: '描述',
                description: '视频描述文本'
            },
            controls: {
                type: 'boolean',
                title: '显示控制条',
                default: true
            },
            autoplay: {
                type: 'boolean',
                title: '自动播放',
                default: false
            },
            loop: {
                type: 'boolean',
                title: '循环播放',
                default: false
            },
            muted: {
                type: 'boolean',
                title: '静音',
                default: false
            },
            style: {
                type: 'object',
                title: '样式设置',
                properties: {
                    backgroundColor: {
                        type: 'string',
                        title: '背景颜色',
                        format: 'color',
                        default: '#ffffff'
                    },
                    padding: {
                        type: 'string',
                        title: '内边距',
                        default: '20px'
                    },
                    textAlign: {
                        type: 'string',
                        title: '对齐方式',
                        enum: ['left', 'center', 'right'],
                        default: 'center'
                    },
                    videoWidth: {
                        type: 'string',
                        title: '视频宽度',
                        default: '100%'
                    },
                    videoHeight: {
                        type: 'string',
                        title: '视频高度',
                        default: 'auto'
                    },
                    borderRadius: {
                        type: 'string',
                        title: '圆角',
                        default: '4px'
                    }
                }
            }
        },
        required: ['src']
    },
    defaultData: {
        title: '视频标题',
        src: '',
        poster: '',
        description: '这里是视频的描述文本',
        controls: true,
        autoplay: false,
        loop: false,
        muted: false,
        style: {
            backgroundColor: '#ffffff',
            padding: '20px',
            textAlign: 'center',
            videoWidth: '100%',
            videoHeight: 'auto',
            borderRadius: '4px'
        }
    }
}

// 注册所有默认模板
export function registerDefaultTemplates() {
    templateRegistry.registerBatch([
        textTemplate,
        imageTemplate,
        videoTemplate
    ])
}

// 导出模板配置
export {
    textTemplate,
    imageTemplate,
    videoTemplate
}