// 扩展API适配器接口
export interface CoursewareAPIAdapter {
    // 课件相关
    fetchCoursewareDetail(coursewareId: string): Promise<CoursewareDetail>
    fetchCoursewareList(params?: CoursewareListParams): Promise<CoursewareListResponse>
    
    // 教学环节相关
    fetchTeachPhases(coursewareId: string): Promise<PhaseData[]>
    addTeachPhase(data: AddPhaseData): Promise<PhaseData>
    editTeachPhase(data: EditPhaseData): Promise<PhaseData>
    deleteTeachPhase(phaseId: string): Promise<void>
    sortTeachPhase(data: SortPhaseData): Promise<void>
    addTeachPhaseFromTemplate(data: AddPhaseFromTemplateData): Promise<PhaseData>
    
    // 教学页相关
    fetchTeachPageDetail(phaseId: string): Promise<PageData>
    editTeachPageDetail(data: EditPageData): Promise<PageData>
    
    // 模板相关
    fetchTemplateList(params?: TemplateListParams): Promise<TemplateListResponse>
    fetchTemplateDetail(templateId: string): Promise<TemplateDetail>
    
    // 文件上传相关
    uploadFile(file: File, options?: UploadOptions): Promise<UploadResponse>
    getUploadSign(params?: UploadSignParams): Promise<UploadSignResponse>
    
    // 发布相关
    publishCourseware(coursewareId: string): Promise<PublishResponse>
    modifyWareMaterielStatus(data: ModifyStatusData): Promise<void>
    
    // 预览相关
    getCoursewarePreviewData(coursewareId: string): Promise<PreviewData>
}

// 课件详情
export interface CoursewareDetail {
    id: string
    name: string
    materielId: string
    dataTemplateId: string
    status: 'draft' | 'published'
    version: string
    canPublish: boolean
    createTime: string
    updateTime: string
    [key: string]: any
}

// 课件列表参数
export interface CoursewareListParams {
    page?: number
    size?: number
    keyword?: string
    status?: string
    [key: string]: any
}

// 课件列表响应
export interface CoursewareListResponse {
    list: CoursewareDetail[]
    total: number
    page: number
    size: number
}

// 教学环节数据
export interface PhaseData {
    id: string
    stepParent?: string
    stepName: string
    templateId: number
    templateName: string
    sort: number
    status: 'edited' | 'unedited'
    pageCount: number
    [key: string]: any
}

// 添加环节数据
export interface AddPhaseData {
    coursewareId: string
    stepName: string
    templateId?: number
    sort?: number
    [key: string]: any
}

// 编辑环节数据
export interface EditPhaseData {
    id: string
    stepName?: string
    templateId?: number
    sort?: number
    [key: string]: any
}

// 排序环节数据
export interface SortPhaseData {
    coursewareId: string
    phases: Array<{
        id: string
        sort: number
    }>
}

// 从模板添加环节数据
export interface AddPhaseFromTemplateData {
    coursewareId: string
    templateId: string
    stepName?: string
    [key: string]: any
}

// 教学页数据
export interface PageData {
    stepId: string
    templateId: string
    stepName: string
    viewUrl: string
    details: {
        type: string
        content: any
        config: any
    }
    tool: {
        assistant?: any
        strokeOrder?: any
    }
    notes?: {
        type: 'text'
        content: string
    }
    [key: string]: any
}

// 编辑页面数据
export interface EditPageData {
    stepId: string
    details: {
        type: string
        content: any
        config: any
    }
    tool?: {
        assistant?: any
        strokeOrder?: any
    }
    notes?: {
        type: 'text'
        content: string
    }
    [key: string]: any
}

// 模板列表参数
export interface TemplateListParams {
    page?: number
    size?: number
    category?: string
    type?: string
    keyword?: string
    [key: string]: any
}

// 模板列表响应
export interface TemplateListResponse {
    list: TemplateDetail[]
    total: number
    page: number
    size: number
}

// 模板详情
export interface TemplateDetail {
    id: string
    name: string
    type: string
    category: string
    description?: string
    thumbnail?: string
    pageCount: number
    config?: any
    [key: string]: any
}

// 上传选项
export interface UploadOptions {
    type?: 'image' | 'video' | 'audio' | 'document'
    maxSize?: number
    accept?: string[]
    [key: string]: any
}

// 上传响应
export interface UploadResponse {
    url: string
    filename: string
    size: number
    type: string
    [key: string]: any
}

// 上传签名参数
export interface UploadSignParams {
    filename: string
    type?: string
    [key: string]: any
}

// 上传签名响应
export interface UploadSignResponse {
    policy: string
    signature: string
    accessKeyId: string
    host: string
    expire: number
    dir: string
    [key: string]: any
}

// 发布响应
export interface PublishResponse {
    version: string
    isFirstPublish: boolean
    publishTime: string
    [key: string]: any
}

// 修改状态数据
export interface ModifyStatusData {
    id: string
    coursewareId: string
    canPublish: number
    [key: string]: any
}

// 预览数据
export interface PreviewData {
    courseware: CoursewareDetail
    phases: PhaseData[]
    pages: PageData[]
    [key: string]: any
}