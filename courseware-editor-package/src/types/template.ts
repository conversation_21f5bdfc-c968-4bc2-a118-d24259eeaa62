// 模板基础接口
export interface TemplateConfig {
    id: string
    name: string
    type: string
    category: string
    description?: string
    thumbnail?: string
    version: string
    author?: string
    createTime?: string
    updateTime?: string
}

// 模板组件接口
export interface TemplateComponent {
    id: string
    type: string
    props: Record<string, any>
    children?: TemplateComponent[]
    style?: Record<string, any>
    events?: Record<string, Function>
}

// 模板数据接口
export interface TemplateData {
    config: TemplateConfig
    components: TemplateComponent[]
    schema: TemplateSchema
    defaultData?: Record<string, any>
}

// 模板验证规则
export interface TemplateSchema {
    type: 'object'
    properties: Record<string, TemplateFieldSchema>
    required?: string[]
}

export interface TemplateFieldSchema {
    type: 'string' | 'number' | 'boolean' | 'array' | 'object'
    title?: string
    description?: string
    default?: any
    enum?: any[]
    format?: string
    minLength?: number
    maxLength?: number
    minimum?: number
    maximum?: number
    items?: TemplateFieldSchema
    properties?: Record<string, TemplateFieldSchema>
    required?: string[]
}

// 模板注册器接口
export interface TemplateRegistry {
    register(template: TemplateData): void
    unregister(templateId: string): void
    get(templateId: string): TemplateData | undefined
    getAll(): TemplateData[]
    getByCategory(category: string): TemplateData[]
    getByType(type: string): TemplateData[]
}

// 模板渲染器接口
export interface TemplateRenderer {
    render(templateId: string, data: Record<string, any>): Promise<any>
    validate(templateId: string, data: Record<string, any>): Promise<boolean>
    getSchema(templateId: string): TemplateSchema | undefined
}