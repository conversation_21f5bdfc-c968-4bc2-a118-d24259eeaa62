// 核心数据类型定义

export interface CoursewareData {
  id: string
  name: string
  materielId: string
  dataTemplateId: string
  status: 'draft' | 'published'
  version: string
  createTime: string
  updateTime: string
}

export interface PhaseData {
  id: string
  stepParent?: string
  stepName: string
  templateId: number
  templateName: string
  sort: number
  status: 'edited' | 'unedited'
  pageCount: number
}

export interface PageData {
  stepId: string
  templateId: string
  stepName: string
  viewUrl: string
  details: {
    type: string
    content: any
    config: any
  }
  tool: {
    assistant?: any
    strokeOrder?: any
  }
  notes?: {
    type: 'text'
    content: string
  }
}

// API适配器接口
export interface CoursewareAPI {
  // 课件相关
  fetchCoursewareDetail(id: string): Promise<CoursewareData>
  fetchTeachPhases(coursewareId: string): Promise<PhaseData[]>
  fetchTeachPageDetail(stepId: string): Promise<PageData>
  editTeachPageDetail(data: PageData): Promise<void>
  publishCourseware(id: string): Promise<void>

  // 教学环节相关
  addTeachPhase(data: Partial<PhaseData>): Promise<PhaseData>
  editTeachPhase(data: PhaseData): Promise<void>
  deleteTeachPhase(id: string): Promise<void>
  sortTeachPhase(phases: PhaseData[]): Promise<void>

  // 文件上传相关
  getUploadSign(): Promise<any>
}

// 编辑器配置接口
export interface CoursewareEditorConfig {
  coursewareId: string
  api: CoursewareAPI
  playerConfig?: any
  uploadConfig?: any
  templateConfig?: any
}

// 消息适配器接口
export interface MessageAdapter {
  success(message: string): void
  error(message: string): void
  warning(message: string): void
  info(message: string): void
}

// 编辑器状态接口
export interface EditorState {
  pageLoading: boolean
  saveLoading: boolean
  selectMenuData?: PhaseData
  phaseData?: PhaseData
  pageData?: PageData
  menuShow: boolean
  dataShow: boolean
  pageRemarkContent: string
  stepId?: string
}

// 组件事件类型
export interface EditorEvents {
  'phase-click': (phase: PhaseData) => void
  'data-change': (data: any) => void
  'save-success': () => void
  'save-error': (error: Error) => void
  'publish-success': () => void
  'publish-error': (error: Error) => void
}
