import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'
import dts from 'vite-plugin-dts'
import { visualizer } from 'rollup-plugin-visualizer'

export default defineConfig({
  plugins: [
    vue(),
    // 生成TypeScript声明文件
    dts({
      insertTypesEntry: true,
      copyDtsFiles: true,
      include: ['src/**/*'],
      exclude: ['src/**/*.test.*', 'src/**/*.spec.*']
    }),
    // 打包分析工具
    visualizer({
      filename: 'dist/stats.html',
      open: false,
      gzipSize: true
    })
  ],

  build: {
    lib: {
      entry: resolve(__dirname, 'src/index.ts'),
      name: 'CoursewareEditor',
      fileName: format => `courseware-editor.${format}.js`,
      formats: ['es', 'umd', 'cjs']
    },

    rollupOptions: {
      // 外部依赖，不打包到库中
      external: [
        'vue',
        '@vue/runtime-core',
        '@vue/runtime-dom',
        '@vue/reactivity',
        '@vue/shared',
        'element-plus',
        '@element-plus/icons-vue'
      ],

      output: {
        // 为外部依赖提供全局变量名
        globals: {
          vue: 'Vue',
          'element-plus': 'ElementPlus',
          '@element-plus/icons-vue': 'ElementPlusIconsVue'
        },

        // 代码分割
        manualChunks: {
          templates: ['src/components/templates/index.ts'],
          adapters: ['src/adapters/index.ts'],
          utils: ['src/utils/index.ts']
        }
      }
    },

    // 构建优化
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true
      }
    },

    // 生成source map
    sourcemap: true,

    // 目标环境
    target: 'es2015',

    // CSS代码分割
    cssCodeSplit: true
  },

  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  },

  css: {
    preprocessorOptions: {
      scss: {
        additionalData: `@import "@/styles/variables.scss";`
      }
    }
  },

  // 开发服务器配置
  server: {
    port: 3000,
    open: true,
    cors: true
  },

  // 预览服务器配置
  preview: {
    port: 4173,
    open: true
  }
})
