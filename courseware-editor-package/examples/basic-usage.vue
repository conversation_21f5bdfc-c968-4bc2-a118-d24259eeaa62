<template>
  <div class="example-app">
    <h1>课件编辑器使用示例</h1>
    
    <!-- 基本用法 -->
    <div class="example-section">
      <h2>基本用法</h2>
      <CoursewareEditor
        :courseware-id="coursewareId"
        :api="apiAdapter"
        :message="messageAdapter"
        @save="handleSave"
        @publish="handlePublish"
        @error="handleError"
      />
    </div>

    <!-- 自定义配置 -->
    <div class="example-section">
      <h2>自定义配置</h2>
      <CoursewareEditorCore
        :courseware-id="coursewareId"
        :api="apiAdapter"
        :config="editorConfig"
        @phase-change="handlePhaseChange"
        @page-change="handlePageChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  CoursewareEditor, 
  CoursewareEditorCore,
  DefaultAPIAdapter,
  type CoursewareEditorConfig,
  type MessageAdapter
} from '@courseware-editor/core'

// 课件ID
const coursewareId = ref('example-courseware-123')

// API适配器配置
const apiAdapter = new DefaultAPIAdapter({
  baseURL: 'https://api.example.com',
  headers: {
    'Authorization': 'Bearer your-token-here'
  },
  timeout: 10000
})

// 消息适配器
const messageAdapter: MessageAdapter = {
  success: (message: string) => ElMessage.success(message),
  error: (message: string) => ElMessage.error(message),
  warning: (message: string) => ElMessage.warning(message),
  info: (message: string) => ElMessage.info(message)
}

// 编辑器配置
const editorConfig = reactive<CoursewareEditorConfig>({
  coursewareId: coursewareId.value,
  api: apiAdapter,
  playerConfig: {
    autoplay: false,
    controls: true
  },
  uploadConfig: {
    maxSize: 10 * 1024 * 1024, // 10MB
    allowedTypes: ['jpg', 'png', 'gif', 'mp4', 'mp3']
  },
  templateConfig: {
    enableCustomTemplates: true,
    defaultTemplate: 'text'
  }
})

// 事件处理
const handleSave = (data: any) => {
  console.log('保存数据:', data)
  ElMessage.success('保存成功')
}

const handlePublish = (data: any) => {
  console.log('发布数据:', data)
  ElMessage.success('发布成功')
}

const handleError = (error: Error) => {
  console.error('编辑器错误:', error)
  ElMessage.error(`操作失败: ${error.message}`)
}

const handlePhaseChange = (phase: any) => {
  console.log('环节变更:', phase)
}

const handlePageChange = (page: any) => {
  console.log('页面变更:', page)
}
</script>

<style scoped>
.example-app {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.example-section {
  margin-bottom: 40px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 20px;
}

.example-section h2 {
  margin-top: 0;
  color: #303133;
  border-bottom: 1px solid #e4e7ed;
  padding-bottom: 10px;
}
</style>
