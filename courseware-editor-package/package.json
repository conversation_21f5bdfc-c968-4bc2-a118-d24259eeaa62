{"name": "@yuedushufang/courseware-editor", "version": "1.0.0", "description": "约读书房课件编辑器 - 可复用的NPM包", "type": "module", "main": "./dist/courseware-editor.cjs.js", "module": "./dist/courseware-editor.es.js", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/courseware-editor.es.js", "require": "./dist/courseware-editor.cjs.js", "types": "./dist/index.d.ts"}, "./dist/style.css": "./dist/style.css"}, "files": ["dist", "README.md", "CHANGELOG.md"], "scripts": {"dev": "vite", "build": "vue-tsc --noEmit && vite build", "build:watch": "vite build --watch", "preview": "vite preview", "type-check": "vue-tsc --noEmit", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "lint:check": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --ignore-path .gitignore", "format": "prettier --write src/", "format:check": "prettier --check src/", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "docs:dev": "vitepress dev docs", "docs:build": "vitepress build docs", "docs:preview": "vitepress preview docs", "release": "npm run build && npm run test && npm publish", "release:beta": "npm run build && npm run test && npm publish --tag beta", "prepublishOnly": "npm run build && npm run test"}, "keywords": ["courseware", "editor", "vue3", "typescript", "education", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "author": "约读书房技术团队", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/yuedushufang/courseware-editor.git"}, "bugs": {"url": "https://github.com/yuedushufang/courseware-editor/issues"}, "homepage": "https://github.com/yuedushufang/courseware-editor#readme", "peerDependencies": {"vue": "^3.3.0", "element-plus": "^2.4.0", "@element-plus/icons-vue": "^2.1.0"}, "dependencies": {"ajv": "^8.12.0", "lodash-es": "^4.17.21"}, "devDependencies": {"@types/lodash-es": "^4.17.12", "@types/node": "^20.10.0", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-vue": "^4.5.2", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/test-utils": "^2.4.3", "@vue/tsconfig": "^0.5.0", "element-plus": "^2.4.4", "eslint": "^8.56.0", "eslint-plugin-vue": "^9.19.2", "jsdom": "^23.0.1", "prettier": "^3.1.1", "rollup-plugin-visualizer": "^5.12.0", "sass": "^1.69.5", "terser": "^5.26.0", "typescript": "~5.3.0", "vite": "^5.0.10", "vite-plugin-dts": "^3.6.4", "vitepress": "^1.0.0-rc.31", "vitest": "^1.0.4", "vue": "^3.3.11", "vue-tsc": "^1.8.25"}, "engines": {"node": ">=16.0.0", "npm": ">=7.0.0"}}