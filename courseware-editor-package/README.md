# @yuedushufang/courseware-editor

约读书房课件编辑器 - 可复用的NPM包

## 安装

```bash
npm install @yuedushufang/courseware-editor
```

## 快速开始

```vue
<template>
  <CoursewareEditor 
    :config="editorConfig"
    @phase-click="handlePhaseClick"
    @save-success="handleSaveSuccess"
  />
</template>

<script setup>
import { CoursewareEditor, RestAPIAdapter } from '@yuedushufang/courseware-editor'

const editorConfig = {
  api: new RestAPIAdapter({
    baseURL: 'https://your-api.com',
    headers: {
      'Authorization': 'Bearer your-token'
    }
  })
}

const handlePhaseClick = (phase) => {
  console.log('Phase clicked:', phase)
}

const handleSaveSuccess = () => {
  console.log('Save success!')
}
</script>
```

## 开发

```bash
# 安装依赖
npm install

# 开发模式
npm run dev

# 构建
npm run build

# 测试
npm run test

# 代码检查
npm run lint
```

## API文档

详见 [API文档](./docs/api.md)

## 更新日志

详见 [CHANGELOG.md](./CHANGELOG.md)