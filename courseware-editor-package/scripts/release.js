#!/usr/bin/env node

import { execSync } from 'child_process'
import { readFileSync } from 'fs'
import { resolve } from 'path'
import { createInterface } from 'readline'

const packagePath = resolve(process.cwd(), 'package.json')
const pkg = JSON.parse(readFileSync(packagePath, 'utf-8'))

const rl = createInterface({
  input: process.stdin,
  output: process.stdout
})

function question(query) {
  return new Promise(resolve => rl.question(query, resolve))
}

async function release() {
  console.log(`🚀 Preparing to release ${pkg.name} v${pkg.version}`)
  
  try {
    // 检查工作目录是否干净
    console.log('🔍 Checking working directory...')
    const status = execSync('git status --porcelain', { encoding: 'utf-8' })
    if (status.trim()) {
      console.log('⚠️  Working directory is not clean:')
      console.log(status)
      const proceed = await question('Continue anyway? (y/N): ')
      if (proceed.toLowerCase() !== 'y') {
        console.log('❌ Release cancelled')
        process.exit(1)
      }
    }

    // 运行测试
    console.log('🧪 Running tests...')
    execSync('npm test', { stdio: 'inherit' })

    // 构建
    console.log('🏗️  Building...')
    execSync('npm run build', { stdio: 'inherit' })

    // 确认发布
    const confirmRelease = await question(`📦 Ready to publish ${pkg.name}@${pkg.version}. Continue? (y/N): `)
    if (confirmRelease.toLowerCase() !== 'y') {
      console.log('❌ Release cancelled')
      process.exit(1)
    }

    // 发布到npm
    console.log('📤 Publishing to npm...')
    const publishArgs = process.argv.includes('--beta') ? '--tag beta' : ''
    execSync(`npm publish ${publishArgs}`, { stdio: 'inherit' })

    // 创建git标签
    console.log('🏷️  Creating git tag...')
    execSync(`git tag v${pkg.version}`, { stdio: 'inherit' })
    execSync('git push origin --tags', { stdio: 'inherit' })

    console.log('✅ Release completed successfully!')
    console.log(`🎉 ${pkg.name}@${pkg.version} is now available on npm`)

  } catch (error) {
    console.error('❌ Release failed:', error.message)
    process.exit(1)
  } finally {
    rl.close()
  }
}

release()