#!/usr/bin/env node

import { execSync } from 'child_process'
import { readFileSync, writeFileSync } from 'fs'
import { resolve } from 'path'

const packagePath = resolve(process.cwd(), 'package.json')
const pkg = JSON.parse(readFileSync(packagePath, 'utf-8'))

console.log(`🚀 Building ${pkg.name} v${pkg.version}...`)

try {
  // 清理dist目录
  console.log('📦 Cleaning dist directory...')
  execSync('rm -rf dist', { stdio: 'inherit' })

  // 类型检查
  console.log('🔍 Type checking...')
  execSync('vue-tsc --noEmit', { stdio: 'inherit' })

  // 构建
  console.log('🏗️  Building library...')
  execSync('vite build', { stdio: 'inherit' })

  // 生成版本信息
  console.log('📝 Generating version info...')
  const versionInfo = {
    name: pkg.name,
    version: pkg.version,
    buildTime: new Date().toISOString(),
    nodeVersion: process.version,
    platform: process.platform
  }
  
  writeFileSync(
    resolve(process.cwd(), 'dist/version.json'),
    JSON.stringify(versionInfo, null, 2)
  )

  console.log('✅ Build completed successfully!')
  console.log(`📊 Bundle size analysis: dist/stats.html`)
  
} catch (error) {
  console.error('❌ Build failed:', error.message)
  process.exit(1)
}