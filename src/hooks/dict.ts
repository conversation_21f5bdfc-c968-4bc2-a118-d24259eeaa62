import { dict } from '/@/stores/dict';
import { getDicts } from '/@/api/admin/dict';
import { ref, toRefs } from 'vue';

interface DictItem {
    label: string;
    value: string;
    elTagType?: string;
    elTagClass?: string;
    sortOrder: number;
}

interface DictResponse {
    code: number;
    data: {
        label: string;
        value: string;
        listClass?: string;
        cssClass?: string;
        sortOrder: number;
        [key: string]: any;
    }[];
}

/**
 * 获取字典数据
 * @param args 字典类型参数
 * @returns 格式化后的字典引用对象
 */
export function useDict(...args: string[]): any {
    const res = ref<Record<string, DictItem[]>>({});

    return (() => {
        args.forEach((dictType: string) => {
            res.value[dictType] = [];
            const dicts = dict().getDict(dictType);

            if (dicts) {
                res.value[dictType] = dicts;
            } else {
                getDicts(dictType).then((resp: DictResponse) => {
                    res.value[dictType] = resp.data
                        .map((p) => ({
                            label: p.label,
                            value: p.value,
                            elTagType: p.listClass,
                            elTagClass: p.cssClass,
                            sortOrder: p.sortOrder,
                        }))
                        .sort((a, b) => a.sortOrder - b.sortOrder);

                    dict().setDict(dictType, res.value[dictType]);
                });
            }
        });
        return toRefs(res.value);
    })();
}
