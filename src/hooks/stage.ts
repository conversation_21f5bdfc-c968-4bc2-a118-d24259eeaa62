import { computed } from 'vue';
import { useStageStore } from '/@/stores/stage';

/**
 * 阶段数据便捷Hook
 * 提供对全局阶段数据的便捷访问
 */
export function useStageList() {
    // 延迟获取store，确保pinia已初始化
    const getStore = () => useStageStore();

    // 确保数据在访问时已加载
    const ensureDataLoaded = () => {
        const store = getStore();
        if (!store.isLoaded && !store.loading) {
            store.loadStages().catch((error) => {
                console.error('Failed to load stage data:', error);
            });
        }
        return store;
    };

    return {
        // 基础数据 - 使用函数来延迟store访问
        stages: computed(() => {
            const store = ensureDataLoaded();
            return store.stageList;
        }),
        stageOptions: computed(() => {
            const store = ensureDataLoaded();
            return store.stageOptions;
        }),
        stageDictionary: computed(() => {
            const store = ensureDataLoaded();
            return store.stageDictionary;
        }),
        loading: computed(() => getStore().loading),
        isLoaded: computed(() => getStore().isLoaded),

        // 便捷方法
        getStageName: (id: string | number) => {
            const store = ensureDataLoaded();
            return store.getStageName(id);
        },
        getStageNames: (ids: (string | number)[]) => {
            const store = ensureDataLoaded();
            return store.getStageNames(ids);
        },
        getStageId: (name: string) => {
            const store = ensureDataLoaded();
            return store.getStageId(name);
        },

        // 确保数据加载
        ensureLoaded: () => getStore().loadStages(),

        // 强制刷新
        refresh: () => getStore().refreshStages(),
    };
}

/**
 * 专门用于表单选择器的Hook
 * 返回适合el-select使用的选项数据
 */
export function useStageOptions() {
    const { stageOptions, loading, isLoaded } = useStageList();

    return {
        options: stageOptions,
        loading,
        isLoaded,
    };
}

/**
 * 专门用于数据显示的Hook
 * 提供快速的ID到名称转换
 */
export function useStageDisplay() {
    const { getStageName, loading, isLoaded } = useStageList();

    return {
        getStageName,
        loading,
        isLoaded,
    };
}
