// 阿里云播放器样式表
import 'aliyun-aliplayer/build/skins/default/aliplayer-min.css';
import Aliplayer from 'aliyun-aliplayer';
// type SkinLayout =  'bigPlayButton'
const _defaultOptions = {
    width: '100%',
    height: '300px',
    preload: true,
    autoplay: false,
    skinLayoutIgnore: ['controlBar.fullScreenButton'],
    language: 'zh-cn',
    license: {
        domain: import.meta.env.VITE_LICENSE_DOMAIN,
        key: import.meta.env.VITE_ALI_KEY,
    },
};
const mergeOptions = (options = {}) => {
    return Object.assign({}, _defaultOptions, options);
};

export interface PlayerConfig {
    id: String;
    source: string;
    width?: string;
    height?: string;
}

// 创建播放器
const createPlayer = (userOptions: PlayerConfig) => {
    const options = mergeOptions(userOptions);
    console.log('## player options:', options);
    return new Aliplayer(options);
};

// 销毁播放器
export const destroyPlayer = (player: any) => {
    player && player.dispose();
};
export const usePlayer = () => {
    return {
        createPlayer,
        destroyPlayer,
    };
};
