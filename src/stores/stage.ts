import { defineStore } from 'pinia';
import { getStageList } from '/@/api/common';

export interface StageItem {
    id: number;
    stageName: string;
}

export interface StageOption {
    label: string;
    value: number;
}

export interface StageDictItem {
    label: string;
    value: string;
}

export const useStageStore = defineStore('stage', {
    state: () => ({
        stages: [] as StageItem[],
        isLoaded: false,
        loading: false,
    }),

    getters: {
        // 原始数据
        stageList: (state): StageItem[] => state.stages,

        // 选择器格式
        stageOptions: (state): StageOption[] =>
            state.stages.map((item) => ({
                label: item.stageName,
                value: item.id,
            })),

        // 字典格式（兼容现有字典系统）
        stageDictionary: (state): StageDictItem[] =>
            state.stages.map((item) => ({
                label: item.stageName,
                value: String(item.id),
            })),
    },

    actions: {
        // 根据ID获取名称（处理string/number兼容）
        getStageName(id: string | number): string {
            if (!id) return '';
            const numId = typeof id === 'string' ? parseInt(id) : id;
            if (isNaN(numId)) return '';

            const stage = this.stages.find((item) => item.id === numId);
            return stage?.stageName || '';
        },

        // 批量获取名称
        getStageNames(ids: (string | number)[]): string[] {
            return ids.map((id) => this.getStageName(id));
        },

        // 根据名称获取ID
        getStageId(name: string): number | undefined {
            if (!name) return undefined;
            const stage = this.stages.find((item) => item.stageName === name);
            return stage?.id;
        },

        // 初始化数据
        async loadStages(): Promise<void> {
            if (this.isLoaded || this.loading) {
                return;
            }

            try {
                this.loading = true;
                const response = await getStageList();
                this.stages = response.data || [];
                this.isLoaded = true;
            } catch (error) {
                console.error('Failed to load stages:', error);
                throw error;
            } finally {
                this.loading = false;
            }
        },

        // 强制刷新数据
        async refreshStages(): Promise<void> {
            this.isLoaded = false;
            await this.loadStages();
        },
    },
});
