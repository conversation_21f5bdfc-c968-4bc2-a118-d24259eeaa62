/* 页面切换动画
------------------------------- */
.slide-right-enter-active,
.slide-right-leave-active,
.slide-left-enter-active,
.slide-left-leave-active {
    will-change: transform;
    transition: all 0.3s ease;
}
// slide-right
.slide-right-enter-from {
    opacity: 0;
    transform: translateX(-20px);
}
.slide-right-leave-to {
    opacity: 0;
    transform: translateX(20px);
}
// slide-left
.slide-left-enter-from {
    @extend .slide-right-leave-to;
}
.slide-left-leave-to {
    @extend .slide-right-enter-from;
}
// opacitys
.opacitys-enter-active,
.opacitys-leave-active {
    will-change: transform;
    transition: all 0.3s ease;
}
.opacitys-enter-from,
.opacitys-leave-to {
    opacity: 0;
}

/* Breadcrumb 面包屑过渡动画
------------------------------- */
.breadcrumb-enter-active,
.breadcrumb-leave-active {
    transition: all 0.5s ease;
}
.breadcrumb-enter-from,
.breadcrumb-leave-active {
    opacity: 0;
    transform: translateX(20px);
}
.breadcrumb-leave-active {
    position: absolute;
    z-index: -1;
}

/* logo 过渡动画
------------------------------- */
@keyframes logoAnimation {
    0% {
        transform: scale(0);
    }
    80% {
        transform: scale(1.2);
    }
    100% {
        transform: scale(1);
    }
}

/* 404、401 过渡动画
------------------------------- */
@keyframes error-num {
    0% {
        transform: translateY(60px);
        opacity: 0;
    }
    100% {
        transform: translateY(0);
        opacity: 1;
    }
}
@keyframes error-img {
    0% {
        opacity: 0;
    }
    100% {
        opacity: 1;
    }
}
@keyframes error-img-two {
    0% {
        opacity: 1;
    }
    100% {
        opacity: 0;
    }
}

/* 登录页动画
------------------------------- */
@keyframes loginLeft {
    0% {
        left: -100%;
    }
    50%,
    100% {
        left: 100%;
    }
}
@keyframes loginTop {
    0% {
        top: -100%;
    }
    50%,
    100% {
        top: 100%;
    }
}
@keyframes loginRight {
    0% {
        right: -100%;
    }
    50%,
    100% {
        right: 100%;
    }
}
@keyframes loginBottom {
    0% {
        bottom: -100%;
    }
    50%,
    100% {
        bottom: 100%;
    }
}

/* 左右左 link.vue
------------------------------- */
@keyframes toRight {
    0% {
        left: -5px;
    }
    50% {
        left: 100%;
    }
    100% {
        left: -5px;
    }
}
