import request from '/@/utils/request';

//查看
export function fetchTeacherList(query?: Object) {
    return request({
        url: '/permission/LecturerInfo/details',
        method: 'get',
        params: query,
    });
}
//新增
export function addTeacher(data: Object) {
    return request({
        url: '/permission/LecturerInfo/add',
        method: 'post',
        data: data,
    });
}
//修改
export function editTeacher(data: Object) {
    return request({
        url: '/permission/LecturerInfo/edit',
        method: 'put',
        data: data,
    });
}

//分页
export function fetchList(query?: Object) {
    return request({
        url: '/permission/LecturerInfo/page',
        method: 'get',
        params: query,
    });
}

//查询
export function getuser(query?: Object) {
    return request({
        url: '/permission/SysUser/usersByName',
        method: 'get',
        params: query,
    });
}

// 查询主讲老师
export function fetchAllTeacher(status: number = 0) {
    return request({
        url: '/permission/LecturerInfo/list',
        method: 'get',
        params: { status },
    });
}
