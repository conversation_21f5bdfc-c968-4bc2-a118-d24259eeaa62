import request from '/@/utils/request';

/**
 * 查询校区列表
 * @param {Object} [query] - 查询参数。
 * @returns {Promise} 请求的 Promise 分页对象。
 */
export function getSchoolList(query?: Object) {
    return request({
        url: '/store/school/page',
        method: 'get',
        params: query,
    });
}

/**
 * 新增校区信息
 * @param {Object} [obj] - 要添加的对象。
 * @returns {Promise} 请求的 Promise 对象 （true/false）。
 */
export function addSchool(obj?: Object) {
    return request({
        url: '/store/school',
        method: 'post',
        data: obj,
    });
}

/**
 * 修改校区信息
 * @param {Object} [obj] - 要修改的对象。
 * @returns {Promise} 请求的 Promise 对象 （true/false）。
 */
export function updateSchool(obj?: Object) {
    return request({
        url: '/store/school',
        method: 'put',
        data: obj,
    });
}

/**
 * 获取单条校区信息
 * @param {String} [id] - 门店id。
 * @returns {Promise} 获取的门店详情。
 */
export function getSchoolDetail(id: string) {
    return request({
        url: `/store/school/${id}`,
        method: 'get',
    });
}

/**
 * 查询校区门店列表
 * @param {Object} [query] - 查询参数。
 * @returns {Promise} 请求的 Promise 分页对象。
 */
export function getSchoolStoreList(query?: Object) {
    return request({
        url: '/store/campus/page',
        method: 'get',
        params: query,
    });
}

/**
 * 新增校区门店
 * @param {Object} [obj] - 查询参数。
 * @returns {Promise} 请求的 Promise 分页对象。
 */
export function addSchoolStore(obj?: Object) {
    return request({
        url: '/store/campus',
        method: 'post',
        data: obj,
    });
}

/**
 * 修改校区门店
 * @param {Object} [obj] - 查询参数。
 * @returns {Promise} 请求的 Promise 分页对象。
 */
export function updateSchoolStore(obj?: Object) {
    return request({
        url: '/store/campus',
        method: 'put',
        data: obj,
    });
}

/**
 * 获取单条门店信息
 * @param {String} [id] - 门店id。
 * @returns {Promise} 获取的门店详情。
 */
export function getSchoolStoreDetail(id: string) {
    return request({
        url: `/store/campus/${id}`,
        method: 'get',
    });
}

/**
 * 查询学生列表
 * @param {Object} [query] - 查询参数。
 * @returns {Promise} 请求的 Promise 分页对象。
 */
export function getStudentList(query?: Object) {
    return request({
        url: '/store/student/page',
        method: 'get',
        params: query,
    });
}
/**
 * 查询门店员工列表
 * @param {Object} [query] - 查询参数。
 * @returns {Promise} 请求的 Promise 分页对象。
 */
export function getStoreStaffList(query?: Object) {
    return request({
        url: '/store/employee_campus/page',
        method: 'get',
        params: query,
    });
}

/**
 * 新增门店员工
 * @param {Object} [obj] - 查询参数。
 * @returns {Promise} 请求的 Promise 。
 */
export function addStoreStaff(obj?: Object) {
    return request({
        url: '/store/employee_campus/installUser',
        method: 'post',
        data: obj,
    });
}

/**
 * 修改门店员工
 * @param {Object} [obj] - 查询参数。
 * @returns {Promise} 请求的 Promise 。
 */
export function updateStoreStaff(obj?: Object) {
    return request({
        url: '/store/employee_campus/updateUser',
        method: 'put',
        data: obj,
    });
}
/**
 * 更新门店员工禁用/启用状态
 * @param {Object} [obj] - 查询参数。
 * @returns {Promise} 请求的 Promise 。
 */
export function updateStoreStaffStatus(obj?: Object) {
    return request({
        url: '/store/employee_campus/updateIsEnable',
        method: 'put',
        data: obj,
    });
}

/**
 * 门店课时费分页查询
 * @param {Object} [obj] - 查询参数。
 * @returns {Promise} 请求的 Promise 。
 */
export function courseFeeListApi(obj?: any) {
    return request({
        url: `/teaching/courseFee/pageByStoreId/${obj?.id}`,
        method: 'get',
        params: obj,
    });
}

/**
 * 门店课时费修改
 * @param {Object} [obj] - 查询参数。
 * @returns {Promise} 请求的 Promise 。
 */
export function updateFeeByStoreIdApi(obj?: any) {
    return request({
        url: `/teaching/courseFee/updateFeeByStoreId/${obj?.id}`,
        method: 'put',
        data: obj,
    });
}
