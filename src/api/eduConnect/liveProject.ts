import request from '/@/utils/request';

export function fetchList(query?: Object) {
    return request({
        url: '/edusystem/LiveRoomPlanDraft/page',
        method: 'get',
        params: query,
    });
}

export function addObj(obj?: Object) {
    return request({
        url: '/edusystem/LiveRoomPlanDraft/add',
        method: 'post',
        data: obj,
    });
}

export function getObj(id?: string) {
    return request({
        url: '/admin/liveRoomSchedule/' + id,
        method: 'get',
    });
}

export function delObjs(ids?: Object) {
    return request({
        url: '/edusystem/LiveRoomPlanDraft/delete',
        method: 'delete',
        data: ids,
    });
}

export function putObj(obj?: Object) {
    return request({
        url: '/edusystem/LiveRoomPlanDraft/edit',
        method: 'put',
        data: obj,
    });
}
