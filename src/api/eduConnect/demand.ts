import request from '/@/utils/request';

export function fetchList(query?: Object) {
    return request({
        url: '/edusystem/CourseVod/list',
        method: 'get',
        params: query,
    });
}
// 详情
export function detailDemand(query?: Object) {
    return request({
        url: '/edusystem/CourseVod/infoList',
        method: 'get',
        params: query,
    });
}

export function delObjs(query?: Object) {
    return request({
        url: '/edusystem/CourseVod/edit',
        method: 'put',
        data: query,
    });
}
export function getuser(query?: Object) {
    return request({
        url: '/permission/SysUser/usersByName',
        method: 'get',
        params: query,
    });
}

// /course/listByCourseName
export function getlistByCourseName(obj?: Object) {
    return request({
        url: '/teaching/course/listByCourseName',
        method: 'post',
        data: obj,
    });
}

export function getpubListByNameAndId(obj?: Object) {
    return request({
        url: '/teaching/lesson/pubListByNameAndId',
        method: 'post',
        data: obj,
    });
}
