import request from '/@/utils/request';

export function fetchList(query?: Object) {
    return request({
        url: '/edusystem/ssDevice/page',
        method: 'get',
        params: query,
    });
}

export function addObj(obj?: Object) {
    return request({
        url: '/edusystem/ssDevice',
        method: 'post',
        data: obj,
    });
}

export function getObj(id?: string) {
    return request({
        url: '/edusystem/ssDevice/' + id,
        method: 'get',
    });
}

export function delObjs(ids?: Object) {
    return request({
        url: '/edusystem/ssDevice',
        method: 'delete',
        data: ids,
    });
}

export function putObj(obj?: Object) {
    return request({
        url: '/edusystem/ssDevice',
        method: 'put',
        data: obj,
    });
}

export function unbind(obj?: Object) {
    return request({
        url: '/edusystem/ssDevice/unbind',
        method: 'put',
        data: obj,
    });
}
export function changeDeviceState(obj?: Object) {
    return request({
        url: '/edusystem/ssDevice/changeDeviceState',
        method: 'put',
        data: obj,
    });
}

export function uploadLog(obj?: Object) {
    return request({
        url: '/edulive/ssDevice/sendUploadLogsCommand',
        method: 'post',
        data: obj,
    });
}
