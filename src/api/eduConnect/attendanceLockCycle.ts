import request from '/@/utils/request';

/**
 * 获取考勤锁定周期列表
 * @param query 查询参数
 * @returns Promise
 */
export function getCycleList(query?: Object) {
    return request({
        url: '/edusystem/SettlementCycle/list',
        method: 'get',
        params: query,
    });
}

/**
 * 修改考勤锁定周期
 * @param obj 考勤锁定周期对象
 * @returns Promise
 */
export function putObj(obj?: Object) {
    return request({
        url: '/edusystem/SettlementCycle/edit',
        method: 'put',
        data: obj,
    });
}

/**
 * 锁定考勤周期
 * @param id 考勤锁定周期ID
 * @returns Promise
 */
export function lockCycle(id?: string, isLock?: number) {
    return request({
        url: '/edusystem/SettlementCycle/lockCheckin',
        method: 'put',
        data: {
            id,
            checkinLocked: isLock,
        },
    });
}

/**
 * 解锁考勤周期
 * @param id 考勤锁定周期ID
 * @returns Promise
 */
export function unlockCycle(id?: string, isLock?: number) {
    return request({
        url: '/edusystem/SettlementCycle/lockCheckin',
        method: 'put',
        data: {
            id,
            checkinLocked: isLock,
        },
    });
}
