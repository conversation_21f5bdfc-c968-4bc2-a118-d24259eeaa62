import request from '/@/utils/request';

export function fetchList(query?: Object) {
    return request({
        url: '/edusystem/ssClass/page',
        method: 'get',
        params: query,
    });
}

export function addObj(obj?: Object) {
    return request({
        url: '/edusystem/ssClass/add',
        method: 'post',
        data: obj,
    });
}

export function getObj(id?: string) {
    return request({
        url: '/edusystem/ssClass/' + id,
        method: 'get',
    });
}

export function delObjs(ids?: Object) {
    return request({
        url: '/edusystem/ssClass/delete',
        method: 'delete',
        data: ids,
    });
}

export function putObj(obj?: Object) {
    return request({
        url: '/edusystem/ssClass/edit',
        method: 'put',
        data: obj,
    });
}
// 结业
export function finishSchools(obj?: Object) {
    return request({
        url: '/edusystem/ssClass/complete',
        method: 'put',
        data: obj,
    });
}
// 查询班级所有已授权设备信息
export function getAuthDeviceListByClassId(id: string, obj?: Object) {
    return request({
        url: '/edusystem/ssClass/getAuthDeviceListByClassId?classId=' + id,
        method: 'get',
        params: obj,
    });
}
// 查询班级课程安排
export function getPageClassTimeByClassId(params: Record<string, any>) {
    return request({
        url: '/edusystem/ssClass/getPageClassTimeByClassId',
        method: 'get',
        params,
    });
}
// 修改授权门店
export function putClassRoomDeviceList(obj?: Object) {
    return request({
        url: '/edusystem/ssClass/classAuthDevice',
        method: 'put',
        data: obj,
    });
}
// 查询门店
export function getClassRoomDeviceList(obj?: Object) {
    return request({
        url: '/edusystem/ssDevice/getClassRoomDeviceList',
        method: 'get',
        params: obj,
    });
}
