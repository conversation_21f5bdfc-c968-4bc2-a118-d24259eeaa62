import request from '/@/utils/request';

/**
 * 获取课程表分页列表
 * @param {Object} [query] - 查询参数
 * @returns {Promise} 请求的Promise对象，包含课程表分页数据
 */
export function fetchList(query?: Object) {
    return request({
        url: '/edusystem/timetable/page',
        method: 'get',
        params: query,
    });
}

/**
 * 根据ID获取课程表详情
 * @param {string} id - 课程表ID
 * @returns {Promise} 请求的Promise对象，包含课程表详情数据
 */
export function fetchDetail(id: string) {
    return request({
        url: '/edusystem/timetable/getInfo/' + id,
        method: 'get',
    });
}

/**
 * 门店线上补课
 * @param {string} id - 课程表ID
 * @returns {Promise} 请求的Promise对象，包含课程表详情数据
 */
export function onLineCourseConsumptionListApi(data?: Object) {
    return request({
        url: '/edusystem/courseMakeUpOnline/page',
        method: 'get',
        params: data,
    });
}

/**
 * 通过id门店线上补课表分页查询
 * @param {string} id - 课程表ID
 * @returns {Promise} 请求的Promise对象，包含课程表详情数据
 */
export function courseMakeUpOnlineApi(id?: string) {
    return request({
        url: '/edusystem/courseMakeUpOnline/' + id,
        method: 'get',
    });
}

/**
 * 导出门店线上补课表
 * @param {Object} params - 查询参数
 * @returns {Promise} 请求的Promise对象，包含课程表详情数据
 */
export function exportCourseMakeUpOnlineApi(params?: Object) {
    return request({
        url: '/edusystem/courseMakeUpOnline/export',
        method: 'get',
        params: params,
    });
}
