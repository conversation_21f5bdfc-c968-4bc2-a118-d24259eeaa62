import request from '/@/utils/request';

export function fetchList(data?: Object) {
    return request({
        url: '/edusystem/ssClassTime/direct/page',
        method: 'post',
        data,
    });
}

//读书会编辑课次
export function updateClassTime(obj?: Object) {
    return request({
        url: '/edusystem/ssClassTime/direct/updateClassTime',
        method: 'put',
        data: obj,
    });
}

//新增课次
export function addClassTime(obj?: Object) {
    return request({
        url: '/edusystem/ssClassTime/direct/addClassTime',
        method: 'post',
        data: obj,
    });
}

//获取课次详情
export function getClassTimeInfo(id?: string) {
    return request({
        url: '/edusystem/ssClassTime/getClassTimeInfo/' + id,
        method: 'get',
    });
}

export function delObjs(ids?: Object) {
    return request({
        url: '/edusystem/ssClassTime/direct/deleteClassTime',
        method: 'delete',
        data: ids,
    });
}
export function putObj(obj?: Object) {
    return request({
        url: '/edusystem/ssClassTime',
        method: 'put',
        data: obj,
    });
}

//截图
export function ssScreenshotDetail(query?: Object) {
    return request({
        url: '/edusystem/ssScreenshotDetail/page',
        method: 'get',
        params: query,
    });
}

//新增读书会排课表
export function addReading(obj?: Object) {
    return request({
        url: '/edusystem/ssCourseSchedule/reading',
        method: 'post',
        data: obj,
    });
}

//获取直播授权门店列表
export function authRoomList(id: string) {
    return request({
        url: '/edusystem/ssClassTime/direct/authRoomList/' + id,
        method: 'get',
    });
}
//直播课次授权门店
export function authClassTimeRoom(data: Object) {
    return request({
        url: '/edusystem/ssClassTime/direct/authClassTimeRoom',
        method: 'put',
        data,
    });
}
//生成主讲端-教室端上课码
export function genRoomTimeCode(data: Object) {
    return request({
        url: '/edusystem/ssClassTime/genRoomTimeCode',
        method: 'post',
        data,
    });
}

//获取监课链接
export function getSupervisionUrl(query: Object) {
    return request({
        url: '/edusystem/ssClassTime/getSupervisionUrl',
        method: 'get',
        params: query,
    });
}

//监课进入监课端
export function inRoomByRoomUuid(query: Object) {
    return request({
        url: '/edusystem/ssClassTime/inRoomByRoomUuid',
        method: 'get',
        params: query,
    });
}

//同步今日排课到校管家
export function syncToSchoolManager() {
    return request({
        url: '/edusystem/ssClassTime/pushXiaogjMessage',
        method: 'get',
    });
}
