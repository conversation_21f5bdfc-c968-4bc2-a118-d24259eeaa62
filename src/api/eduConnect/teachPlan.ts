import request from '/@/utils/request';

// 已发布的直播间计划
export function fetchPublishLiveRoomPlan() {
    return request({
        url: '/edusystem/LiveRoomPlanVersion/list',
        method: 'get',
    });
}

export function getPublishLiveRoomPlan({
    size = 50,
    current = 1,
    planName = '',
}: {
    size: number;
    current: number;
    planName: string;
}) {
    return request({
        url: '/edusystem/LiveRoomPlanVersion/pub/list',
        method: 'get',
        params: { size, current, planName },
    });
}

// 教学计划列表
export function fetchTeachPlanList(query?: Object) {
    return request({
        url: '/edusystem/TeachingPlanDraft/page',
        method: 'get',
        params: query,
    });
}

// 新增教学计划
export function addTeachPlan(data: Object) {
    return request({
        url: '/edusystem/TeachingPlanDraft/add',
        method: 'post',
        data,
    });
}

// 修改教学计划
export function editTeachPlan(data: Object) {
    return request({
        url: '/edusystem/TeachingPlanDraft/edit',
        method: 'put',
        data,
    });
}

// 删除教学计划
export function deleteTeachPlan(id: string) {
    return request({
        url: '/edusystem/TeachingPlanDraft/delete',
        method: 'delete',
        data: [id],
    });
}

// 发布教学计划
export function publishTeachPlan(id: string, force: boolean) {
    return request({
        url: `/edusystem/TeachingPlanDraft/publish?forcePublish=${force}`,
        method: 'put',
        data: [id],
    });
}

// 教学计划详情
export function fetchTeachPlanInfo(id: string) {
    return request({
        url: '/edusystem/TeachingPlanDraft/details',
        method: 'get',
        params: { id },
    });
}

// 修改教学计划排期
export function editTeachPlanPerioded(data: Object) {
    return request({
        url: '/edusystem/TeachingPlanDetailDraft/edit',
        method: 'put',
        data,
    });
}
