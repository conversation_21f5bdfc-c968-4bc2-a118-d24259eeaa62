import request from '/@/utils/request';

/**
 * 资料分发列表
 * @param {Object} [query] - 查询参数。
 * @returns {Promise} 请求的 Promise 分页对象。
 */
export function informationListApi(query?: Object) {
    return request({
        url: '/edusystem/information/page',
        method: 'get',
        params: query,
    });
}

/**
 * 新增资料目录
 * @param {Object} [query] - 查询参数。
 * @returns {Promise} 请求的 Promise 分页对象。
 */
export function saveInformationApi(data?: Object) {
    return request({
        url: '/edusystem/information/save',
        method: 'post',
        data: data,
    });
}
/**
 * 修改资料目录
 * @param {Object} [query] - 查询参数。
 * @returns {Promise} 请求的 Promise 分页对象。
 */
export function editInformationApi(data?: Object) {
    return request({
        url: '/edusystem/information/edit',
        method: 'put',
        data: data,
    });
}
/**
 * 删除资料管理
 * @param {Object} [query] - 查询参数。
 * @returns {Promise} 请求的 Promise 分页对象。
 */
export function delInformationApi(id?: string) {
    return request({
        url: '/edusystem/information/del/' + id,
        method: 'delete',
    });
}

/**
 * 通过id查询子目录
 * @param {Object} [query] - 查询参数。
 * @returns {Promise} 请求的 Promise 分页对象。
 */
export function childInformationApi(id: string) {
    return request({
        url: '/edusystem/information/child/' + id,
        method: 'get',
    });
}

/**
 * 通过id查询授权列表
 * @param {Object} [query] - 查询参数。
 * @returns {Promise} 请求的 Promise 分页对象。
 */
export function authInformationApi(id: string) {
    return request({
        url: '/edusystem/information/auth/' + id,
        method: 'get',
    });
}

/**
 * 授权门店资料
 * @param {Object} [query] - 查询参数。
 * @returns {Promise} 请求的 Promise 分页对象。
 */
export function saveAuthInformationApi(data: Object) {
    return request({
        url: '/edusystem/information/auth',
        method: 'put',
        data: data,
    });
}

/**
 * 通过id查询资源列表
 * @param {Object} [query] - 查询参数。
 * @returns {Promise} 请求的 Promise 分页对象。
 */
export function resourceInformationApi(data: any) {
    return request({
        url: '/edusystem/informationResource/page/' + data.id,
        method: 'get',
        params: data,
    });
}

/**
 * 获取资料上传临时token
 * @param {Object} [query] - 查询参数。
 * @returns {Promise} 请求的 Promise 分页对象。
 */
export function uploadInformationTokenApi(data: { id: string }) {
    return request({
        url: '/edusystem/information/upload/sts/' + data.id,
        method: 'get',
    });
}

/**
 * 根据目录ID保存资源
 * @param {Object} [query] - 查询参数。
 * @returns {Promise} 请求的 Promise 分页对象。
 */
export function saveUploadInformationApi(
    id: string,
    data: Array<{
        resourceName: string;
        resourceUrl: string;
    }>
) {
    return request({
        url: '/edusystem/informationResource/add/' + id,
        method: 'post',
        data: data,
    });
}

/**
 * 根据资源Id修改资源信息
 * @param {Object} [query] - 查询参数。
 * @returns {Promise} 请求的 Promise 分页对象。
 */
export function editInformationTokenApi(data: Object) {
    return request({
        url: '/edusystem/informationResource/edit',
        method: 'put',
        data,
    });
}

/**
 * 通过id删除资料资源
 * @param {Object} [query] - 查询参数。
 * @returns {Promise} 请求的 Promise 分页对象。
 */
export function delInformationTokenApi(id: string) {
    return request({
        url: '/edusystem/informationResource/del/' + id,
        method: 'delete',
    });
}

/**
 * 通过id查询获得详细信息
 * @param {Object} [query] - 查询参数。
 * @returns {Promise} 请求的 Promise 分页对象。
 */
export function informationInfoApi(id: string) {
    return request({
        url: '/edusystem/informationResource/info/' + id,
        method: 'get',
    });
}

/**
 * 获得一级目录集合
 * @param {Object} [query] - 查询参数。
 * @returns {Promise} 请求的 Promise 分页对象。
 */
export function informationDirectoryApi(data: any) {
    return request({
        url: '/edusystem/information/directory',
        method: 'get',
        params: data,
    });
}
