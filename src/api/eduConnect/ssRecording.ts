import request from '/@/utils/request';

/**
 * 根据分页查询参数获取列表数据。
 * @param {Object} [query] - 查询参数。
 * @returns {Promise} 请求的 Promise 分页对象。
 */
export function fetchList(query?: Object) {
    return request({
        url: '/edusystem/ssRecording/page',
        method: 'get',
        params: query,
    });
}

/**
 * 添加一个新对象。
 * @param {Object} [obj] - 要添加的对象。
 * @returns {Promise} 请求的 Promise 对象 （true/false）。
 */
export function addObj(obj?: Object) {
    return request({
        url: '/edusystem/ssRecording',
        method: 'post',
        data: obj,
    });
}

/**
 * 根据ID获取对象详情。
 * @param {Object} [obj] - 包含ID的对象。
 * @returns {Promise} 请求的 Promise 对象。
 */
export function getObj(obj: any) {
    return request({
        url: `/edusystem/ssRecording/${obj.id}`,
        method: 'get',
    });
}

/**
 * 根据 ID 删除对象。
 * @param {Object} [ids] - 要删除的对象 ID。
 * @returns {Promise} 请求的 Promise 对象。
 */
export function delObjs(ids?: Object) {
    return request({
        url: '/edusystem/ssRecording',
        method: 'delete',
        data: ids,
    });
}

/**
 * 更新一个已存在的对象。
 * @param {Object} [obj] - 要更新的对象。
 * @returns {Promise} 请求的 Promise 对象。
 */
export function putObj(obj?: Object) {
    return request({
        url: '/edusystem/ssRecording',
        method: 'put',
        data: obj,
    });
}

/**
 * 获取下载源。
 * @param {Object} query - 包含ID的查询对象。
 * @returns {Promise} 请求的 Promise 对象。
 */
export function getDownloadSource(query: { id: String }) {
    return request({
        url: `/edusystem/ssRecording/download/${query.id}`,
        method: 'get',
        params: query,
    });
}

/**
 * 验证某个字段的值是否已经存在。
 * @param {Object} rule - 验证规则对象。
 * @param {*} value - 要验证的值。
 * @param {Function} callback - 验证完成后的回调函数。
 * @param {boolean} isEdit - 当前操作是否为编辑。
 */
export function validateExist(rule: any, value: any, callback: any, isEdit: boolean) {
    if (isEdit) {
        return callback();
    }

    getObj({ [rule.field]: value }).then((response) => {
        const result = response.data;
        if (result !== null && result.length > 0) {
            callback(new Error('数据已经存在'));
        } else {
            callback();
        }
    });
}

/**
 * 上传视频记录。
 * @param {Object} obj - 要上传的对象。
 * @returns {Promise} 请求的 Promise 对象。
 */
export function upLoadRecord(obj: Object) {
    return request({
        url: `/edusystem/ssRecording`,
        method: 'post',
        data: obj,
    });
}

/**
 * 获取上传凭证。
 * @param {Object} params - 参数对象，可包含title和fileName。
 * @returns {Promise} 请求的 Promise 对象。
 */
export function getUploadAuth(params?: Object) {
    return request({
        url: '/edusystem/ssRecording/getUploadAuth',
        method: 'get',
        params,
    });
}

/**
 * 刷新视频上传凭证。
 * @param {Object} params - 参数对象，需包含videoId。
 * @returns {Promise} 请求的 Promise 对象。
 */
export function refreshUploadAuth(params?: Object) {
    return request({
        url: '/edusystem/ssRecording/refreshUploadAuth',
        method: 'get',
        params,
    });
}

/**
 * 更新视频信息。
 * @param {Object} data - 要更新的视频信息。
 * @returns {Promise} 请求的 Promise 对象。
 */
export function updateVideoInfo(data?: Object) {
    return request({
        url: '/edusystem/ssRecording/uploadVideo',
        method: 'post',
        data,
    });
}

/**
 * 阿里回调。
 * @param {Object} obj - 回调对象。
 * @returns {Promise} 请求的 Promise 对象。
 */
export function aliBack(obj?: Object) {
    return request({
        url: '/edusystem/ali/vod/callback',
        method: 'post',
        data: obj,
    });
}

/**
 * 新增视频播放凭证。
 * @param {Object} obj - 视频播放凭证对象。
 * @returns {Promise} 请求的 Promise 对象。
 */
export function getVideoPlayToken(obj?: Object) {
    return request({
        url: '/edusystem/ssVideoPlayToken/add',
        method: 'post',
        data: obj,
    });
}

// 为了保持向后兼容，保留旧的函数名，但引用新的函数实现
// 这些函数将在未来版本中废弃，请使用上述新的函数名
/**
 * @deprecated 请使用 getUploadAuth 代替
 */
export function getLoadRecord(obj: Object) {
    return getUploadAuth(obj);
}

/**
 * @deprecated 请使用 refreshUploadAuth 代替
 */
export function refreshRecord(videoId?: String) {
    return refreshUploadAuth({ videoId });
}
