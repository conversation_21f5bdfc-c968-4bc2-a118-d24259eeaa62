import request from '/@/utils/request';

// 请求参数接口
export interface RequestParams {
    schoolId?: number; // 学校ID
    storeId?: number; // 门店ID
    studentId?: number; // 学生ID
    name?: string; // 学员姓名
    phone?: string; // 学员手机号
    courseType?: number; // 课程类型
    feeDate?: string; // 收费日期
    feeType?: number; // 收费类型
    advisorId?: number; // 业绩归属人
    nullify?: number; // 作废状态
    orgId?: number; // 组织ID
}

// 联营门店信息接口
export interface CampusJoint {
    id?: string; // 门店ID
    signingSubject?: string; // 签约主体
    customerName?: string; // 客户名称
    signingSubjectPhone?: string; // 签约主体手机号
    departmentId?: string; // 部门ID
    departmentName?: string; // 部门名称
}

// 联营收费单数据接口
export interface JointBillData {
    id?: string; // 批次号
    studentId?: string; // 学员ID
    name?: string; // 学员姓名
    phone?: string; // 学员手机号
    grade?: number; // 年级
    schoolId?: string; // 校区ID
    storeId?: string; // 门店ID
    gift?: number; // 赠课
    courseType?: number; // 课程类型
    feeDate?: string; // 收费日期
    feeType?: number; // 收费类型
    advisorId?: string; // 业绩归属人
    formal?: number; // 正式课时
    totalAmount?: number; // 金额
    notes?: string; // 备注
    createBy?: string; // 创建人
    createTime?: string; // 创建时间
    updateBy?: string; // 更新人
    updateTime?: string; // 更新时间
    nullify?: number; // 作废状态
}

// 响应数据接口
export interface ResponseData {
    msg?: string; // 返回信息
    code?: number; // 返回标记：成功标记=0，失败标记=1
    data?: {
        records: JointBillData[];
        total: number;
        size: number;
        current: number;
        pages: number;
    };
    errCode?: string; // 错误码
    ok?: boolean;
}

/**
 * 获取联营收费单列表
 * @param {RequestParams} [query] - 查询参数
 * @returns {Promise} 请求的 Promise 分页对象
 */
export function getJointBillList(query?: RequestParams) {
    return request({
        url: '/store/jointBill/page',
        method: 'get',
        params: query,
    });
}

// 重推收费单请求体接口
export interface RetryRequestBody {
    batchNo: string; // id
}

/**
 * 重新推送联营收费单
 * @param {string} batchNo - 收费单批次号/ID
 * @returns {Promise} 请求的 Promise 对象
 */
export function retryJointBill(batchNo: string) {
    return request({
        url: '/store/jointBill/push',
        method: 'post',
        data: {
            batchNo: batchNo,
        },
    });
}
