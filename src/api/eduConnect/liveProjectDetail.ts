import request from '/@/utils/request';
export function fetchList(query?: Object) {
    return request({
        url: '/edusystem/LiveRoomPlanDraft/details',
        method: 'get',
        params: query,
    });
}

export function delObjs(ids?: Object) {
    return request({
        url: '/edusystem/LiveRoomPlanDetailDraft/delete',
        method: 'delete',
        data: ids,
    });
}

export function addObj(obj?: Object) {
    return request({
        url: '/edusystem/LiveRoomPlanDetailDraft/add',
        method: 'post',
        data: obj,
    });
}
export function putObj(obj?: Object) {
    return request({
        url: '/edusystem/LiveRoomPlanDetailDraft/edit',
        method: 'put',
        data: obj,
    });
}

export function publish(ids: number[], forcePublish: boolean = false) {
    return request({
        url: `/edusystem/LiveRoomPlanDraft/publish?forcePublish=${forcePublish}`,
        method: 'put',
        data: ids,
    });
}
export function getTime(obj?: Object) {
    return request({
        url: '/edusystem/ClassTime/list',
        method: 'get',
        data: obj,
    });
}
