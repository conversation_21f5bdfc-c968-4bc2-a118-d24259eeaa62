import request from '/@/utils/request';
// 新增角色列表
export function addPictureBook(data?: Object) {
    return request({
        url: '/teaching/pictureBookRole/add',
        method: 'post',
        data: data,
    });
}
// 获取角色列表
export function getPictureBookList(params?: Object) {
    return request({
        url: '/teaching/pictureBookRole/details',
        method: 'get',
        params: params,
    });
}
// 修改单个角色
export function editPictureBookRole(data?: Object) {
    return request({
        url: '/teaching/pictureBookRole/edit',
        method: 'put',
        data: data,
    });
}
// 修改全部role
export function editAllRole(data?: Object) {
    return request({
        url: '/teaching/pictureBookRole/batchSave',
        method: 'post',
        data: data,
    });
}
