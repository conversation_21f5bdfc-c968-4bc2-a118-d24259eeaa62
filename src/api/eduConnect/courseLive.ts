import request from '/@/utils/request';

/**
 * 获取直播课程分页列表
 * @param {Object} [query] - 查询参数
 * @returns {Promise} 请求的Promise对象，包含直播课程分页数据
 */
export function fetchList(query?: Object) {
    return request({
        url: '/edujw/courseLive/live/page',
        method: 'get',
        params: query,
    });
}

/**
 * 获取直播课程详情列表
 * @param {Object} [query] - 查询参数
 * @returns {Promise} 请求的Promise对象，包含直播课程详情数据
 */
export function fetchDetailList(query?: Object) {
    return request({
        url: '/edujw/timetable/live/details',
        method: 'get',
        params: query,
    });
}
