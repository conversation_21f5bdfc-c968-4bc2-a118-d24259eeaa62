import request from '/@/utils/request';

export function getSsClassList(query?: Object) {
    return request({
        url: '/edusystem/ssClass/getSsClassList',
        method: 'get',
        params: query,
    });
}
//主讲老师
export function getLecturerList(query?: Object) {
    return request({
        url: '/store/lecturer/list',
        method: 'get',
        params: query,
    });
}

//校区
export function getCampusList(query?: Object) {
    return request({
        url: 'store/campus/list',
        method: 'get',
        params: query,
    });
}
//根据校区id查询教室
export function classRoomByCampusId(id?: string) {
    return request({
        url: 'store/classRoom/infoByCampusId/' + id,
        method: 'get',
    });
}

//查看讲师空闲时间(读书会)
export function selectLiveLecturerFreeTime(query?: Object) {
    return request({
        url: '/edusystem/ssClassTime/selectLiveLecturerFreeTime',
        method: 'get',
        params: query,
    });
}
//查看教室空闲时间(读书会)
export function classRoomFreeTimeCalendar(query?: Object) {
    return request({
        url: '/edusystem/ssClassTime/classRoomFreeTimeCalendar',
        method: 'post',
        data: query,
    });
}
