import request from '/@/utils/request';

export function ssCourseSchedule(obj?: Object) {
    return request({
        url: '/edusystem/ssCourseSchedule',
        method: 'post',
        data: obj,
    });
}
//新增点播课排课表
export function addVod(obj?: Object) {
    return request({
        url: '/edusystem/ssCourseSchedule/vod',
        method: 'post',
        data: obj,
    });
}
//点播课列表
export function fetchVodList(data?: Object) {
    return request({
        url: '/edusystem/ssClassTime/vod/page',
        method: 'post',
        data,
    });
}
//获取点播授权门店列表
export function authVodRoomList(id: string) {
    return request({
        url: '/edusystem/ssClassTime/vod/authRoomList/' + id,
        method: 'get',
    });
}
//点播课次授权门店
export function authVodClassTimeRoom(data: Object) {
    return request({
        url: '/edusystem/ssClassTime/vod/authClassTimeRoom',
        method: 'put',
        data,
    });
}

//删除
export function delObjs(ids?: Object) {
    return request({
        url: '/edusystem/ssClassTime/vod/deleteClassTime',
        method: 'delete',
        data: ids,
    });
}

//通过id查询视频详情
export function ssRecording(id: string) {
    return request({
        url: '/edusystem/ssRecording/' + id,
        method: 'get',
    });
}
