import request from '/@/utils/request';

/**
 * 获取录课任务列表
 * @param query 查询参数
 * @returns Promise
 */
export function fetchList(query?: Object) {
    return request({
        url: '/edusystem/recordVideoTask/getRecordVideoTaskList',
        method: 'get',
        params: query,
    });
}

/**
 * 获取录课任务详情
 * @param id 录课任务ID
 * @returns Promise
 */
export function getObj(id?: string) {
    return request({
        url: '/edusystem/recordVideoTask/' + id,
        method: 'get',
    });
}

// 已删除重复的getStageList函数，请使用 /@/api/common 中的版本

/**
 * 关联视频
 * @param data 关联数据
 * @returns Promise
 */
export function associateVideo(data?: Object) {
    return request({
        url: '/edusystem/recordVideoTask/matchResources',
        method: 'put',
        data,
    });
}

/**
 * 根据关键字搜索视频
 * @param query 查询参数
 * @returns Promise
 */
export function searchVideos(query?: Object) {
    return request({
        url: '/edusystem/ssRecording/list',
        method: 'get',
        params: query,
    });
}

/**
 * 生成录课任务
 * @param data 生成录课任务数据
 * @returns Promise
 */
export function generateTask() {
    return request({
        url: '/edusystem/recordVideoTask/saveRecordVideoTask',
        method: 'post',
    });
}
