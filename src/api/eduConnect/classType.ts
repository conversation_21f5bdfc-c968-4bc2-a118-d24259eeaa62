import request from '/@/utils/request';

/**
 * 查询课程类型列表
 * @param {Object} [query] - 查询参数。
 * @returns {Promise} 请求的 Promise 分页对象。
 */
export function getCourseType(query?: Object) {
    return request({
        url: '/teaching/courseType/page',
        method: 'get',
        params: query,
    });
}

/**
 * 新增课程类型
 * @param {Object} [data] - 查询参数。
 * @returns {Promise} 请求的 Promise 分页对象。
 */
export function addCoureTypeApi(data?: Object) {
    return request({
        url: '/teaching/courseType/add',
        method: 'post',
        data: data,
    });
}

/**
 * 通过条件查询课程类型
 * @param {Object} [data] - 查询参数。
 * @returns {Promise} 请求的 Promise 分页对象。
 */
export function coureTypeDetailsApi(query?: Object) {
    return request({
        url: '/teaching/courseType/details',
        method: 'get',
        params: query,
    });
}

/**
 * 修改课程类型
 * @param {Object} [data] - 查询参数。
 * @returns {Promise} 请求的 Promise 分页对象。
 */
export function editCoureTypeApi(data?: Object) {
    return request({
        url: '/teaching/courseType/edit',
        method: 'put',
        data: data,
    });
}

/**
 * 查询所有可用课程类型
 * @param {Object} [query] - 查询参数。
 * @returns {Promise} 请求的 Promise 分页对象。
 */
export function allCourseTypeApi(query?: Object) {
    return request({
        url: '/teaching/courseType/all',
        method: 'get',
        params: query,
    });
}

/**
 * 查询所有课程类型
 * @param {Object} [query] - 查询参数。
 * @returns {Promise} 请求的 Promise 分页对象。
 */
export function allOKCourseTypeApi(query?: Object) {
    return request({
        url: '/teaching/courseType/allOK',
        method: 'get',
        params: query,
    });
}

/**
 * 获取课程授权校区
 * @param {Object} [query] - 查询参数。
 * @returns {Promise} 请求的 Promise 分页对象。
 */
export function getAuthStoreApi(query?: Object) {
    return request({
        url: '/teaching/course/getAuthStore',
        method: 'get',
        params: query,
    });
}

/**
 *根据ID查询课程
 * @param {Object} [query] - 查询参数。
 * @returns {Promise} 请求的 Promise 分页对象。
 */
export function getCourseByIdApi(query?: Object) {
    return request({
        url: '/teaching/course/get',
        method: 'get',
        params: query,
    });
}

/**
 *根据门店获得当前执行信息
 * @param {Object} [query] - 查询参数。
 * @returns {Promise} 请求的 Promise 分页对象。
 */
export function getCurrentCourseFeeByStoreApi(query?: any) {
    return request({
        url: `/teaching/courseFee/getCurrentCourseFeeByStore/${query?.id}`,
        method: 'get',
        params: query,
    });
}

/**
 *根据门店修改自定义课时费
 * @param {Object} [data] - 查询参数。
 * @returns {Promise} 请求的 Promise 分页对象。
 */
export function updateCustomizeFeeByStoreIdApi(data?: any) {
    return request({
        url: `/teaching/courseFee/updateCustomizeFeeByStoreId/${data?.id}`,
        method: 'put',
        data: data,
    });
}

/**
 *根据门店修改自定义课时费
 * @param {Object} [data] - 查询参数。
 * @returns {Promise} 请求的 Promise 分页对象。
 */
export function updateCourseFeeByStoreIdApi(data?: any) {
    return request({
        url: `/teaching/courseFee/updateCourseFeeByStoreId/${data?.id}`,
        method: 'put',
        data: data,
    });
}

/**
 *根据门店查询当前执行列表
 * @param {Object} [data] - 查询参数。
 * @returns {Promise} 请求的 Promise 分页对象。
 */
export function getCourseFeeListByStoreApi(data?: { courseId: string; id: string }) {
    return request({
        url: `/teaching/courseFee/getCourseFeeListByStore/${data?.id}/${data?.courseId}`,
        method: 'get',
    });
}
