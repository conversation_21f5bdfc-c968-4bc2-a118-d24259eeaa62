import request from '/@/utils/request';

// 已删除 fetchStageList 函数，请使用 /@/hooks/stage 中的 useStageList

// 书名列表
export function fetchBookNameList(query?: Object) {
    return request({
        url: 'teaching/bookName/page',
        method: 'get',
        params: query,
    });
}

// 书名查询
export function fetchBookNames(query?: Object) {
    return request({
        url: 'teaching/bookName/details',
        method: 'get',
        params: query,
    });
}

//课件查询
export function coursewareNames(query?: Object) {
    return request({
        url: 'teaching/courseware/listByName',
        method: 'post',
        data: query,
    });
}

// 添加书名
export function addBookName(obj?: Object) {
    return request({
        url: 'teaching/bookName/add',
        method: 'post',
        data: obj,
    });
}

// 编辑书名
export function editBookName(obj?: Object) {
    return request({
        url: 'teaching/bookName/edit',
        method: 'put',
        data: obj,
    });
}

// 删除书名
export function deleteBookName(bookId: string, stageId: string) {
    return request({
        url: 'teaching/bookName/delete',
        method: 'delete',
        data: { bookId, stageId },
    });
}

// 版本列表
export function fetchBookVersionList(query?: Object) {
    return request({
        url: 'teaching/bookVersion/page',
        method: 'get',
        params: query,
    });
}

// 版本列表, 不分页
export function fetchBookVersions(query?: Object) {
    return request({
        url: 'teaching/bookVersion/details',
        method: 'get',
        params: query,
    });
}

// 版本详情
export function bookVersionDetail(id: string) {
    return request({
        url: 'teaching/bookVersion/get/' + id,
        method: 'get',
    });
}

// 添加版本
export function addBookVersion(obj?: Object) {
    return request({
        url: 'teaching/bookVersion/add',
        method: 'post',
        data: obj,
    });
}

// 编辑版本
export function editBookVersion(obj?: Object) {
    return request({
        url: 'teaching/bookVersion/edit',
        method: 'put',
        data: obj,
    });
}

// 删除版本
export function deleteBookVersion(bookId: string, bookVersionId: string) {
    return request({
        url: 'teaching/bookVersion/delete',
        method: 'delete',
        data: { bookId, bookVersionId },
    });
}
