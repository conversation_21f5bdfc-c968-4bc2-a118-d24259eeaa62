import request from '/@/utils/request';

/**
 * 获取课件书名列表
 * @param query 查询参数
 * @returns 返回书名列表数据
 */
export function fetchWareBookNameList(query?: Object) {
    return request({
        url: 'teaching/bookName/details',
        method: 'get',
        params: query,
    });
}

/**
 * 获取课件列表
 * @param query 查询参数，包含bookId
 * @returns 返回课件列表数据
 */
export function fetchCoursewareList(query: any) {
    return request({
        url: 'teaching/courseware/page/' + (query.bookId ?? ''),
        method: 'get',
    });
}

/**
 * 获取课件详情
 * @param id 课件ID
 * @returns 返回课件详细信息
 */
export function fetchCoursewareDetail(id: string) {
    return request({
        url: 'teaching/courseware/detail',
        method: 'get',
        params: { id },
    });
}

/**
 * 添加新课件
 * @param data 课件信息，包含课件名称、关联书籍等
 * @returns 返回添加结果
 */
export function addCourseware(data?: Object) {
    return request({
        url: 'teaching/courseware/add',
        method: 'post',
        data,
    });
}

/**
 * 修改课件信息
 * @param data 课件信息，包含id、课件名称等
 * @returns 返回修改结果
 */
export function editCourseware(data?: Object) {
    return request({
        url: 'teaching/courseware/edit',
        method: 'put',
        data,
    });
}

/**
 * 获取课件资料列表
 * @param query 查询参数，包含coursewareId
 * @returns 返回课件资料列表，包含courseware和other两部分
 */
export function fetchWareMaterielList(query?: Object) {
    return request({
        url: 'teaching/coursewareData/list',
        method: 'get',
        params: query,
    });
}

/**
 * 添加课件资料
 * @param data 资料信息，包含coursewareId、dataTemplateId等
 * @returns 返回添加结果
 */
export function wareMaterielAdd(data?: Object) {
    return request({
        url: 'teaching/coursewareData/add',
        method: 'post',
        data,
    });
}

/**
 * 修改课件资料
 * @param data 资料信息，包含id、coursewareId、dataTemplateId、path、fileName、details等
 * @returns 返回修改结果
 */
export function wareMaterielEdit(data?: Object) {
    return request({
        url: 'teaching/coursewareData/edit',
        method: 'put',
        data,
    });
}

/**
 * 修改课件资料发布状态
 * @param data 发布状态信息，包含id和发布状态
 * @returns 返回修改结果
 */
export function modifyWareMaterielStatus(data?: Object) {
    return request({
        url: 'teaching/coursewareData/setPublishStatus',
        method: 'post',
        data,
    });
}

/**
 * 更改课件资料的门店可见状态
 * @param data 包含id和open状态，控制资料是否对门店可见
 * @returns 返回修改结果
 */
export function modifyWareMaterielStoreStatus(data?: Object) {
    return request({
        url: '/teaching/coursewareData/setOpenStore',
        method: 'post',
        data,
    });
}

/**
 * 更改课件资料的门店下载状态
 * @param data 包含id和download状态，控制资料是否允许门店下载
 * @returns 返回修改结果
 */
export function modifyWareMaterielStoreDownloadStatus(data?: Object) {
    return request({
        url: '/teaching/coursewareData/setAllowDownload',
        method: 'post',
        data,
    });
}

/**
 * 获取所有教学环节
 * @param coursewareId 关联课件ID
 * @param coursewareDataId 课件内容Id
 * @returns 返回教学环节列表
 */
export function fetchTeachPhases(coursewareId: string, coursewareDataId: string) {
    return request({
        url: 'teaching/coursewareStep/list',
        method: 'get',
        params: { coursewareId, coursewareDataId },
    });
}

/**
 * 编辑教学环节
 * @param data 环节信息，包含id、名称、描述等
 * @returns 返回编辑结果
 */
export function editTeachPhase(data?: Object) {
    return request({
        url: 'teaching/coursewareStep/edit',
        method: 'put',
        data,
    });
}

/**
 * 教学环节排序
 * @param data 包含排序后的环节列表
 * @returns 返回排序结果
 */
export function sortTeachPhase(data?: Object) {
    return request({
        url: 'teaching/coursewareStep/editList',
        method: 'put',
        data,
    });
}

/**
 * 删除教学环节
 * @param id 环节和页面ID
 * @param coursewareId 课件ID
 * @returns 返回删除结果
 */
export function deleteTeachPhase(id: string, coursewareId: string) {
    return request({
        url: 'teaching/coursewareStep/delete',
        method: 'delete',
        data: { id, coursewareId },
    });
}

/**
 * 新建教学环节
 * @param data 环节信息，包含名称、课件ID、课件内容ID等
 * @returns 返回创建结果
 */
export function addTeachPhase(data?: Object) {
    return request({
        url: 'teaching/coursewareStep/add',
        method: 'post',
        data,
    });
}

/**
 * 教学页预览
 * @param data 预览参数，包含课件ID、环节ID等
 * @returns 返回教学页预览数据
 */
export function coursewarePreview(data?: Object) {
    return request({
        url: 'teaching/coursewareStep/view',
        method: 'get',
        params: data,
    });
}

/**
 * 从模版新建教学环节
 * @param data 模板和目标信息，包含模板ID、课件ID等
 * @returns 返回创建结果
 */
export function addTeachPhaseFromTemplate(data?: Object) {
    return request({
        url: 'teaching/coursewareStep/addByTemplate',
        method: 'post',
        data,
    });
}

/**
 * 获取教学环节模版列表
 * @returns 返回模板列表数据
 */
export function fetchTeachPhaseTemplate() {
    return request({
        url: 'teaching/coursewareStepTemplate/list',
        method: 'get',
    });
}

/**
 * 获取教学环节模版详情
 * @param id 模板ID
 * @returns 返回模板详细信息
 */
export function fetchTeachPhaseTemplateInfo(id: string) {
    return request({
        url: 'teaching/coursewareStepTemplate/details',
        method: 'get',
        params: { id },
    });
}

/**
 * 新增教学环节模版
 * @param data 模板信息，包含名称、详情等
 * @returns 返回创建结果
 */
export function addTeachPhaseTemplate(data?: Object) {
    return request({
        url: 'teaching/coursewareStepTemplate/add',
        method: 'post',
        data,
    });
}

/**
 * 获取教学环节和教学页详情
 * @param coursewareId 课件ID
 * @param coursewareDataId 课件内容Id
 * @param stepId 教学页所属环节ID
 * @returns 返回教学环节和教学页详情
 */
export function fetchTeachPageDetail(coursewareId: string, coursewareDataId: string, stepId: string) {
    return request({
        url: 'teaching/coursewareDataStepDetails',
        method: 'get',
        params: { coursewareId, coursewareDataId, stepId },
    });
}

/**
 * 修改教学环节和教学页详情
 * @param data 修改信息，包含课件ID、环节ID、页面内容等
 * @returns 返回修改结果
 */
export function editTeachPageDetail(data?: Object) {
    return request({
        url: 'teaching/coursewareDataStepDetails',
        method: 'put',
        data,
    });
}

/**
 * 发布课件
 * @param coursewareId 课件ID
 * @returns 返回发布结果，包含版本号和是否为首次发布等信息
 */
export function publishCourseware(coursewareId: string) {
    return request({
        url: 'teaching/coursewareDataPub/publishData',
        method: 'post',
        data: { coursewareId },
    });
}

/**
 * 修改资料数据和教学环节详情
 * @param data 包含课件ID、环节ID、页面内容等
 * @returns 返回修改结果
 */
export function coursewareDataStepDetails(data: Object) {
    return request({
        url: 'teaching/coursewareDataStepDetails',
        method: 'put',
        data: data,
    });
}

/**
 * 更新课件是否生成录课任务状态
 * @param data 包含id(课件ID)、version(版本)和genRecordTask(是否生成录课任务：1是，0否)
 * @returns 返回更新结果
 */
export function updateRecordTaskStatus(data: { id: string; version: string; genRecordTask: number }) {
    return request({
        url: 'teaching/coursewareDataPub/updateRecordTaskStatus',
        method: 'put',
        data,
    });
}
