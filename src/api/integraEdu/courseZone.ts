import request from '/@/utils/request';

// 课程列表, 分页
export function fetchCourseList(query?: Object) {
    return request({
        url: 'teaching/course/page',
        method: 'get',
        params: query,
    });
}

// 所有已发布的课程
export function fetchAllPublishCourse() {
    return request({
        url: 'teaching/course/pubList',
        method: 'get',
    });
}

// 课程详情
export function fetchCourseDetail(id: string) {
    return request({
        url: 'teaching/course/get',
        method: 'get',
        params: { id },
    });
}

// 添加课程
export function addCourse(obj?: Object) {
    return request({
        url: 'teaching/course/add',
        method: 'post',
        data: obj,
    });
}

// 编辑课程
export function editCourse(obj?: Object) {
    return request({
        url: 'teaching/course/edit',
        method: 'put',
        data: obj,
    });
}

// 删除课程
export function deleteCourse(id: string) {
    return request({
        url: 'teaching/course/delete',
        method: 'delete',
        data: { id },
    });
}

// 发布课程
export function publishCourse(obj?: Object) {
    return request({
        url: 'teaching/course/coursePublish',
        method: 'post',
        data: obj,
    });
}

// 课节列表
export function fetchLessonList(query?: Object) {
    return request({
        url: 'teaching/lesson/list',
        method: 'get',
        params: query,
    });
}

// 调整课节顺序
export function sortLessons(courseId: string, entityList: Array<any>) {
    return request({
        url: 'teaching/lesson/updateBatchByList',
        method: 'post',
        data: { courseId, entityList },
    });
}

// 课节详情
export function lessonDetail(id: string) {
    return request({
        url: 'teaching/lesson/' + id,
        method: 'get',
    });
}

// 添加课节
export function addLesson(obj?: Object) {
    return request({
        url: 'teaching/lesson/saveLesson',
        method: 'post',
        data: obj,
    });
}

// 编辑课节
export function editLesson(obj?: Object) {
    return request({
        url: 'teaching/lesson',
        method: 'put',
        data: obj,
    });
}

// 删除课节
export function deleteLesson(courseId: string, lessonId: string) {
    return request({
        url: 'teaching/lesson/removeLesson',
        method: 'delete',
        data: { courseId, id: lessonId },
    });
}

// //模糊查询课节
// export function searchLesson() {
// 	return request({
// 		url: 'teaching/courseware/listBy',
// 		method: 'post',
// 	});
// }

// 课程状态

export function changeDeviceState(obj?: Object) {
    return request({
        url: 'teaching/course/disable',
        method: 'put',
        data: obj,
    });
}
