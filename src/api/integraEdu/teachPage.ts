import request from '/@/utils/request';

export function fetchTeachPageTempList(query?: Object) {
    return request({
        url: 'teaching/teachingPageTemplate/list',
        method: 'get',
        params: query,
    });
}

export function teachPageTempDetail(id: string) {
    return request({
        url: 'teaching/teachingPageTemplate/detail',
        method: 'get',
        params: { id },
    });
}

// 编辑/添加 教学页模版
export function editTeachPageTemp(data: any) {
    return request({
        url: 'teaching/teachingPageTemplate',
        method: data.id ? 'put' : 'post',
        data,
    });
}
