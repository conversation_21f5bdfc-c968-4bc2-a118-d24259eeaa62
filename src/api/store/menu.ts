import request from '/@/utils/request';
import type { MenuTreeQueryParams, MenuDetailsParams, MenuRequestBody } from '/@/types/store';

// 获取菜单树形数据
export const getStoreMenuTree = (params?: MenuTreeQueryParams) => {
    return request({
        url: '/store/menu/tree',
        method: 'get',
        params,
    });
};

// 获取菜单详情
export const getStoreMenuDetails = (params: MenuDetailsParams) => {
    return request({
        url: '/store/menu/details',
        method: 'get',
        params,
    });
};

// 获取角色的菜单集合
export const getStoreMenuTreeByRole = (roleId: number) => {
    return request({
        url: `/store/menu/tree/${roleId}`,
        method: 'get',
    });
};

// 新增菜单
export const addStoreMenu = (data: MenuRequestBody) => {
    return request({
        url: '/store/menu',
        method: 'post',
        data,
    });
};

// 更新菜单
export const updateStoreMenu = (data: MenuRequestBody) => {
    return request({
        url: '/store/menu',
        method: 'put',
        data,
    });
};

// 删除菜单（单个删除）
export const delStoreMenu = (id: number) => {
    return request({
        url: `/store/menu/${id}`,
        method: 'delete',
    });
};
