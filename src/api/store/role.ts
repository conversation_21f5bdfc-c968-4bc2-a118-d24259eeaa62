import request from '/@/utils/request';
import type {
    StoreRoleEntity,
    StoreRoleQueryParams,
    StoreRoleListParams,
    StoreRoleCreateRequest,
    StoreRoleUpdateRequest,
    RoleMenuUpdateRequest,
    RoleListByIdsRequest,
    ResponseData,
} from '/@/types/store';

// 1. 通过ID查询角色信息
// GET /store/role/details/{id}
export const getStoreRoleById = (id: number): Promise<ResponseData<StoreRoleEntity>> => {
    return request({
        url: `/store/role/details/${id}`,
        method: 'get',
    });
};

// 2. 查询角色信息
// GET /store/role/details
export const getStoreRoleDetails = (params: StoreRoleQueryParams): Promise<ResponseData<StoreRoleEntity>> => {
    return request({
        url: '/store/role/details',
        method: 'get',
        params,
    });
};

// 3. 添加角色
// POST /store/role
export const addStoreRole = (data: StoreRoleCreateRequest): Promise<ResponseData> => {
    return request({
        url: '/store/role',
        method: 'post',
        data,
    });
};

// 4. 修改角色
// PUT /store/role
export const updateStoreRole = (data: StoreRoleUpdateRequest): Promise<ResponseData> => {
    return request({
        url: '/store/role',
        method: 'put',
        data,
    });
};

// 5. 删除角色
// DELETE /store/role
export const delStoreRole = (data: string[]): Promise<ResponseData> => {
    return request({
        url: '/store/role',
        method: 'delete',
        data,
    });
};

// 6. 获取角色列表
// GET /store/role/list
export const getStoreRoleList = (params?: StoreRoleListParams): Promise<ResponseData> => {
    return request({
        url: '/store/role/page',
        method: 'get',
        params,
    });
};

// 保留现有的其他接口（菜单分配等）
export const getAllStoreRoles = (): Promise<ResponseData> => {
    return request({
        url: '/store/role/all',
        method: 'get',
    });
};

export const assignStoreMenus = (data: Object): Promise<ResponseData> => {
    return request({
        url: '/store/role/assign-menus',
        method: 'post',
        data,
    });
};

export const getStoreRoleMenus = (data: string[]): Promise<ResponseData> => {
    return request({
        url: '/store/role/getRoleList',
        method: 'post',
        data,
    });
};

// 新增：更新角色菜单
// PUT /store/role/menu
export const updateStoreRoleMenu = (data: RoleMenuUpdateRequest): Promise<ResponseData> => {
    return request({
        url: '/store/role/menu',
        method: 'put',
        data,
    });
};

// 新增：通过角色ID查询角色列表
// POST /store/role/getRoleList
export const getStoreRoleListByIds = (data: RoleListByIdsRequest): Promise<ResponseData> => {
    return request({
        url: '/store/role/getRoleList',
        method: 'post',
        data: data.roleIds,
    });
};
