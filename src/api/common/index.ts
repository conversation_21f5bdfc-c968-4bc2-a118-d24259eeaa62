import request from '/@/utils/request';

/**
 * 获取上传凭证
 * @param serviceName 服务名, 后续直接加在类型里 'teaching' | 'xxxxx' | 'xxxxxxxxx'
 * @param module 模块名, 随便传
 * @param suffix 后缀名
 * @returns
 */
export function getUpdateSign(serviceName: string, module: string, suffix: string, contentMd5?: string) {
    return request({
        url: 'teaching/file/getUploadSign',
        method: 'get',
        params: { serviceName, module, suffix, contentMd5 },
    });
}

export function md5Submit(url: string, contentMd5: string) {
    return request({
        url: 'teaching/file/submit',
        method: 'post',
        data: { url, contentMd5 },
    });
}

// 获取阶段列表
export function getStageList() {
    return request({
        url: 'teaching/stage/list',
        method: 'get',
    });
}
