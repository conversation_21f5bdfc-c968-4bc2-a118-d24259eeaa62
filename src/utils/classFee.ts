export const setCourseFee = (courseFee: string, isCustom: boolean) => {
    const isValidNumber = /^-?\d+(\.\d+)?$/.test(courseFee);
    if (isValidNumber) {
        const value = parseFloat(courseFee);
        const flooredValue = Math.floor(value * 100) / 100;
        return `${flooredValue.toFixed(2)}元/人次`;
    }
    return isCustom ? '0.00元/人次' : '/';
};
export const formatToTwoDecimals = (value: any) => {
    const num = Math.floor(parseFloat(value) * 100) / 100; // 向下截断到两位小数
    return num.toFixed(2); // 自动补齐两位小数
};

export enum ChargeMethodEnum {
    shop = 0,
    custom = 1,
}
