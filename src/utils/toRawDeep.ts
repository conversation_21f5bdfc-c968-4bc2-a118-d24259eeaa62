function toRawDeep(obj: any): any {
    if (isRef(obj)) {
        // 如果是 ref，获取其 value 并递归处理
        return toRawDeep(toRaw(obj.value));
    } else if (isReactive(obj)) {
        // 如果是 reactive，获取其原始对象并递归处理
        obj = toRaw(obj);
    }

    if (typeof obj === 'object' && obj !== null) {
        if (Array.isArray(obj)) {
            // 如果是数组，递归处理每个元素
            return obj.map((item) => toRawDeep(item));
        } else {
            // 如果是普通对象，递归处理每个属性
            const result: any = {};
            for (const key in obj) {
                if (obj.hasOwnProperty(key)) {
                    result[key] = toRawDeep(obj[key]);
                }
            }
            return result;
        }
    }

    // 如果不是对象或数组，直接返回
    return obj;
}

export default toRawDeep;
