export default {
    i18n: {
        index: '#',
        importsysI18nTip: 'import SysI18n',
        id: 'id',
        name: 'name',
        zhCn: 'zh-cn',
        en: 'en',
        createBy: 'createBy',
        createTime: 'createTime',
        updateBy: 'updateBy',
        updateTime: 'updateTime',
        delFlag: 'delFlag',
        inputIdTip: 'input id',
        inputKeyTip: 'input key',
        inputZhCnTip: 'input zh-cn',
        inputEnTip: 'input en',
        inputCreateByTip: 'input createBy',
        inputCreateTimeTip: 'input createTime',
        inputUpdateByTip: 'input updateBy',
        inputUpdateTimeTip: 'input updateTime',
        inputDelFlagTip: 'input delFlag',
    },
};
