export default {
    file: {
        index: '#',
        importsysFileTip: 'import SysFile',
        id: 'id',
        fileName: 'fileName',
        bucketName: 'bucketName',
        original: 'original',
        type: 'type',
        fileSize: 'fileSize',
        createBy: 'createBy',
        updateBy: 'updateBy',
        createTime: 'createTime',
        updateTime: 'updateTime',
        delFlag: 'delFlag',
        tenantId: 'tenantId',
        inputidTip: 'input id',
        inputfileNameTip: 'input fileName',
        inputbucketNameTip: 'input bucketName',
        inputoriginalTip: 'input original',
        inputtypeTip: 'input type',
        inputfileSizeTip: 'input fileSize',
        inputcreateByTip: 'input createBy',
        inputupdateByTip: 'input updateBy',
        inputcreateTimeTip: 'input createTime',
        inputupdateTimeTip: 'input updateTime',
        inputdelFlagTip: 'input delFlag',
        inputtenantIdTip: 'input tenantId',
        image: 'image',
        video: 'video',
        file: 'file',
    },
};
