export default {
    audit: {
        index: '#',
        importsysAuditLogTip: 'import SysAuditLog',
        id: 'id',
        auditName: 'auditName',
        auditField: 'auditField',
        beforeVal: 'beforeVal',
        afterVal: 'afterVal',
        createBy: 'createBy',
        createTime: 'createTime',
        delFlag: 'delFlagx',
        tenantId: 'tenantId',
        inputIdTip: 'input id',
        inputAuditNameTip: 'input auditName',
        inputAuditFieldTip: 'input auditField',
        inputBeforeValTip: 'input beforeVal',
        inputAfterValTip: 'input afterVal',
        inputCreateByTip: 'input createBy',
        inputCreateTimeTip: 'input createTime',
        inputDelFlagTip: 'input delFlagx',
        inputTenantIdTip: 'input tenantId',
    },
};
