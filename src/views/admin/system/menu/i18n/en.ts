export default {
    sysmenu: {
        index: '#',
        name: 'menu name',
        buttonName: 'button name',
        sortOrder: 'sortOrder',
        path: 'path',
        menuType: 'menuType',
        keepAlive: 'keepAlive',
        permission: 'permission',
        inputNameTip: 'input name',
        parentId: 'parent menu',
        embedded: 'embedded',
        param: 'param',
        component: 'component',
        visible: 'visible',
        icon: 'icon',
        inputMenuIdTip: 'input menuId',
        inputPermissionTip: 'input permission',
        inputPathTip: 'input path',
        inputParentIdTip: 'input parentId',
        inputIconTip: 'input icon',
        inputVisibleTip: 'input visible',
        inputSortOrderTip: 'input sortOrder',
        inputKeepAliveTip: 'input keepAlive',
        inputMenuTypeTip: 'input menuType',
        inputCreateByTip: 'input createBy',
        inputCreateTimeTip: 'input createTime',
        inputUpdateByTip: 'input updateBy',
        inputUpdateTimeTip: 'input updateTime',
        inputDelFlagTip: 'input delFlag',
        inputTenantIdTip: 'input tenantId',
        inputEmbeddedTip: 'input embedded',
        inputComponentTip: 'input component',
        deleteDisabledTip: 'menu inclusion subordinates cannot be deleted',
    },
};
