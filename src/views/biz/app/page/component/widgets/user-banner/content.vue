<template>
    <div class="banner mx-[10px] mt-[10px]">
        <div class="banner-image">
            <decoration-img width="100%" height="100px" :src="getImage" fit="contain" />
        </div>
    </div>
</template>
<script lang="ts" setup>
import type { PropType } from 'vue';
import type options from './options';
import DecorationImg from '../../decoration-img.vue';
type OptionsType = ReturnType<typeof options>;
const props = defineProps({
    content: {
        type: Object as PropType<OptionsType['content']>,
        default: () => ({}),
    },
    styles: {
        type: Object as PropType<OptionsType['styles']>,
        default: () => ({}),
    },
});
const getImage = computed(() => {
    const { data } = props.content;
    if (Array.isArray(data)) {
        return data[0] ? data[0].image : '';
    }
    return '';
});
</script>

<style lang="scss" scoped></style>
