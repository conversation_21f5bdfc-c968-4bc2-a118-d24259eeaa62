export default {
    wxFansMsg: {
        index: '#',
        importwxMsgTip: '导入微信消息',
        id: '主键',
        appName: '公众号名称',
        appLogo: '公众号logo',
        wxUserId: '微信用户ID',
        nickName: '微信用户昵称',
        headimgUrl: '微信用户头像',
        type: '消息分类',
        repType: '消息类型',
        repEvent: '事件类型',
        repContent: '内容',
        repMediaId: '回复类型',
        repName: '回复的素材名、视频和音乐的标题',
        repDesc: '视频和音乐的描述',
        repUrl: '链接',
        repHqUrl: '高质量链接',
        content: '图文消息的内容',
        repThumbMediaId: '缩略图的媒体id',
        repThumbUrl: '缩略图url',
        repLocationX: '地理位置维度',
        repLocationY: '地理位置经度',
        repScale: '地图缩放大小',
        readFlag: '已读标记',
        appId: '公众号ID',
        openId: '微信唯一标识',
        remark: '备注',
        delFlag: '逻辑删除标记（0：显示；1：隐藏）',
        createTime: '创建时间',
        updateTime: '更新时间',
        tenantId: '租户ID',
        inputIdTip: '请输入主键',
        inputAppNameTip: '请输入公众号名称',
        inputAppLogoTip: '请输入公众号logo',
        inputWxUserIdTip: '请输入微信用户ID',
        inputNickNameTip: '请输入微信用户昵称',
        inputHeadimgUrlTip: '请输入微信用户头像',
        inputTypeTip: '请输入消息分类',
        inputRepTypeTip: '请输入消息类型',
        inputRepEventTip: '请输入事件类型',
        inputRepContentTip: '请输入回复类型文本保存文字、地理位置信息',
        inputRepMediaIdTip: '请输入回复类型',
        inputRepNameTip: '请输入回复的素材名、视频和音乐的标题',
        inputRepDescTip: '请输入视频和音乐的描述',
        inputRepUrlTip: '请输入链接',
        inputRepHqUrlTip: '请输入高质量链接',
        inputContentTip: '请输入图文消息的内容',
        inputRepThumbMediaIdTip: '请输入缩略图的媒体id',
        inputRepThumbUrlTip: '请输入缩略图url',
        inputRepLocationXTip: '请输入地理位置维度',
        inputRepLocationYTip: '请输入地理位置经度',
        inputRepScaleTip: '请输入地图缩放大小',
        inputReadFlagTip: '请输入已读标记（1：是；0：否）',
        inputAppIdTip: '请输入公众号ID',
        inputOpenIdTip: '请输入微信唯一标识',
        inputRemarkTip: '请输入备注',
        inputDelFlagTip: '请输入逻辑删除标记（0：显示；1：隐藏）',
        inputCreateTimeTip: '请输入创建时间',
        inputUpdateTimeTip: '请输入更新时间',
        inputTenantIdTip: '请输入租户ID',
    },
};
