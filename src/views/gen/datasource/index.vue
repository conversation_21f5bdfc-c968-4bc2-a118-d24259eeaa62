<template>
    <div class="layout-padding">
        <div class="layout-padding-auto layout-padding-view">
            <el-row class="ml10" v-show="showSearch">
                <el-form :inline="true" :model="state.queryForm" @keyup.enter="getDataList" ref="queryRef">
                    <el-form-item :label="$t('datasourceconf.dsName')" prop="dsName">
                        <el-input :placeholder="$t('datasourceconf.inputdsNameTip')" v-model="state.queryForm.dsName" />
                    </el-form-item>
                    <el-form-item>
                        <el-button @click="getDataList" icon="search" type="primary">
                            {{ $t('common.queryBtn') }}
                        </el-button>
                        <el-button @click="resetQuery" icon="Refresh">{{ $t('common.resetBtn') }} </el-button>
                    </el-form-item>
                </el-form>
            </el-row>
            <el-row>
                <div class="mb8" style="width: 100%">
                    <el-button @click="formDialogRef.openDialog()" class="ml10" icon="folder-add" type="primary">
                        {{ $t('common.addBtn') }}
                    </el-button>
                    <el-button
                        :disabled="multiple"
                        @click="handleDelete(selectObjs)"
                        class="ml10"
                        icon="Delete"
                        type="primary"
                    >
                        {{ $t('common.delBtn') }}
                    </el-button>
                    <right-toolbar
                        @queryTable="getDataList"
                        class="ml10"
                        style="float: right; margin-right: 20px"
                        v-model:showSearch="showSearch"
                    ></right-toolbar>
                </div>
            </el-row>
            <el-table
                :data="state.dataList"
                @selection-change="handleSelectionChange"
                style="width: 100%"
                v-loading="state.loading"
                border
                :cell-style="tableStyle.cellStyle"
                :header-cell-style="tableStyle.headerCellStyle"
            >
                <el-table-column align="center" type="selection" width="40" />
                <el-table-column :label="t('datasourceconf.index')" type="index" width="60" />
                <el-table-column :label="t('datasourceconf.name')" prop="name" show-overflow-tooltip />
                <el-table-column :label="t('datasourceconf.dsName')" prop="dsName" show-overflow-tooltip />
                <el-table-column :label="t('datasourceconf.dsType')" prop="dsType" show-overflow-tooltip />
                <el-table-column :label="t('datasourceconf.username')" prop="username" show-overflow-tooltip />
                <el-table-column :label="t('datasourceconf.createTime')" prop="createTime" show-overflow-tooltip />
                <el-table-column :label="$t('common.action')" width="250">
                    <template #default="scope">
                        <el-button icon="document" @click="downloadDoc(scope.row.name)" text type="primary"
                            >{{ $t('datasourceconf.docBtn') }}
                        </el-button>

                        <el-button icon="edit" @click="formDialogRef.openDialog(scope.row.id)" text type="primary"
                            >{{ $t('common.editBtn') }}
                        </el-button>

                        <el-button icon="delete" @click="handleDelete([scope.row.id])" text type="primary"
                            >{{ $t('common.delBtn') }}
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
            <pagination
                @current-change="currentChangeHandle"
                @size-change="sizeChangeHandle"
                v-bind="state.pagination"
            />
        </div>

        <!-- 编辑、新增  -->
        <form-dialog @refresh="getDataList()" ref="formDialogRef" />
    </div>
</template>

<script lang="ts" name="systemDatasourceConf" setup>
import { BasicTableProps, useTable } from '/@/hooks/table';
import { delObj, fetchList } from '/@/api/gen/datasource';
import { useMessage, useMessageBox } from '/@/hooks/message';
import { useI18n } from 'vue-i18n';
import { downBlobFile } from '/@/utils/other';

// 引入组件
const FormDialog = defineAsyncComponent(() => import('./form.vue'));
const { t } = useI18n();

// 定义变量内容
const formDialogRef = ref();
// 搜索变量
const queryRef = ref();
const showSearch = ref(true);
// 多选变量
const selectObjs = ref([]) as any;
const multiple = ref(true);

const state: BasicTableProps = reactive<BasicTableProps>({
    queryForm: {},
    pageList: fetchList,
    descs: ['create_time'],
});

//  table hook
const { getDataList, currentChangeHandle, sizeChangeHandle, tableStyle } = useTable(state);

const downloadDoc = (dsName: string) => {
    downBlobFile('/gen/dsconf/doc', { dsName }, `${dsName}.html`);
};

// 清空搜索条件
const resetQuery = () => {
    queryRef.value.resetFields();
    getDataList();
};

// 多选事件
const handleSelectionChange = (objs: { id: string }[]) => {
    selectObjs.value = objs.map(({ id }) => id);
    multiple.value = !objs.length;
};

// 删除操作
const handleDelete = async (ids: string[]) => {
    try {
        await useMessageBox().confirm(t('common.delConfirmText'));
    } catch {
        return;
    }

    try {
        await delObj(ids);
        getDataList();
        useMessage().success(t('common.delSuccessText'));
    } catch (err: any) {
        useMessage().error(err.msg);
    }
};
</script>
