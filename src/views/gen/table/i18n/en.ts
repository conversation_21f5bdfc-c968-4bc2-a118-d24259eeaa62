export default {
    gen: {
        syncBtn: 'sync',
        designBtn: 'design',
        genBtn: 'gen',
        prewBtn: 'preview',
    },
    table: {
        index: '#',
        importTableTip: ' import Table',
        id: 'id',
        tableName: 'tableName',
        className: 'className',
        tableComment: 'tableComment',
        tableDesc: 'comment',
        author: 'author',
        email: 'email',
        packageName: 'packageName',
        version: 'version',
        generatorType: 'generatorType',
        backendPath: 'backendPath',
        frontendPath: 'frontendPath',
        moduleName: 'moduleName',
        functionName: 'functionName',
        formLayout: 'formLayout',
        datasourceId: 'datasourceId',
        baseclassId: 'baseclassId',
        createTime: 'createTime',
        inputidTip: 'input id',
        inputtableNameTip: 'input tableName',
        inputclassNameTip: 'input className',
        inputtableCommentTip: 'input tableComment',
        inputauthorTip: 'input author',
        inputemailTip: 'input email',
        inputpackageNameTip: 'input packageName',
        inputversionTip: 'input version',
        inputgeneratorTypeTip: 'input generatorType',
        inputbackendPathTip: 'input backendPath',
        inputfrontendPathTip: 'input frontendPath',
        inputmoduleNameTip: 'input moduleName',
        inputfunctionNameTip: 'input functionName',
        inputformLayoutTip: 'input formLayout',
        inputdatasourceIdTip: 'input datasourceId',
        inputbaseclassIdTip: 'input baseclassId',
        inputcreateTimeTip: 'input createTime',
    },
};
