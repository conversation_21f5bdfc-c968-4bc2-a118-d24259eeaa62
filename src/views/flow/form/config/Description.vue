<script setup lang="ts">
import { computed, defineExpose } from 'vue';

let props = defineProps({
    id: {
        type: String,
        default: '',
    },
});

import { useFlowStore } from '../../workflow/stores/flow';

let flowStore = useFlowStore();

var config = computed(() => {
    let step2 = flowStore.step2;
    var idObjList = step2.filter((res) => res.id === props.id);
    if (idObjList.length > 0) {
        return idObjList[0];
    }

    let list = step2.filter((res) => res.type === 'Layout');
    for (var item of list) {
        let value = item.props.value;
        var valueList = value.filter((res) => res.id === props.id);
        if (valueList.length > 0) {
            return valueList[0];
        }
    }

    return undefined;
});
const { proxy } = getCurrentInstance();

const placeHolder = computed(() => {
    return config.value.placeholder;
});

watch(
    () => placeHolder.value,
    (f) => {
        config.value.props.value = f;
    },
    { deep: true }
);
</script>

<template>
    <div></div>
</template>
