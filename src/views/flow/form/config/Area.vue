<script setup lang="ts">
let props = defineProps({
    id: {
        type: String,
        default: '',
    },
});

import { useFlowStore } from '../../workflow/stores/flow';

let flowStore = useFlowStore();
const config = computed(() => {
    let step2 = flowStore.step2;

    const idObjList = step2.filter((res) => res.id === props.id);
    if (idObjList.length > 0) {
        return idObjList[0];
    }

    let list = step2.filter((res) => res.type === 'Layout');
    for (const item of list) {
        let value = item.props.value;
        const valueList = value.filter((res: any) => res.id === props.id);
        if (valueList.length > 0) {
            return valueList[0];
        }
    }

    return undefined;
});
</script>

<template>
    <div></div>
</template>
