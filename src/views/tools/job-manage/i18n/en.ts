export default {
    job: {
        index: '#',
        jobLogBtn: 'log',
        jobStartBtn: 'start',
        jobRunBtn: 'execute',
        jobPauseBtn: 'pause',
        importsysJobTip: 'import SysJob',
        jobId: 'jobId',
        jobName: 'jobName',
        jobGroup: 'jobGroup',
        jobOrder: 'jobOrder',
        jobType: 'jobType',
        executePath: 'executePath',
        className: 'className',
        methodName: 'methodName',
        methodParamsValue: 'methodParamsValue',
        cronExpression: 'cronExpression',
        misfirePolicy: 'misfirePolicy',
        jobTenantType: 'jobTenantType',
        jobStatus: 'jobStatus',
        jobExecuteStatus: 'jobExecuteStatus',
        createBy: 'createBy',
        createTime: 'createTime',
        updateBy: 'updateBy',
        updateTime: 'updateTime',
        startTime: 'startTime',
        previousTime: 'previousTime',
        nextTime: 'nextTime',
        tenantId: 'tenantId',
        remark: 'remark',
        jobMessage: 'jobMessage',
        jobLogStatus: 'jobLogStatus',
        executeTime: 'executeTime',
        exceptionInfo: 'exceptionInfo',
        inputjobIdTip: 'input jobId',
        inputjobNameTip: 'input jobName',
        inputjobGroupTip: 'input jobGroup',
        inputjobOrderTip: 'input jobOrder',
        inputjobTypeTip: 'input jobType',
        inputexecutePathTip: 'input executePath',
        inputclassNameTip: 'input className',
        inputmethodNameTip: 'input methodName',
        inputmethodParamsValueTip: 'input methodParamsValue',
        inputcronExpressionTip: 'input cronExpression',
        inputmisfirePolicyTip: 'input misfirePolicy',
        inputjobTenantTypeTip: 'input jobTenantType',
        inputjobStatusTip: 'input jobStatus',
        inputjobExecuteStatusTip: 'input jobExecuteStatus',
        inputcreateByTip: 'input createBy',
        inputcreateTimeTip: 'input createTime',
        inputupdateByTip: 'input updateBy',
        inputupdateTimeTip: 'input updateTime',
        inputstartTimeTip: 'input startTime',
        inputpreviousTimeTip: 'input previousTime',
        inputnextTimeTip: 'input nextTime',
        inputtenantIdTip: 'input tenantId',
        inputremarkTip: 'input remark',
    },
    log: {
        index: '#',
        importsysJobLogTip: 'import SysJobLog',
        jobLogId: 'jobLogId',
        jobId: 'jobId',
        jobName: 'jobName',
        jobGroup: 'jobGroup',
        jobOrder: 'jobOrder',
        jobType: 'jobType',
        executePath: 'executePath',
        className: 'className',
        methodName: 'methodName',
        methodParamsValue: 'methodParamsValue',
        cronExpression: 'cronExpression',
        jobMessage: 'jobMessage',
        jobLogStatus: 'jobLogStatus',
        executeTime: 'executeTime',
        exceptionInfo: 'exceptionInfo',
        createTime: 'createTime',
        tenantId: 'tenantId',
        inputJobLogIdTip: 'input jobLogId',
        inputJobIdTip: 'input jobId',
        inputJobNameTip: 'input jobName',
        inputJobGroupTip: 'input jobGroup',
        inputJobOrderTip: 'input jobOrder',
        inputJobTypeTip: 'input jobType',
        inputExecutePathTip: 'input executePath',
        inputClassNameTip: 'input className',
        inputMethodNameTip: 'input methodName',
        inputMethodParamsValueTip: 'input methodParamsValue',
        inputCronExpressionTip: 'input cronExpression',
        inputJobMessageTip: 'input jobMessage',
        inputJobLogStatusTip: 'input jobLogStatus',
        inputExecuteTimeTip: 'input executeTime',
        inputExceptionInfoTip: 'input exceptionInfo',
        inputCreateTimeTip: 'input createTime',
        inputTenantIdTip: 'input tenantId',
    },
};
