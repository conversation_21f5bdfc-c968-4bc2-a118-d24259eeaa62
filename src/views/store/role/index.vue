<template>
    <div class="layout-padding">
        <div class="layout-padding-auto layout-padding-view">
            <!-- 搜索栏 -->
            <el-row v-show="showSearch" class="ml10">
                <el-form :inline="true" :model="queryForm" @keyup.enter="getDataList" ref="queryRef">
                    <el-form-item label="角色名称" prop="roleName">
                        <el-input
                            placeholder="请输入角色名称"
                            clearable
                            style="max-width: 180px"
                            v-model="queryForm.roleName"
                        />
                    </el-form-item>

                    <el-form-item>
                        <el-button @click="query" class="ml10" icon="search" type="primary">查询</el-button>
                        <el-button @click="resetQuery" icon="Refresh">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-row>

            <!-- 工具栏 -->
            <el-row>
                <div class="mb8" style="width: 100%">
                    <el-button @click="handleAdd" class="ml10" icon="plus" type="primary">新增角色</el-button>
                    <right-toolbar
                        v-model:showSearch="showSearch"
                        class="ml10"
                        style="float: right; margin-right: 20px"
                        @queryTable="getDataList"
                    ></right-toolbar>
                </div>
            </el-row>

            <!-- 角色表格 -->
            <el-table ref="tableRef" :data="roleList" style="width: 100%" v-loading="tableLoading" border>
                <el-table-column prop="roleName" label="角色名称" show-overflow-tooltip />
                <el-table-column prop="roleCode" label="角色编码" show-overflow-tooltip />
                <el-table-column prop="roleDesc" label="角色描述" show-overflow-tooltip />
                <el-table-column prop="createTime" label="创建时间" width="180" />

                <el-table-column label="操作" width="300" fixed="right">
                    <template #default="scope">
                        <el-button icon="edit-pen" @click="handleEdit(scope.row)" text type="primary">编辑</el-button>
                        <el-button icon="setting" @click="handleAssignMenu(scope.row)" text type="warning">
                            分配权限
                        </el-button>
                        <el-button icon="delete" @click="handleDelete(scope.row)" text type="danger">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>

            <!-- 分页 -->
            <pagination
                v-show="total > 0"
                :total="total"
                v-model:page="queryForm.current"
                v-model:limit="queryForm.size"
                @pagination="getDataList"
            />
        </div>

        <!-- 角色编辑对话框 -->
        <RoleDialog @refresh="getDataList()" ref="roleDialogRef" />

        <!-- 权限分配对话框 -->
        <MenuAssignDialog @refresh="getDataList()" ref="menuAssignDialogRef" />
    </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue';
import { ElMessageBox } from 'element-plus';
import { getStoreRoleList, delStoreRole } from '/@/api/store/role';
import { useMessage } from '/@/hooks/message';

// 引入组件
const RoleDialog = defineAsyncComponent(() => import('./components/RoleDialog.vue'));
const MenuAssignDialog = defineAsyncComponent(() => import('./components/MenuAssignDialog.vue'));

// 定义变量
const tableRef = ref();
const roleDialogRef = ref();
const menuAssignDialogRef = ref();
const showSearch = ref(true);
const queryRef = ref();
const tableLoading = ref(false);

const queryForm = reactive({
    current: 1,
    size: 10,
    roleName: '',
});

const roleList = ref([]);
const total = ref(0);

onMounted(() => {
    getDataList();
});

// 获取角色数据
const getDataList = async () => {
    try {
        tableLoading.value = true;
        const res = await getStoreRoleList(queryForm);
        roleList.value = res.data.records || [];
        total.value = res.data.total || 0;
    } catch (error) {
        useMessage().error('加载角色数据失败');
    } finally {
        tableLoading.value = false;
    }
};

// 查询
const query = () => {
    queryForm.current = 1;
    getDataList();
};

// 重置查询
const resetQuery = () => {
    queryRef.value.resetFields();
    queryForm.roleName = '';
    queryForm.current = 1;
    getDataList();
};

// 新增角色
const handleAdd = () => {
    roleDialogRef.value.openDialog('add');
};

// 编辑角色
const handleEdit = (row: any) => {
    roleDialogRef.value.openDialog('edit', row);
};

// 分配权限
const handleAssignMenu = (row: any) => {
    menuAssignDialogRef.value.openDialog(row);
};

// 删除角色
const handleDelete = async (row: any) => {
    try {
        await ElMessageBox.confirm(`确定要删除角色"${row.roleName}"吗？`, '确认删除', { type: 'warning' });
        await delStoreRole([row.roleId]);
        useMessage().success('删除成功');
        getDataList();
    } catch (error) {
        if (error !== 'cancel') {
            useMessage().error('删除失败');
        }
    }
};
</script>

<style scoped>
.layout-padding {
    padding: 20px;
}
</style>
