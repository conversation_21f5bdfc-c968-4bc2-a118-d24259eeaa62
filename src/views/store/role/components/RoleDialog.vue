<template>
    <el-dialog
        :title="isEdit ? '编辑角色' : '新增角色'"
        v-model="visible"
        width="500px"
        :close-on-click-modal="false"
        draggable
    >
        <el-form ref="formRef" :model="formData" :rules="formRules" label-width="100px" v-loading="loading">
            <el-form-item label="角色名称" prop="roleName">
                <el-input v-model="formData.roleName" placeholder="请输入角色名称" />
            </el-form-item>

            <el-form-item label="角色编码" prop="roleCode">
                <el-input v-model="formData.roleCode" placeholder="请输入角色编码" />
            </el-form-item>

            <el-form-item label="角色描述" prop="roleDesc">
                <el-input v-model="formData.roleDesc" type="textarea" :rows="3" placeholder="请输入角色描述" />
            </el-form-item>

            <el-form-item label="数据权限" prop="dsType">
                <el-select v-model="formData.dsType" placeholder="请选择数据权限类型" style="width: 100%">
                    <el-option label="全部数据权限" :value="0" />
                    <el-option label="自定数据权限" :value="1" />
                    <el-option label="本部门数据权限" :value="2" />
                    <el-option label="本部门及以下数据权限" :value="3" />
                    <el-option label="仅本人数据权限" :value="4" />
                </el-select>
            </el-form-item>

            <el-form-item label="权限范围" prop="dsScope" v-if="formData.dsType === 1">
                <el-input v-model="formData.dsScope" placeholder="请输入数据权限作用范围" />
            </el-form-item>
        </el-form>

        <template #footer>
            <span class="dialog-footer">
                <el-button @click="visible = false">取消</el-button>
                <el-button type="primary" @click="handleSubmit" :disabled="loading">确定</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, computed } from 'vue';
import { addStoreRole, updateStoreRole } from '/@/api/store/role';
import { useMessage } from '/@/hooks/message';

const emit = defineEmits(['refresh']);

const formRef = ref();
const visible = ref(false);
const loading = ref(false);

const formData = reactive({
    roleId: undefined,
    roleName: '',
    roleCode: '',
    roleDesc: '',
    dsType: 0,
    dsScope: '',
});

const formRules = reactive({
    roleName: [{ required: true, message: '请输入角色名称', trigger: 'blur' }],
    roleCode: [{ required: true, message: '请输入角色编码', trigger: 'blur' }],
    roleDesc: [{ required: false, message: '请输入角色描述', trigger: 'blur' }],
    dsType: [{ required: true, message: '请选择数据权限类型', trigger: 'change' }],
});

const isEdit = computed(() => {
    return !!formData.roleId;
});

const openDialog = (type: string, row?: any) => {
    visible.value = true;
    loading.value = false;

    nextTick(() => {
        formRef.value?.resetFields();

        if (type === 'edit' && row) {
            // 将组件使用的snake_case字段转换为接口需要的camelCase
            Object.assign(formData, {
                roleId: row.roleId,
                roleName: row.roleName,
                roleCode: row.roleCode,
                roleDesc: row.roleDesc,
                dsType: row.ds_type || 0,
                dsScope: row.ds_scope || '',
            });
        } else {
            Object.assign(formData, {
                roleId: undefined,
                roleName: '',
                roleCode: '',
                roleDesc: '',
                dsType: 0,
                dsScope: '',
            });
        }
    });
};

const handleSubmit = async () => {
    const valid = await formRef.value.validate().catch(() => {});
    if (!valid) return false;

    try {
        loading.value = true;

        if (isEdit.value) {
            await updateStoreRole(formData);
            useMessage().success('更新成功');
        } else {
            await addStoreRole(formData);
            useMessage().success('新增成功');
        }

        visible.value = false;
        emit('refresh');
    } catch (err: any) {
        useMessage().error(err.msg || '操作失败');
    } finally {
        loading.value = false;
    }
};

defineExpose({
    openDialog,
});
</script>

<style scoped>
.dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}
</style>
