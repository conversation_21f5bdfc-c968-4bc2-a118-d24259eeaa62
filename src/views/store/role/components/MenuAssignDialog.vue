<template>
    <el-dialog title="分配权限" v-model="visible" width="600px" :close-on-click-modal="false" draggable>
        <!-- 操作按钮 -->
        <div class="operation-buttons mb-4">
            <el-button size="small" @click="handleSelectAll">全选</el-button>
            <el-button size="small" @click="handleUnselectAll">取消全选</el-button>
        </div>

        <!-- 权限树 -->
        <el-scrollbar class="tree-container" height="400px">
            <el-tree
                ref="menuTreeRef"
                :data="menuTreeData"
                :props="treeProps"
                node-key="id"
                show-checkbox
                :check-strictly="false"
                :default-expand-all="false"
                v-loading="treeLoading"
            >
                <template #default="{ data }">
                    <div class="tree-node">
                        <span class="node-label">{{ data.name }}</span>
                        <el-tag v-if="data.menuType === '0'" type="primary" size="small">菜单</el-tag>
                        <el-tag v-else type="warning" size="small">按钮</el-tag>
                    </div>
                </template>
            </el-tree>
        </el-scrollbar>

        <template #footer>
            <span class="dialog-footer">
                <el-button @click="visible = false">取消</el-button>
                <el-button type="primary" @click="handleSubmit" :disabled="loading">确定</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script lang="ts" setup>
import { ref, nextTick } from 'vue';
import { getStoreMenuTree, getStoreMenuTreeByRole } from '/@/api/store/menu';
import { updateStoreRoleMenu } from '/@/api/store/role';
import { useMessage } from '/@/hooks/message';

const emit = defineEmits(['refresh']);

const visible = ref(false);
const loading = ref(false);
const treeLoading = ref(false);
const menuTreeRef = ref();

const roleData = ref<any>({});
const menuTreeData = ref<any[]>([]);

const treeProps = {
    children: 'children',
    label: 'name',
};

const openDialog = (row: any) => {
    visible.value = true;
    roleData.value = row;
    loadMenuTree();
};

const loadMenuTree = async () => {
    try {
        treeLoading.value = true;

        // 加载菜单树
        const menuRes = await getStoreMenuTree();
        menuTreeData.value = menuRes.data || [];

        // 加载角色已分配的权限
        if (roleData.value.roleId) {
            const permissionRes = await getStoreMenuTreeByRole(roleData.value.roleId);

            // 设置选中的节点 - 只设置叶子节点避免级联选择
            nextTick(() => {
                if (menuTreeRef.value && permissionRes.data) {
                    const leafNodeIds = getLeafNodeIds(menuTreeData.value, permissionRes.data);
                    menuTreeRef.value.setCheckedKeys(leafNodeIds);
                }
            });
        }
    } catch (error) {
        useMessage().error('加载数据失败');
    } finally {
        treeLoading.value = false;
    }
};

// 获取叶子节点ID
const getLeafNodeIds = (treeData: any[], checkedIds: any[]): any[] => {
    const leafIds: any[] = [];

    const findLeafNodes = (nodes: any[]) => {
        nodes.forEach((node) => {
            if (checkedIds.includes(node.id)) {
                // 如果是叶子节点（没有子节点或子节点为空）
                if (!node.children || node.children.length === 0) {
                    leafIds.push(node.id);
                }
            }
            // 递归处理子节点
            if (node.children && node.children.length > 0) {
                findLeafNodes(node.children);
            }
        });
    };

    findLeafNodes(treeData);
    return leafIds;
};

const handleSelectAll = () => {
    const allNodeIds = getAllNodeIds(menuTreeData.value);
    menuTreeRef.value.setCheckedKeys(allNodeIds);
};

const handleUnselectAll = () => {
    menuTreeRef.value.setCheckedKeys([]);
};

// 获取所有节点ID
const getAllNodeIds = (nodes: any[]): any[] => {
    const ids: any[] = [];
    const traverse = (nodeList: any[]) => {
        nodeList.forEach((node) => {
            ids.push(node.id);
            if (node.children && node.children.length > 0) {
                traverse(node.children);
            }
        });
    };
    traverse(nodes);
    return ids;
};

const handleSubmit = async () => {
    try {
        loading.value = true;

        const checkedKeys = menuTreeRef.value.getCheckedKeys();
        const halfCheckedKeys = menuTreeRef.value.getHalfCheckedKeys();
        const allCheckedKeys = [...checkedKeys, ...halfCheckedKeys];

        await updateStoreRoleMenu({
            roleId: roleData.value.roleId,
            menuIds: allCheckedKeys.join(','),
        });

        useMessage().success('权限分配成功');
        visible.value = false;
        emit('refresh');
    } catch (error) {
        useMessage().error('权限分配失败');
    } finally {
        loading.value = false;
    }
};

defineExpose({
    openDialog,
});
</script>

<style scoped>
.app-type-selector {
    text-align: center;
}

.operation-buttons {
    display: flex;
    gap: 10px;
}

.tree-container {
    border: 1px solid var(--el-border-color);
    border-radius: 4px;
    padding: 10px;
}

.tree-node {
    display: flex;
    align-items: center;
    gap: 8px;
    width: 100%;
}

.node-label {
    flex: 1;
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}
</style>
