<template>
    <el-dialog
        :title="isEdit ? '编辑菜单' : '新增菜单'"
        v-model="visible"
        width="600px"
        :close-on-click-modal="false"
        draggable
    >
        <el-form ref="formRef" :model="formData" :rules="formRules" label-width="100px" v-loading="loading">
            <el-form-item label="菜单名称" prop="name">
                <el-input v-model="formData.name" placeholder="请输入菜单名称" />
            </el-form-item>

            <el-form-item label="权限标识" prop="permission">
                <el-input v-model="formData.permission" placeholder="请输入权限标识" />
            </el-form-item>

            <el-form-item label="菜单类型" prop="menuType">
                <el-radio-group v-model="formData.menuType">
                    <el-radio border label="0">菜单</el-radio>
                    <el-radio border label="1">按钮</el-radio>
                </el-radio-group>
            </el-form-item>

            <el-form-item label="排序" prop="sortOrder">
                <el-input-number
                    v-model="formData.sortOrder"
                    :min="0"
                    style="width: 200px"
                    controls-position="right"
                    :controls="true"
                />
            </el-form-item>
            <!--
            <el-form-item label="显示状态" prop="visible">
                <el-radio-group v-model="formData.visible">
                    <el-radio border label="0">隐藏</el-radio>
                    <el-radio border label="1">显示</el-radio>
                </el-radio-group>
            </el-form-item> -->
        </el-form>

        <template #footer>
            <span class="dialog-footer">
                <el-button @click="visible = false">取消</el-button>
                <el-button type="primary" @click="handleSubmit" :disabled="loading">确定</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, nextTick } from 'vue';
import { addStoreMenu, getStoreMenuDetails, updateStoreMenu } from '/@/api/store/menu';
import { useMessage } from '/@/hooks/message';
import type { MenuRequestBody } from '/@/types/store';

const emit = defineEmits(['refresh']);

const formRef = ref();
const visible = ref(false);
const loading = ref(false);

const formData = reactive<MenuRequestBody>({
    menuId: undefined,
    name: '',
    permission: '',
    path: '',
    parentId: 0,
    visible: '1',
    sortOrder: 1,
    menuType: '0',
});

const formRules = reactive({
    name: [{ required: true, message: '请输入菜单名称', trigger: 'blur' }],
    permission: [{ required: true, message: '请输入权限标识', trigger: 'blur' }],
    menuType: [{ required: true, message: '请选择菜单类型', trigger: 'change' }],
    path: [
        {
            validator: (rule: any, value: any, callback: any) => {
                if (formData.menuType === '0' && !value) {
                    callback(new Error('请输入路由路径'));
                } else {
                    callback();
                }
            },
            trigger: 'blur',
        },
    ],
    sortOrder: [{ required: true, message: '请输入排序', trigger: 'blur' }],
    visible: [{ required: true, message: '请选择是否显示', trigger: 'change' }],
});

const isEdit = computed(() => {
    return !!formData.menuId;
});

const openDialog = (type: string, row?: any) => {
    visible.value = true;
    loading.value = false;

    nextTick(async () => {
        formRef.value?.resetFields();

        if (type === 'edit' && row) {
            await getMenuDetail(row.id);
            Object.assign(formData, row);
        } else {
            Object.assign(formData, {
                menuId: undefined,
                name: '',
                permission: '',
                path: '',
                parentId: row?.parentId || 0,
                visible: '1',
                sortOrder: 1,
                menuType: row?.menuType || '0',
            });
        }
    });
};

const getMenuDetail = async (id: string) => {
    const res = await getStoreMenuDetails({ menuId: id });
    Object.assign(formData, res.data[0]);
};

const handleSubmit = async () => {
    const valid = await formRef.value.validate().catch(() => {});
    if (!valid) return false;

    try {
        loading.value = true;

        if (isEdit.value) {
            await updateStoreMenu(formData);
            useMessage().success('更新成功');
        } else {
            await addStoreMenu(formData);
            useMessage().success('新增成功');
        }

        visible.value = false;
        emit('refresh');
    } catch (err: any) {
        useMessage().error(err.msg || '操作失败');
    } finally {
        loading.value = false;
    }
};

defineExpose({
    openDialog,
});
</script>

<style scoped>
.dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}
</style>
