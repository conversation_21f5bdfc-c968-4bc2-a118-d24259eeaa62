<template>
    <div class="layout-padding">
        <div class="layout-padding-auto layout-padding-view">
            <!-- 搜索栏 -->
            <el-row v-show="showSearch" class="ml10">
                <el-form :inline="true" :model="queryForm" @keyup.enter="getDataList" ref="queryRef">
                    <el-form-item label="菜单名称" prop="menuName">
                        <el-input
                            placeholder="请输入菜单名称"
                            clearable
                            style="max-width: 180px"
                            v-model="queryForm.menuName"
                        />
                    </el-form-item>

                    <el-form-item>
                        <el-button @click="query" class="ml10" icon="search" type="primary">查询</el-button>
                        <el-button @click="resetQuery" icon="Refresh">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-row>

            <!-- 工具栏 -->
            <el-row>
                <div class="mb8" style="width: 100%">
                    <el-button @click="handleAdd" class="ml10" icon="folder-add" type="primary">新增菜单</el-button>
                    <right-toolbar
                        v-model:showSearch="showSearch"
                        class="ml10"
                        style="float: right; margin-right: 20px"
                        @queryTable="getDataList"
                    ></right-toolbar>
                </div>
            </el-row>

            <!-- 菜单表格 -->
            <el-table
                ref="tableRef"
                :data="tableList"
                row-key="id"
                :tree-props="{ children: 'children' }"
                style="width: 100%"
                v-loading="tableLoading"
                border
            >
                <el-table-column width="240" label="菜单名称" prop="name" show-overflow-tooltip />

                <el-table-column label="权限标识" prop="permission" show-overflow-tooltip />

                <el-table-column label="菜单类型" show-overflow-tooltip width="100">
                    <template #default="scope">
                        <el-tag v-if="scope.row.menuType === '0'" type="primary">菜单</el-tag>
                        <el-tag v-else type="warning">按钮</el-tag>
                    </template>
                </el-table-column>

                <el-table-column label="排序" prop="sortOrder" width="80" />

                <el-table-column label="显示状态" width="100">
                    <template #default="scope">
                        <el-tag v-if="!scope.row.meta.isHide" type="primary">显示</el-tag>
                        <el-tag v-else type="info">隐藏</el-tag>
                    </template>
                </el-table-column>

                <el-table-column label="操作" show-overflow-tooltip width="250">
                    <template #default="scope">
                        <el-button icon="edit-pen" @click="handleEdit(scope.row)" text type="primary">编辑</el-button>
                        <el-button
                            icon="folder-add"
                            @click="handleAddChild(scope.row)"
                            text
                            type="primary"
                            v-if="scope.row.menuType === '0'"
                        >
                            添加子项
                        </el-button>
                        <el-button
                            icon="delete"
                            :disabled="hasChildren(scope.row)"
                            @click="handleDelete(scope.row)"
                            text
                            type="danger"
                        >
                            删除
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>

        <!-- 菜单编辑对话框 -->
        <MenuDialog @refresh="getDataList()" ref="menuDialogRef" />
    </div>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { getStoreMenuTree, delStoreMenu } from '/@/api/store/menu';
import { useMessage } from '/@/hooks/message';
import type { MenuTreeQueryParams } from '/@/types/store';

// 引入组件
const MenuDialog = defineAsyncComponent(() => import('./components/MenuDialog.vue'));

// 定义变量
const tableRef = ref();
const menuDialogRef = ref();
const showSearch = ref(true);
const queryRef = ref();
const tableLoading = ref(false);

const queryForm = reactive<MenuTreeQueryParams>({
    parentId: undefined,
    menuName: '',
    type: undefined,
});

const menuTreeData = ref([]);

const tableList = computed(() => {
    return menuTreeData.value || [];
});

onMounted(() => {
    getDataList();
});

// 获取菜单数据
const getDataList = async () => {
    try {
        tableLoading.value = true;
        const params = { ...queryForm };
        // 移除空值参数
        Object.keys(params).forEach((key) => {
            if ((params as any)[key] === '' || (params as any)[key] === undefined) {
                delete (params as any)[key];
            }
        });
        const res = await getStoreMenuTree(params);
        menuTreeData.value = res.data || [];
    } catch (error) {
        ElMessage.error('加载菜单数据失败');
    } finally {
        tableLoading.value = false;
    }
};

// 查询
const query = () => {
    getDataList();
};

// 重置查询
const resetQuery = () => {
    queryRef.value.resetFields();
    queryForm.menuName = '';
    queryForm.type = undefined;
    queryForm.parentId = undefined;
    getDataList();
};

// 新增菜单
const handleAdd = () => {
    menuDialogRef.value.openDialog('add', {
        parentId: 0,
        menuType: '0',
        visible: '0',
        sortOrder: 1,
    });
};

// 添加子菜单
const handleAddChild = (row: any) => {
    menuDialogRef.value.openDialog('add', {
        parentId: row.id,
        menuType: '1', // 子项只能是按钮
        visible: '0',
        sortOrder: 1,
    });
};

// 编辑菜单
const handleEdit = (row: any) => {
    menuDialogRef.value.openDialog('edit', row);
};

// 判断是否有子节点
const hasChildren = (row: any) => {
    return (row.children || []).length > 0;
};

// 删除菜单
const handleDelete = async (row: any) => {
    try {
        await ElMessageBox.confirm(`确定要删除菜单"${row.name}"吗？`, '确认删除', { type: 'warning' });
        await delStoreMenu(row.id);
        useMessage().success('删除成功');
        getDataList();
    } catch (error) {
        if (error !== 'cancel') {
            useMessage().error('删除失败');
        }
    }
};
</script>

<style scoped>
.text-primary {
    color: var(--el-color-primary);
}

.text-muted {
    color: var(--el-text-color-secondary);
}
</style>
