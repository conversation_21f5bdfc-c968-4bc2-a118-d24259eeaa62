<template>
    <el-dialog :title="form.id ? '编辑' : '新增'" v-model="visible" :close-on-click-modal="false" draggable>
        <el-form
            ref="dataFormRef"
            :model="form"
            :rules="dataRules"
            formDialogRef
            label-width="90px"
            v-loading="loading"
        >
            <el-row :gutter="24">
                <el-col :span="12" class="mb20">
                    <el-form-item label="校管家主讲老师ID" prop="xgjLecturerId">
                        <el-input v-model="form.xgjLecturerId" placeholder="请输入校管家主讲老师ID" />
                    </el-form-item>
                </el-col>

                <el-col :span="12" class="mb20">
                    <el-form-item label="主讲老师名称" prop="lecturerName">
                        <el-input v-model="form.lecturerName" placeholder="请输入主讲老师名称" />
                    </el-form-item>
                </el-col>

                <el-col :span="12" class="mb20">
                    <el-form-item label="主讲老师状态: 0-启用; 1-禁用;" prop="lecturerState">
                        <el-input v-model="form.lecturerState" placeholder="请输入主讲老师状态: 0-启用; 1-禁用;" />
                    </el-form-item>
                </el-col>

                <el-col :span="12" class="mb20">
                    <el-form-item label="创建时间" prop="ctime">
                        <el-input v-model="form.ctime" placeholder="请输入创建时间" />
                    </el-form-item>
                </el-col>

                <el-col :span="12" class="mb20">
                    <el-form-item label="创建者" prop="creator">
                        <el-input v-model="form.creator" placeholder="请输入创建者" />
                    </el-form-item>
                </el-col>

                <el-col :span="12" class="mb20">
                    <el-form-item label="编辑时间" prop="mtime">
                        <el-input v-model="form.mtime" placeholder="请输入编辑时间" />
                    </el-form-item>
                </el-col>

                <el-col :span="12" class="mb20">
                    <el-form-item label="编辑人" prop="modifer">
                        <el-input v-model="form.modifer" placeholder="请输入编辑人" />
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="visible = false">取 消</el-button>
                <el-button type="primary" @click="onSubmit" :disabled="loading">确 认</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script setup lang="ts" name="LecturerDialog">
import { useDict } from '/@/hooks/dict';
import { useMessage } from '/@/hooks/message';
import { getObj, addObj, putObj, validateExist } from '/@/api/store/lecturer';
import { rule } from '/@/utils/validate';
const emit = defineEmits(['refresh']);

// 定义变量内容
const dataFormRef = ref();
const visible = ref(false);
const loading = ref(false);
// 定义字典

// 提交表单数据
const form = reactive({
    id: '',
    xgjLecturerId: '',
    lecturerName: '',
    lecturerState: '',
    ctime: '',
    creator: '',
    mtime: '',
    modifer: '',
});

// 定义校验规则
const dataRules = ref({});

// 打开弹窗
const openDialog = (id: string) => {
    visible.value = true;
    form.id = '';

    // 重置表单数据
    nextTick(() => {
        dataFormRef.value?.resetFields();
    });

    // 获取lecturer信息
    if (id) {
        form.id = id;
        getLecturerData(id);
    }
};

// 提交
const onSubmit = async () => {
    const valid = await dataFormRef.value.validate().catch(() => {});
    if (!valid) return false;

    try {
        loading.value = true;
        form.id ? await putObj(form) : await addObj(form);
        useMessage().success(form.id ? '修改成功' : '添加成功');
        visible.value = false;
        emit('refresh');
    } catch (err: any) {
        useMessage().error(err.msg);
    } finally {
        loading.value = false;
    }
};

// 初始化表单数据
const getLecturerData = (id: string) => {
    // 获取数据
    loading.value = true;
    getObj({ id: id })
        .then((res: any) => {
            Object.assign(form, res.data[0]);
        })
        .finally(() => {
            loading.value = false;
        });
};

// 暴露变量
defineExpose({
    openDialog,
});
</script>
