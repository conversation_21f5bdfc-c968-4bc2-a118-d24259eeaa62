<template>
    <div class="layout-padding">
        <div class="layout-padding-auto layout-padding-view">
            <el-row>
                <el-form
                    :inline="true"
                    :model="state.queryForm"
                    ref="queryRef"
                    class="demo-form-inline"
                    v-show="showSearch"
                >
                    <el-form-item label="校区名称">
                        <el-input v-model="state.queryForm.campusName" placeholder="请输入校区名称" clearable />
                    </el-form-item>
                    <el-form-item>
                        <el-button icon="search" type="primary" @click="getDataList">
                            {{ $t('common.queryBtn') }}
                        </el-button>
                        <el-button icon="Refresh" @click="resetQuery">{{ $t('common.resetBtn') }}</el-button>
                    </el-form-item>
                </el-form>
                <div class="mb8" style="width: 100%">
                    <el-button
                        icon="folder-add"
                        type="primary"
                        class="ml10"
                        @click="formDialogRef.openDialog()"
                        v-auth="'store_campus_add'"
                    >
                        新 增
                    </el-button>
                    <!-- <el-button plain icon="upload-filled" type="primary" class="ml10" @click="excelUploadRef.show()" v-auth="'sys_user_add'">
						导 入
					</el-button> -->
                    <el-button
                        plain
                        :disabled="multiple"
                        icon="Delete"
                        type="primary"
                        v-auth="'store_campus_del'"
                        @click="handleDelete(selectObjs)"
                    >
                        删 除
                    </el-button>
                    <right-toolbar
                        v-model:showSearch="showSearch"
                        @exportExcel="exportExcel"
                        class="ml10 mr20"
                        style="float: right"
                        @queryTable="getDataList"
                    ></right-toolbar>
                </div>
            </el-row>
            <el-table
                :data="state.dataList"
                v-loading="state.loading"
                border
                :cell-style="tableStyle.cellStyle"
                :header-cell-style="tableStyle.headerCellStyle"
                @selection-change="selectionChangHandle"
                @sort-change="sortChangeHandle"
            >
                <el-table-column type="selection" width="40" align="center" />
                <el-table-column type="index" label="#" width="40" />
                <el-table-column prop="xgjCampusId" label="校`管家校区ID(类型为主讲端时为空)" show-overflow-tooltip />
                <el-table-column prop="regionName" label="大区名称" show-overflow-tooltip />
                <el-table-column prop="campusNo" label="校区编号" show-overflow-tooltip />
                <el-table-column prop="campusName" label="校区名称" show-overflow-tooltip />
                <el-table-column prop="campusState" label="校区状态: 0-启用; 1-禁用;" show-overflow-tooltip>
                    <template #="{ row }">
                        {{ row.campusState === 0 ? '启用' : '禁用' }}
                    </template>
                </el-table-column>
                <el-table-column prop="campusType" label="校区类型: 1-主讲端; 2-教室端;" show-overflow-tooltip>
                    <template #="{ row }">
                        {{ row.campusType === 1 ? '主讲端' : '教室端' }}
                    </template>
                </el-table-column>
                <el-table-column prop="ctime" label="创建时间" show-overflow-tooltip />
                <el-table-column prop="creator" label="创建者" show-overflow-tooltip />
                <el-table-column prop="mtime" label="编辑时间" show-overflow-tooltip />
                <el-table-column prop="modifer" label="编辑者" show-overflow-tooltip />
                <el-table-column label="操作" width="150">
                    <template #default="scope">
                        <el-button
                            icon="edit-pen"
                            text
                            type="primary"
                            v-auth="'store_campus_edit'"
                            @click="formDialogRef.openDialog(scope.row.id)"
                            >编辑</el-button
                        >
                        <el-button
                            icon="delete"
                            text
                            type="primary"
                            v-auth="'store_campus_del'"
                            @click="handleDelete([scope.row.id])"
                            >删除</el-button
                        >
                    </template>
                </el-table-column>
            </el-table>
            <pagination
                @size-change="sizeChangeHandle"
                @current-change="currentChangeHandle"
                v-bind="state.pagination"
            />
        </div>

        <!-- 编辑、新增  -->
        <form-dialog ref="formDialogRef" @refresh="getDataList(false)" />

        <!-- 导入excel (需要在 upms-biz/resources/file 下维护模板) -->
        <upload-excel
            ref="excelUploadRef"
            title="导入"
            url="/store/campus/import"
            temp-url="/admin/sys-file/local/file/campus.xlsx"
            @refreshDataList="getDataList"
        />
    </div>
</template>

<script setup lang="ts" name="systemCampus">
import { BasicTableProps, useTable } from '/@/hooks/table';
import { fetchList, delObjs } from '/@/api/store/campus';
import { useMessage, useMessageBox } from '/@/hooks/message';
import { useDict } from '/@/hooks/dict';

// 引入组件
const FormDialog = defineAsyncComponent(() => import('./form.vue'));
// 定义查询字典

// 定义变量内容
const formDialogRef = ref();
const excelUploadRef = ref();
// 搜索变量
const queryRef = ref();
const showSearch = ref(true);
// 多选变量
const selectObjs = ref([]) as any;
const multiple = ref(true);

const state: BasicTableProps = reactive<BasicTableProps>({
    queryForm: {
        campusName: '',
    },
    pageList: fetchList,
});

//  table hook
const { getDataList, currentChangeHandle, sizeChangeHandle, sortChangeHandle, downBlobFile, tableStyle } =
    useTable(state);

// 清空搜索条件
const resetQuery = () => {
    // 清空搜索条件
    queryRef.value?.resetFields();
    // 清空多选
    selectObjs.value = [];
    state.queryForm = [];
    getDataList();
};

// 导出excel
const exportExcel = () => {
    downBlobFile('/store/campus/export', Object.assign(state.queryForm, { ids: selectObjs }), 'campus.xlsx');
};

// 多选事件
const selectionChangHandle = (objs: { id: string }[]) => {
    selectObjs.value = objs.map(({ id }) => id);
    multiple.value = !objs.length;
};

// 删除操作
const handleDelete = async (ids: string[]) => {
    try {
        await useMessageBox().confirm('此操作将永久删除');
    } catch {
        return;
    }

    try {
        await delObjs(ids);
        getDataList();
        useMessage().success('删除成功');
    } catch (err: any) {
        useMessage().error(err.msg);
    }
};
</script>
