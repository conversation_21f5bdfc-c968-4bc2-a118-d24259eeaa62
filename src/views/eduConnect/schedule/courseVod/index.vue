<template>
    <div class="layout-padding">
        <div class="layout-padding-auto layout-padding-view" v-if="isMask">
            <el-row class="ml10" v-show="showSearch">
                <el-form
                    :model="state.queryForm"
                    ref="queryRef"
                    :inline="true"
                    label-width="80px"
                    @keyup.enter="getDataList"
                >
                    <el-form-item label="门店名称" prop="storeId">
                        <fuzzy-search
                            class="w-[240px]"
                            placeholder="请选择门店名称"
                            v-model="state.queryForm.storeId"
                            :requestUrl="getStoreSelectList"
                            request-name="campusName"
                            data-label="campusName"
                            clearable
                        ></fuzzy-search>
                    </el-form-item>
                    <el-form-item label="课程名称" prop="courseId">
                        <fuzzy-search
                            class="w-[240px]"
                            placeholder="请选择课程名称"
                            data-key="id"
                            v-model="state.queryForm.courseId"
                            :requestUrl="getlistByCourseName"
                            request-name="courseName"
                            data-label="courseName"
                            clearable
                        />
                    </el-form-item>

                    <el-form-item label="上课时间" prop="attendClassDate">
                        <el-date-picker
                            class="w-[240px]"
                            v-model="queryDate"
                            type="daterange"
                            range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            @change="queryDateChange(queryDate)"
                        />
                    </el-form-item>

                    <el-form-item label="阶段" prop="stage">
                        <el-select class="w-[240px]" v-model="state.queryForm.stage" placeholder="请选择阶段">
                            <el-option
                                v-for="item in stage_list"
                                :key="item.value"
                                :value="item.value"
                                :label="item.label"
                                clearable
                            />
                        </el-select>
                    </el-form-item>

                    <el-form-item label="主讲老师" prop="lectureId">
                        <fuzzy-search
                            class="w-[240px]"
                            v-model="state.queryForm.lectureId"
                            :requestUrl="() => fetchAllTeacher(1)"
                            placeholder="请选择主讲老师"
                            data-key="userId"
                            data-label="name"
                            :get-label="(row) => (row.nickname ? `${row.name}(${row.nickname})` : row.name)"
                            :remote="false"
                        />
                    </el-form-item>

                    <el-form-item>
                        <el-button @click="getDataList" formDialogRef icon="search" type="primary">
                            {{ $t('common.queryBtn') }}
                        </el-button>
                        <el-button @click="resetQuery" formDialogRef icon="Refresh">
                            {{ $t('common.resetBtn') }}
                        </el-button>
                    </el-form-item>
                </el-form>
            </el-row>
            <el-row>
                <div class="mb8" style="width: 100%">
                    <right-toolbar
                        v-model:showSearch="showSearch"
                        class="ml10 mr20"
                        style="float: right"
                        @queryTable="getDataList"
                    ></right-toolbar>
                </div>
            </el-row>
            <el-table
                :data="state.dataList"
                v-loading="state.loading"
                border
                :cell-style="tableStyle.cellStyle"
                :header-cell-style="tableStyle.headerCellStyle"
                @sort-change="sortChangeHandle"
            >
                <el-table-column type="index" label="序号" width="60" />
                <el-table-column prop="campusName" label="门店名称" show-overflow-tooltip min-width="200px" />
                <el-table-column prop="courseName" label="已约课程" show-overflow-tooltip min-width="200px" />
                <el-table-column prop="stage" label="阶段" show-overflow-tooltip width="80px">
                    <template #default="{ row }">
                        <dict-tag
                            :options="stage_list"
                            :type="setTagType(row.stage, { '0': 'warning', '1': 'primary' })"
                            :value="row.stage"
                        ></dict-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="lectureName" label="主讲老师" show-overflow-tooltip width="100px" />
                <el-table-column prop="className" label="班级" show-overflow-tooltip width="200px" />
                <el-table-column prop="classroomName" label="教室" show-overflow-tooltip min-width="200px" />
                <el-table-column prop="teacherName" label="指导师" show-overflow-tooltip width="100px" />
                <el-table-column prop="scheduled" label="是否排课" show-overflow-tooltip width="100px">
                    <template #default="{ row }">
                        <dict-tag
                            :options="scheduled_type"
                            :type="setTagType(row.scheduled, { '0': 'warning', '1': 'primary' })"
                            :value="row.scheduled"
                        ></dict-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="courseVodStatus" label="是否结束" show-overflow-tooltip width="100px">
                    <template #default="{ row }">
                        <span v-if="row.scheduled === 0">-</span>
                        <dict-tag
                            v-else
                            :options="closed_type"
                            :type="setTagType(row.courseVodStatus, { '0': 'warning', '1': 'primary' })"
                            :value="row.courseVodStatus"
                        ></dict-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="updateBy" label="操作人" show-overflow-tooltip />
                <el-table-column prop="updateTime" label="更新时间" width="200" />
                <el-table-column label="操作" width="80" fixed="right">
                    <template #default="{ row }">
                        <el-button text type="primary" v-auth="'edujw_courseVod_detail'" @click="linkToDetail(row)">
                            详情
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>

            <pagination
                @size-change="sizeChangeHandle"
                @current-change="currentChangeHandle"
                v-bind="state.pagination"
            />
        </div>

        <form-dialog ref="formDialogRef" @refresh="getDataList(false)" />
    </div>
</template>

<script setup lang="ts" name="systemSsClass">
import { BasicTableProps, useTable } from '/@/hooks/table';
import fuzzySearch from '/@/components/EduComponents/fuzzySearch.vue';
import { useDict } from '/@/hooks/dict';
import { courseVodPage } from '/@/api/eduConnect/courseVod';
import { fetchAllTeacher } from '/@/api/eduConnect/teacher';
import { getSchoolStoreList } from '/@/api/eduConnect/campusStore';
import { getlistByCourseName } from '/@/api/eduConnect/demand';
import { formatDate } from '/@/utils/formatTime';

// 定义查询字典
const { stage_list, closed_type, scheduled_type } = useDict('stage_list', 'closed_type', 'scheduled_type');

// 定义变量内容
const formDialogRef = ref();

// 搜索变量
const queryRef = ref();
const showSearch = ref(true);
// 多选变量
const $router = useRouter();
const router = useRouter();
const isMask = ref(true);
const queryDate = ref();

const state: BasicTableProps = reactive<BasicTableProps>({
    queryForm: {},
    isShowWarning: true,
    pageList: courseVodPage,
});

const setTagType = (key: any, obj: any) => {
    return obj[key];
};

//  table hook
const { getDataList, currentChangeHandle, sizeChangeHandle, sortChangeHandle, tableStyle } = useTable(state);

// 清空搜索条件
const resetQuery = () => {
    // 清空搜索条件
    queryRef.value?.resetFields();
    // 清空日期
    queryDate.value = undefined;
    state.queryForm = {};
    getDataList();
};

//处理日期格式
const queryDateChange = (date: Date[]) => {
    state.queryForm.classStartDateTime = date ? formatDate(date[0] as Date, 'YYYY-mm-dd') + ' 00:00:00' : '';
    state.queryForm.classEndDateTime = date ? formatDate(date[1] as Date, 'YYYY-mm-dd') + ' 23:59:59' : '';
};

// 查询门店
const getStoreSelectList = async (params: {}) => {
    const res = await getSchoolStoreList(params);
    return { data: res.data.records };
};

// 查看详情
const linkToDetail = (row: Record<string, number>) => {
    $router.push({
        path: '/eduConnect/schedule/courseVod/detail',
        query: {
            id: row.id,
            store: encodeURIComponent(row.campusName),
            course: encodeURIComponent(row.courseName),
            scheduled: row.scheduled,
            from: encodeURIComponent(router.currentRoute.value.fullPath),
        },
    });
    isMask.value = false;
};
</script>

<style scoped lang="scss"></style>
