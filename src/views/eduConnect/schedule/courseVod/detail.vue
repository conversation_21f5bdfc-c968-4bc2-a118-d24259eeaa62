<template>
    <div class="layout-padding">
        <div class="layout-padding-auto layout-padding-view">
            <navi-bar
                class="mb20"
                :title="route.query.title as string"
                :sub-title="route.query.author as string"
                :from="decodeURIComponent(route.query.from as string)"
            />
            <div class="w-[100%] p-[20px] border-gray-300 border rounded shadow-md">
                <div class="flex justify-between w-1/2 text-[#6c6c6c]">
                    <div>
                        <span class="text-xl">门店名称：</span>
                        <span class="text-xl text-[#101010] font-normal">{{
                            decodeURIComponent(route.query.store as string)
                        }}</span>
                    </div>
                    <div>
                        <span class="text-xl">已约课程：</span>
                        <span class="text-xl text-[#101010] font-normal">{{
                            decodeURIComponent(route.query.course as string)
                        }}</span>
                    </div>
                </div>
            </div>

            <el-table
                class="mt-[20px]"
                :data="state.dataList"
                v-loading="state.loading"
                border
                :cell-style="tableStyle.cellStyle"
                :header-cell-style="tableStyle.headerCellStyle"
                @sort-change="sortChangeHandle"
            >
                <el-table-column type="index" label="序号" width="60" />
                <el-table-column prop="lessonName" label="课节名称" show-overflow-tooltip />
                <el-table-column prop="bookName" label="关联书籍" show-overflow-tooltip />
                <el-table-column prop="lectureName" label="主讲老师" show-overflow-tooltip />
                <el-table-column prop="className" label="班级" show-overflow-tooltip />
                <el-table-column prop="classroomName" label="教室" show-overflow-tooltip />
                <el-table-column prop="teacherName" label="指导师" show-overflow-tooltip />
                <el-table-column prop="timeSlotName" label="日期/时段" show-overflow-tooltip>
                    <template #default="scope">
                        {{ scope.row.classDate ? `${scope.row.classDate} ${scope.row.timeSlotName}` : '未排期' }}
                    </template>
                </el-table-column>
                <el-table-column prop="courseVodPlanStatus" label="是否结束" show-overflow-tooltip>
                    <template #default="{ row }">
                        <span v-if="route.query.scheduled == 0">-</span>
                        <dict-tag
                            v-else
                            :options="closed_type"
                            :type="setTagType(row.courseVodPlanStatus, { '0': 'warning', '1': 'primary' })"
                            :value="row.courseVodPlanStatus"
                        ></dict-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="updateBy" label="操作人" show-overflow-tooltip />
                <el-table-column prop="updateTime" label="更新时间" show-overflow-tooltip />
            </el-table>
        </div>
    </div>
</template>

<script setup lang="ts">
import { useTable, BasicTableProps } from '/@/hooks/table';
import { courseVodDetail } from '/@/api/eduConnect/courseVod';
import NaviBar from '/@/components/NaviBar/index.vue';
import { useDict } from '/@/hooks/dict';

// 定义查询字典
const { closed_type } = useDict('closed_type');

const route = useRoute();

const state: BasicTableProps = reactive<BasicTableProps>({
    queryForm: {
        id: route.query.id,
    },
    props: {
        item: 'courseVodPlanVOList',
    },
    pageList: courseVodDetail,
});

const setTagType = (key: any, obj: any) => {
    return obj[key];
};

const { sortChangeHandle, tableStyle } = useTable(state);
</script>

<style lang="scss" scoped></style>
