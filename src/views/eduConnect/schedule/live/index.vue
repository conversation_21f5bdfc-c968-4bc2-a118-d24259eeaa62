<template>
    <div class="layout-padding">
        <div class="layout-padding-auto layout-padding-view" v-if="isMask">
            <el-row class="ml10 top-3" v-show="showSearch">
                <el-form
                    :inline="true"
                    :model="state.queryForm"
                    ref="queryRef"
                    label-width="80px"
                    @keyup.enter="getDataList"
                >
                    <!-- 查询直播间字段 -->
                    <el-form-item label="门店名称">
                        <fuzzy-search
                            class="w-[240px]"
                            placeholder="请输入/选择门店名称"
                            v-model="state.queryForm.storeId"
                            :requestUrl="getStoreSelectList"
                            request-name="campusName"
                            data-label="campusName"
                            clearable
                        ></fuzzy-search>
                    </el-form-item>
                    <el-form-item label="课程名称">
                        <fuzzy-search
                            class="w-[240px]"
                            placeholder="请输入/选择课程名称"
                            v-model="state.queryForm.courseId"
                            :requestUrl="getCourseListList"
                            request-name="courseName"
                            data-label="courseName"
                            clearable
                        ></fuzzy-search>
                    </el-form-item>
                    <el-form-item label="上课时间" prop="live_room_id">
                        <el-date-picker
                            class="w-[240px]"
                            v-model="queryDate"
                            type="daterange"
                            range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            @change="queryDateChange(queryDate)"
                        />
                    </el-form-item>
                    <el-form-item label="阶段" prop="live_room_id">
                        <el-select
                            v-model="state.queryForm.stage"
                            placeholder="请选择阶段"
                            class="w-[240px]"
                            clearable
                            filterable
                        >
                            <el-option
                                v-for="item in stage_list"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="主讲老师" prop="live_room_id">
                        <fuzzy-search
                            class="w-[260px]"
                            v-model="state.queryForm.lectureId"
                            :requestUrl="() => fetchAllTeacher(1)"
                            placeholder="请输入主讲老师名称"
                            data-key="userId"
                            data-label="name"
                            :get-label="(row) => (row.nickname ? `${row.name}(${row.nickname})` : row.name)"
                            :remote="false"
                        />
                    </el-form-item>
                    <el-form-item>
                        <el-button @click="getDataList" formDialogRef icon="search" type="primary">
                            {{ $t('common.queryBtn') }}
                        </el-button>
                        <el-button @click="resetQuery" formDialogRef icon="Refresh"
                            >{{ $t('common.resetBtn') }}
                        </el-button>
                    </el-form-item>
                </el-form>
            </el-row>
            <el-row>
                <div class="mb8" style="width: 100%">
                    <!-- <el-button
                        icon="folder-add"
                        type="primary"
                        class="ml10"
                        @click="formDialogRef.openDialog()"
                        v-auth="'edusystem_ssClass_add'"
                    >
                        新增
                    </el-button>
                    <el-button
                        plain
                        :disabled="multiple"
                        icon="Delete"
                        type="primary"
                        v-auth="'edusystem_ssClass_del'"
                        @click="handleDelete(null, selectObjs)"
                    >
                        删除
                    </el-button> -->
                    <right-toolbar
                        v-model:showSearch="showSearch"
                        class="ml10 mr20"
                        style="float: right"
                        @queryTable="getDataList"
                    ></right-toolbar>
                </div>
            </el-row>
            <el-table
                :data="state.dataList"
                v-loading="state.loading"
                border
                :cell-style="tableStyle.cellStyle"
                :header-cell-style="tableStyle.headerCellStyle"
                @sort-change="sortChangeHandle"
            >
                <el-table-column type="index" label="序号" width="60" />
                <el-table-column prop="storeName" label="门店名称" show-overflow-tooltip min-width="200px" />
                <el-table-column prop="courseName" label="已约课程" show-overflow-tooltip min-width="200px" />
                <el-table-column
                    prop="stage"
                    label="阶段"
                    show-overflow-tooltip
                    width="80px
                "
                >
                    <template #default="{ row }">
                        <dict-tag
                            :options="stage_list"
                            :type="setTagType(row.stage, { '0': 'warning', '1': 'primary' })"
                            :value="row.stage"
                        ></dict-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="lectureName" label="主讲老师" show-overflow-tooltip />
                <el-table-column prop="className" label="班级" show-overflow-tooltip min-width="150px" />
                <el-table-column prop="classroomName" label="教室" show-overflow-tooltip min-width="150px" />
                <el-table-column prop="teacherName" label="指导师" show-overflow-tooltip />
                <el-table-column
                    prop="close"
                    label="是否结束"
                    show-overflow-tooltip
                    width="100px
                "
                >
                    <template #default="{ row }">
                        <dict-tag
                            :options="closed_type"
                            :type="setTagType(row.close, { '0': 'warning', '1': 'primary' })"
                            :value="row.close"
                        ></dict-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="updateBy" label="操作人" show-overflow-tooltip />
                <el-table-column prop="updateTime" label="更新时间" show-overflow-tooltip />
                <el-table-column label="操作" width="80">
                    <template #default="{ row }">
                        <el-button text type="primary" @click="linkToDetails(row)">详情</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <pagination
                @size-change="sizeChangeHandle"
                @current-change="currentChangeHandle"
                v-bind="state.pagination"
            />
        </div>
    </div>
</template>

<script setup lang="ts" name="systemLiveSchedule">
import { BasicTableProps, useTable } from '/@/hooks/table';
import { fetchList } from '/@/api/eduConnect/courseLive';
import fuzzySearch from '/@/components/EduComponents/fuzzySearch.vue';
import { fetchCourseList } from '/@/api/integraEdu/courseZone';
import { fetchAllTeacher } from '/@/api/eduConnect/teacher';
import { getSchoolStoreList } from '/@/api/eduConnect/campusStore';
import { useDict } from '/@/hooks/dict';
import { formatDate } from '/@/utils/formatTime';

const isMask = ref(true);
const router = useRouter();
const route = useRoute();
// 定义查询字典
let { closed_type, stage_list } = useDict('closed_type', 'stage_list');

// 定义变量内容
const queryRef = ref();
const showSearch = ref(true);
const queryDate = ref();

const state: BasicTableProps = reactive<BasicTableProps>({
    queryForm: {},
    isShowWarning: true,
    pageList: fetchList,
});
const setTagType = (key: any, obj: any) => {
    return obj[key];
};

//  table hook
const { getDataList, currentChangeHandle, sizeChangeHandle, sortChangeHandle, tableStyle } = useTable(state);

const linkToDetails = (row: any) => {
    router.push({
        path: '/eduConnect/schedule/live/details',
        query: {
            id: row.id,
            store: encodeURIComponent(row.storeName),
            course: encodeURIComponent(row.courseName),
            from: encodeURIComponent(route.fullPath),
        },
    });
};

// 清空搜索条件
const resetQuery = () => {
    queryRef.value?.resetFields();
    queryDate.value = undefined;
    state.queryForm = {};
    getDataList();
};

//处理日期格式
const queryDateChange = (date: Date[]) => {
    state.queryForm.startTime = date ? formatDate(date[0] as Date, 'YYYY-mm-dd') + ' 00:00:00' : '';
    state.queryForm.endTime = date ? formatDate(date[1] as Date, 'YYYY-mm-dd') + ' 23:59:59' : '';
};

const getStoreSelectList = async (params: {}) => {
    const res = await getSchoolStoreList(params);
    return { data: res.data.records };
};

const getCourseListList = async (params: {}) => {
    const res = await fetchCourseList(params);
    return { data: res.data.records };
};
</script>

<style scoped lang="scss">
.el-dropdown {
    display: flex;
    align-items: center;
    justify-content: center;

    :deep(.el-dropdown-link) {
        cursor: pointer;
        color: var(--el-color-primary);
        display: flex;
        align-items: center;
    }
}

:deep(.el-overlay .el-overlay-dialog .el-dialog .el-dialog__body) {
    text-align: center;
}

:deep(.el-dialog__header) {
    padding: 0;
}
</style>
