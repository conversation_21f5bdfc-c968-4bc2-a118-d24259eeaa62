<template>
    <div class="layout-padding">
        <div class="layout-padding-auto layout-padding-view">
            <navi-bar
                ref="naviBarRef"
                class="mb28"
                :title="'查看图片'"
                :from="decodeURIComponent(route.query.from as string)"
            />
            <div class="w-[100%] p-[20px] border-gray-300 border rounded shadow-md">
                <div class="flex justify-between w-4/5 text-[#6c6c6c]">
                    <div>
                        <span class="text-xl">门店名称 </span>
                        <span class="text-xl text-[#101010] font-normal">{{
                            decodeURIComponent(route.query.storeName as string)
                        }}</span>
                    </div>
                    <div>
                        <span class="text-xl">课节名称 </span>
                        <span class="text-xl text-[#101010] font-normal">{{
                            decodeURIComponent(route.query.lessonName as string)
                        }}</span>
                    </div>

                    <div>
                        <span class="text-xl">班级名称 </span>
                        <span class="text-xl text-[#101010] font-normal">{{
                            decodeURIComponent(route.query.className as string)
                        }}</span>
                    </div>

                    <div>
                        <span class="text-xl">上课时间 </span>
                        <span class="text-xl text-[#101010] font-normal">{{
                            decodeURIComponent(route.query.classStartTimeStr as string)
                        }}</span>
                    </div>
                </div>
            </div>
            <div class="w-[100%] p-[20px] border-gray-200 border rounded shadow-md mt-2 min-h-150">
                <div>
                    <span class="text-lg font-bold">上课图片</span>
                </div>
                <div class="w-[100%] flex justify-around m20 mt15">
                    <div class="flex flex-col">
                        <el-image
                            :src="form.pictureList.filter((item) => item.sort === 1).map((item) => item.photoUrl)[0]"
                            :preview-src-list="
                                form.pictureList.filter((item) => item.sort === 1).map((item) => item.photoUrl)
                            "
                            class="border-solid border-2 size-[230px]"
                        >
                            <template #error>
                                <div class="el-image__error">未拍照</div>
                            </template>
                        </el-image>
                        <span class="mt-4 text-center text-lg">上课15分钟截图</span>
                    </div>

                    <div class="flex flex-col">
                        <el-image
                            :src="form.pictureList.filter((item) => item.sort === 2).map((item) => item.photoUrl)[0]"
                            :preview-src-list="
                                form.pictureList.filter((item) => item.sort === 2).map((item) => item.photoUrl)
                            "
                            class="border-solid border-2 size-[230px]"
                        >
                            <template #error>
                                <div class="el-image__error">未拍照</div>
                            </template>
                        </el-image>
                        <span class="mt-4 text-center text-lg">上课30分钟截图</span>
                    </div>

                    <div class="flex flex-col">
                        <el-image
                            :src="form.pictureList.filter((item) => item.sort === 3).map((item) => item.photoUrl)[0]"
                            :preview-src-list="
                                form.pictureList.filter((item) => item.sort === 3).map((item) => item.photoUrl)
                            "
                            class="border-solid border-2 size-[230px]"
                        >
                            <template #error>
                                <div class="el-image__error">未拍照</div>
                            </template>
                        </el-image>
                        <span class="mt-4 text-center text-lg">上课45分钟截图</span>
                    </div>
                </div>
            </div>
            <div class="w-[100%] p-[20px] border-gray-200 border rounded shadow-md mt-2">
                <div>
                    <span class="text-lg font-bold">应出勤学员 ({{ form.expectedNames.length }})</span>
                    <div class="flex flex-wrap ml15 min-h-10 mt-2">
                        <div v-for="(item, index) in form.expectedNames" :key="index">
                            <span class="text-base"
                                >{{ item }}<span v-if="form.expectedNames.length - 1 != index">,</span></span
                            >
                        </div>
                    </div>
                </div>
                <div>
                    <span class="text-lg font-bold">未出勤学员 ({{ form.unActualNames.length }})</span>
                    <div class="flex flex-wrap ml15 min-h-10 mt-2">
                        <div v-for="(item, index) in form.unActualNames" :key="index">
                            <span class="text-base"
                                >{{ item }}<span v-if="form.unActualNames.length - 1 != index">,</span></span
                            >
                        </div>
                    </div>
                </div>
                <div>
                    <span class="text-lg font-bold">已出勤学员 ({{ form.actualNames.length }})</span>
                    <div class="flex flex-wrap ml15 min-h-10 mt-2">
                        <div v-for="(item, index) in form.actualNames" :key="index">
                            <span class="text-base"
                                >{{ item }}<span v-if="form.actualNames.length - 1 != index">,</span></span
                            >
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts" name="courseConsumptionDetails">
import NaviBar from '/@/components/NaviBar/index.vue';
import { fetchDetail } from '/@/api/eduConnect/timetable';

const naviBarRef = ref();
const route = useRoute();
const form = reactive<{
    expectedNames: string[];
    actualNames: string[];
    unActualNames: string[];
    pictureList: {
        photoUrl: string;
        sort: number;
    }[];
}>({
    expectedNames: [],
    actualNames: [],
    unActualNames: [],
    pictureList: [],
});

onMounted(() => {
    fetchDetail(route.query.id as string).then((res) => {
        Object.assign(form, res.data || {});
    });
});
</script>

<style lang="scss" scoped></style>
