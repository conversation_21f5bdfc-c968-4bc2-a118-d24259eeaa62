<template>
    <div class="layout-padding">
        <div class="layout-padding-auto layout-padding-view">
            <el-row class="ml10" v-show="showSearch">
                <el-form :inline="true" :model="state.queryForm" ref="queryRef" label-width="80px">
                    <el-form-item label="设备号" prop="deviceNo">
                        <el-input
                            placeholder="请输入设备号"
                            class="w-[240px]"
                            clearable
                            v-model="state.queryForm.deviceNo"
                        />
                    </el-form-item>
                    <el-form-item label="门店名称" prop="lecturerId">
                        <fuzzy-search
                            class="w-[240px]"
                            placeholder="输入搜索授权门店"
                            v-model="state.queryForm.campusId"
                            :requestUrl="getCampusList"
                            request-name="query"
                            data-label="campusName"
                            clearable
                        ></fuzzy-search>
                    </el-form-item>
                    <el-form-item label="设备类型" prop="publicName">
                        <el-select
                            class="w-[240px]"
                            v-model="state.queryForm.deviceType"
                            placeholder="请选择设备类型"
                            clearable
                        >
                            <el-option
                                v-for="item in device_type"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="设备状态" prop="deviceState">
                        <el-select
                            class="w-[240px]"
                            v-model="state.queryForm.deviceState"
                            placeholder="请选择设备状态"
                            clearable
                        >
                            <el-option
                                v-for="item in device_state"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="是否欠费" prop="deviceArrears">
                        <el-select
                            class="w-[240px]"
                            v-model="state.queryForm.deviceArrears"
                            placeholder="请选择是否欠费"
                            clearable
                        >
                            <el-option
                                v-for="item in device_arrears"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="设备名称" prop="deviceName">
                        <el-input
                            placeholder="请输入设备名称"
                            clearable
                            class="w-[240px]"
                            v-model="state.queryForm.deviceName"
                        />
                    </el-form-item>
                    <el-form-item>
                        <el-button @click="getDataList" formDialogRef icon="search" type="primary">
                            {{ $t('common.queryBtn') }}
                        </el-button>
                        <el-button @click="resetQuery" formDialogRef icon="Refresh"
                            >{{ $t('common.resetBtn') }}
                        </el-button>
                    </el-form-item>
                </el-form>
            </el-row>
            <el-row>
                <div class="mb8" style="width: 100%">
                    <!-- <el-button icon="folder-add" type="primary" class="ml10" @click="formDialogRef.openDialog()"
            v-auth="'edusystem_ssDevice_add'">
            新 增
          </el-button> -->
                    <el-button
                        plain
                        :disabled="multiple"
                        class="ml10"
                        icon="Delete"
                        type="primary"
                        v-auth="'edusystem_ssDevice_del'"
                        @click="handleDelete(selectObjs)"
                    >
                        删除
                    </el-button>
                    <right-toolbar
                        v-model:showSearch="showSearch"
                        :export="false"
                        @exportExcel="exportExcel"
                        class="ml10 mr20"
                        style="float: right"
                        @queryTable="getDataList"
                    ></right-toolbar>
                </div>
            </el-row>
            <el-table
                :data="state.dataList"
                v-loading="state.loading"
                border
                :cell-style="tableStyle.cellStyle"
                :header-cell-style="tableStyle.headerCellStyle"
                @selection-change="selectionChangHandle"
                @sort-change="sortChangeHandle"
            >
                <el-table-column type="selection" width="45" align="center" />
                <el-table-column type="index" label="序号" width="60" />
                <el-table-column prop="deviceName" label="设备名称" show-overflow-tooltip min-width="120" />
                <el-table-column prop="deviceNo" label="设备号" show-overflow-tooltip min-width="320" />
                <el-table-column prop="campusName" label="所属门店" show-overflow-tooltip min-width="120">
                    <template #default="{ row }">
                        <div v-if="row.deviceType === DeviceType.classroomType">
                            {{ row.campusName }}
                        </div>
                        <div v-if="row.deviceType === DeviceType.speakerType">总部主讲端门店</div>
                    </template>
                </el-table-column>
                <el-table-column prop="classRoomName" label="所属教室" show-overflow-tooltip min-width="120">
                    <template #default="{ row }">
                        <div v-if="row.deviceType === DeviceType.speakerType">
                            <dict-tag :options="live_room" type="default" :value="row.classRoomId"></dict-tag>
                        </div>
                        <div v-if="row.deviceType === DeviceType.classroomType">
                            {{ row.classRoomName }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column prop="deviceType" label="设备类型" show-overflow-tooltip min-width="100">
                    <template #default="scope">
                        <dict-tag
                            :options="device_type"
                            :type="
                                setTagType(scope.row.deviceType, {
                                    [DeviceType.speakerType]: 'primary',
                                    [DeviceType.classroomType]: 'info',
                                })
                            "
                            :value="scope.row.deviceType"
                        ></dict-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="deviceState" label="设备状态" show-overflow-tooltip min-width="100">
                    <template #default="scope">
                        <el-switch
                            v-model="scope.row.deviceState"
                            :active-value="DeviceState.enable"
                            :inactive-value="DeviceState.disable"
                            class="ml-2"
                            width="60"
                            inline-prompt
                            active-text="启用"
                            inactive-text="禁用"
                            @change="changeSwitch(scope.row.id)"
                        />
                        <!-- <dict-tag :options="device_state"
              :type="setTagType(scope.row.deviceState, { [DeviceState.enable]: 'success', [DeviceState.disable]: 'danger' })"
              :value="scope.row.deviceState"></dict-tag> -->
                    </template>
                </el-table-column>
                <!-- <el-table-column prop="deviceActive" label="设备是否激活:0-未激活;1-已激活" show-overflow-tooltip>
          <template #default="scope">
            <dict-tag :options="device_active" :value="scope.row.deviceActive"></dict-tag>
          </template>
        </el-table-column> -->
                <!-- <el-table-column prop="indateStart" label="有效期开始时间" show-overflow-tooltip />
        <el-table-column prop="indateEnd" label="有效期结束时间" show-overflow-tooltip /> -->
                <el-table-column label="设备有效期" show-overflow-tooltip min-width="120">
                    <template #default="scope">
                        <span v-if="scope.row.indateForever == '1'">永久</span>
                        <span v-else-if="scope.row.indateStart && scope.row.indateEnd">
                            {{ setDate(scope.row.indateStart) }}至{{ setDate(scope.row.indateEnd) }}
                        </span>
                        <span v-else>时间无效</span>
                    </template>
                </el-table-column>
                <el-table-column label="设备日志" show-overflow-tooltip min-width="110">
                    <template #default="scope">
                        <el-button
                            icon="upload"
                            :disabled="!scope.row.deviceType || scope.row.deviceType === DeviceType.speakerType"
                            text
                            type="primary"
                            @click="handleUploadLog(scope.row)"
                        >
                            上传日志
                        </el-button>
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="210" fixed="right">
                    <template #default="scope">
                        <el-button
                            icon="edit-pen"
                            text
                            type="primary"
                            @click="formDialogRef.openDialog(scope.row.id)"
                            v-auth="'edusystem_ssDevice_edit'"
                        >
                            编辑
                        </el-button>

                        <el-button
                            icon="Link"
                            text
                            type="primary"
                            @click="handleUnbind(scope.row.id)"
                            v-auth="'edusystem_ssDevice_unbind'"
                            :disabled="scope.row.classRoomId === '-1' || scope.row.classRoomId === null"
                        >
                            解绑
                        </el-button>
                        <el-button
                            icon="delete"
                            text
                            type="primary"
                            @click="handleDelete([scope.row.id])"
                            v-auth="'edusystem_ssDevice_del'"
                            :disabled="!(scope.row.classRoomId === '-1' || scope.row.classRoomId === null)"
                        >
                            删除
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
            <pagination
                @size-change="sizeChangeHandle"
                @current-change="currentChangeHandle"
                v-bind="state.pagination"
            />
        </div>

        <!-- 编辑、新增  -->
        <form-dialog ref="formDialogRef" @refresh="getDataList(false)" />

        <el-dialog v-model="showLogDialog" title="上传日志" width="400px">
            <el-form
                :model="{ logDays: logDays }"
                :rules="{ logDays: logDaysRules }"
                ref="logFormRef"
                label-width="100px"
            >
                <el-form-item label="日志天数" prop="logDays">
                    <el-input-number v-model="logDays" :min="1" :max="7" :step="1" :controls="false" />
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button @click="showLogDialog = false">取消</el-button>
                <el-button type="primary" @click="confirmUploadLog">确定</el-button>
            </template>
        </el-dialog>
    </div>
</template>

<script setup lang="ts" name="systemSsDevice">
import { BasicTableProps, useTable } from '/@/hooks/table';
import { fetchList, delObjs, unbind, changeDeviceState, uploadLog } from '../../../../api/eduConnect/ssDevice';
import { useMessage, useMessageBox } from '/@/hooks/message';
import { useDict } from '/@/hooks/dict';
import { getCampusList } from '/@/api/eduConnect/ssQuery';
import fuzzySearch from '/@/components/EduComponents/fuzzySearch.vue';
import { DeviceType, DeviceState, setTagType } from '/@/components/EduComponents/enums/eduEnums';
import moment from 'moment';

// 引入组件
const FormDialog = defineAsyncComponent(() => import('./form.vue'));
// 定义查询字典

const { device_state, device_type, live_room, device_arrears } = useDict(
    'device_state',
    'device_type',
    'live_room',
    'device_arrears'
);
// 定义变量内容
const formDialogRef = ref();
// 搜索变量
const queryRef = ref();
const showSearch = ref(true);
// 多选变量
const selectObjs = ref([]) as any;
const multiple = ref(true);

const state: BasicTableProps = reactive<BasicTableProps>({
    queryForm: {},
    pageList: fetchList,
});

//  table hook
const { getDataList, currentChangeHandle, sizeChangeHandle, sortChangeHandle, downBlobFile, tableStyle } =
    useTable(state);

// 清空搜索条件
const resetQuery = () => {
    state.queryForm = {};
    // 清空搜索条件
    queryRef.value?.resetFields();
    // 清空多选
    selectObjs.value = [];
    getDataList();
};

// 导出excel
const exportExcel = () => {
    downBlobFile('/edusystem/ssDevice/export', Object.assign(state.queryForm, { ids: selectObjs }), 'ssDevice.xlsx');
};

// 多选事件
const selectionChangHandle = (objs: { id: string }[]) => {
    selectObjs.value = objs.map(({ id }) => id);
    multiple.value = !objs.length;
};
const changeSwitch = async (id: string) => {
    try {
        await changeDeviceState([id]);
        getDataList(false);
    } catch {
        return;
    }
};
const setDate = (date: string) => {
    return moment(date).format('YYYY-MM-DD');
};

// 删除操作
const handleDelete = async (ids: string[]) => {
    try {
        await useMessageBox().confirm('此操作将永久删除');
    } catch {
        return;
    }

    try {
        let res = await delObjs(ids);
        if (res.code != 0) {
            throw res;
        }
        getDataList(false);
        useMessage().success('删除成功');
    } catch (err: any) {
        useMessage().error(err.msg);
    }
};
//解绑
const handleUnbind = async (id: any) => {
    try {
        await useMessageBox().confirm('是否解绑该设备');
    } catch {
        return;
    }
    try {
        await unbind([id]);
        getDataList(false);
        useMessage().success('解绑成功');
    } catch (err: any) {
        useMessage().error(err.msg);
    }
};

const showLogDialog = ref(false);
const logDays = ref(1);
const currentDeviceNo = ref('');
const logFormRef = ref();
const logDaysRules = [
    { required: true, message: '请输入天数', trigger: 'blur' },
    { type: 'number', min: 1, max: 7, message: '天数范围为1-7', trigger: 'blur' },
    {
        validator: (rule: any, value: any, callback: any) => {
            if (!Number.isInteger(value) || value < 1) {
                callback(new Error('请输入正整数'));
            } else {
                callback();
            }
        },
        trigger: 'blur',
    },
];

const handleUploadLog = (row: any) => {
    currentDeviceNo.value = row.deviceNo;
    logDays.value = 1;
    showLogDialog.value = true;
};
const confirmUploadLog = async () => {
    // @ts-ignore
    await logFormRef.value.validate();
    try {
        const res = await uploadLog({ deviceNo: currentDeviceNo.value, logDays: logDays.value });
        if (res.code === 0) {
            useMessage().success(res.data.sendMessage);
            showLogDialog.value = false;
        } else {
            useMessage().error('消息发送失败');
        }
    } catch (error) {
        useMessage().error('消息发送失败');
    }
};
</script>
