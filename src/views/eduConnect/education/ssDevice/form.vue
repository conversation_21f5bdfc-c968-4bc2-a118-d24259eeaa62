<template>
    <el-dialog :title="form.id ? '编辑设备' : '新增设备'" v-model="visible" :close-on-click-modal="false" :width="800">
        <el-form
            ref="dataFormRef"
            :model="form"
            :rules="dataRules"
            formDialogRef
            label-width="100px"
            v-loading="loading"
        >
            <el-row :gutter="24">
                <el-col :span="12" class="mb20">
                    <el-form-item label="设备名称" prop="deviceName">
                        <el-input v-model="form.deviceName" placeholder="请输入设备名称" />
                    </el-form-item>
                </el-col>

                <el-col :span="12" class="mb20">
                    <el-form-item label="设备编号" prop="deviceNo">
                        <el-input v-model="form.deviceNo" placeholder="请输入设备号" :disabled="!!form.id" />
                    </el-form-item>
                </el-col>

                <el-col :span="24" class="mb20">
                    <el-form-item label="设备类型" prop="deviceType">
                        <el-radio-group v-model="form.deviceType" @change="changeDeviceType">
                            <el-radio :label="item.value" v-for="(item, index) in device_type" border :key="index"
                                >{{ item.label }}
                            </el-radio>
                        </el-radio-group>
                    </el-form-item>
                </el-col>
                <el-col :span="12" class="mb20" v-if="form.deviceType == '2'">
                    <el-form-item label="所属门店" prop="campusId">
                        <fuzzy-search
                            ref="campusIdSearchRef"
                            placeholder="输入搜索授权门店"
                            v-model="form.campusId"
                            :echoContent="form.campusName"
                            :requestUrl="getCampusList"
                            request-name="query"
                            data-label="campusName"
                            clearable
                        ></fuzzy-search>
                    </el-form-item>
                </el-col>
                <el-col :span="12" class="mb20" v-if="form.deviceType == '2'">
                    <el-form-item label="所属教室" prop="classRoomId">
                        <el-select v-model="form.classRoomId" placeholder="请选择教室">
                            <el-option
                                v-for="item in roomList"
                                :key="item.id"
                                :label="item.classRoomName"
                                :value="item.id"
                            />
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="12" class="mb20" v-if="form.deviceType == '1'">
                    <el-form-item label="所属直播间" prop="classRoomId">
                        <el-select v-model="form.classRoomId" placeholder="请选择" class="w-96">
                            <el-option
                                v-for="item in live_room"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            />
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="24" class="mb20">
                    <el-form-item label="有效期" prop="indateForever">
                        <el-radio-group v-model="form.indateForever">
                            <el-radio label="1" border>永久</el-radio>
                            <el-radio label="0" border>自定义时间</el-radio>
                        </el-radio-group>
                        <el-date-picker
                            v-if="form.indateForever == '0'"
                            v-model="date"
                            type="daterange"
                            range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            class="ml-6 max-w-80"
                        />
                    </el-form-item>
                </el-col>
                <el-col :span="24" class="mb20">
                    <el-form-item label="设备状态" prop="deviceState">
                        <el-radio-group v-model="form.deviceState">
                            <el-radio :label="item.value" v-for="(item, index) in device_state" border :key="index"
                                >{{ item.label }}
                            </el-radio>
                        </el-radio-group>
                    </el-form-item>
                </el-col>

                <el-col :span="24" class="mb20">
                    <el-form-item label="是否激活" prop="deviceActive">
                        <el-radio-group v-model="form.deviceActive">
                            <el-radio :label="item.value" v-for="(item, index) in device_active" border :key="index"
                                >{{ item.label }}
                            </el-radio>
                        </el-radio-group>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="visible = false">取消</el-button>
                <el-button type="primary" @click="onSubmit" :disabled="loading">确认</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script setup lang="ts" name="SsDeviceDialog">
import { useDict } from '/@/hooks/dict';
import { useMessage } from '/@/hooks/message';
import { getObj, addObj, putObj } from '../../../../api/eduConnect/ssDevice';
import fuzzySearch from '/@/components/EduComponents/fuzzySearch.vue';
import { FormRules } from 'element-plus';
import { getCampusList, classRoomByCampusId } from '/@/api/eduConnect/ssQuery';
import moment from 'moment';
import { isArray } from 'lodash';
const emit = defineEmits(['refresh']);

// 定义变量内容
const dataFormRef = ref();
const campusIdSearchRef = ref();
const visible = ref(false);
const loading = ref(false);
const date = ref();
const roomList = ref([] as any);
// 定义字典
const { device_state, device_active, device_type, live_room } = useDict(
    'device_state',
    'device_active',
    'device_type',
    'live_room'
);

// 提交表单数据
const form = reactive<any>({
    id: '',
    deviceName: '',
    deviceNo: '',
    deviceType: '',
    deviceState: '0',
    deviceActive: '1',
    indateForever: '1',
    campusId: '',
    classRoomId: '',
    campusName: '',
});
watch(
    () => form.campusId,
    (val) => {
        console.log(val, 99999);

        if (form.deviceType === '2') {
            getRoomList(val);
        }
    }
);
const getRoomList = async (id: string) => {
    await classRoomByCampusId(id).then((res) => {
        roomList.value = res.data;
        if (res.data.findIndex((item: any) => item.id == form.classRoomId) == -1) {
            form.classRoomId = '';
        }
    });
};
// 定义校验规则
const dataRules = reactive<FormRules>({
    deviceName: [{ required: true, message: '请输入设备名称', trigger: 'blur' }],
    deviceNo: [{ required: true, message: '请输入设备编号', trigger: 'blur' }],
    deviceType: [{ required: true, message: '请选择设备类型', trigger: 'change' }],
    deviceState: [{ required: true, message: '请选择设备状态', trigger: 'change' }],
    deviceActive: [{ required: true, message: '请选择是否激活', trigger: 'change' }],
    indateForever: [{ required: true, message: '请选择有效期', trigger: 'change' }],

    campusId: [{ required: true, message: '请选择所属门店', trigger: 'change' }],
    classRoomId: [{ required: true, message: '请选择所属教室', trigger: 'change' }],
    lecturerId: [{ required: true, message: '请选择所属门店', trigger: 'change' }],
});
// 打开弹窗
const openDialog = (id: string) => {
    visible.value = true;
    form.id = '';
    date.value = undefined;
    // 重置表单数据
    nextTick(() => {
        dataFormRef.value?.resetFields();
        form.campusId = '';
        form.classRoomId = '';
        form.campusName = '';
        roomList.value = [];
        // 获取ssDevice信息
    });

    if (id) {
        form.id = id;
        getssDeviceData(id);
        // campusFuzzySearchRef.value.remoteMethod(row.className);
    }
};
const changeDeviceType = (e: any) => {
    // if (e === '2') {
    //   form.campusId = ""
    //   campusIdSearchRef.value.clearOptions()
    // } else {
    form.classRoomId = '';

    // }
};

// 提交
const onSubmit = async () => {
    const valid = await dataFormRef.value.validate().catch(() => {});
    if (!valid) return false;
    if (isArray(unref(date)) && (!unref(date).length || !unref(date)[0] || !unref(date)[1])) {
        useMessage().warning('请选择有效时间');
        return false;
    }

    let params = { ...form } as any;
    console.log(params.deviceType, 88888);

    // if (params.deviceType != '1') {
    //   params.campusId = ""
    // }
    if (form.indateForever === '0') {
        try {
            params.indateStart = moment(unref(date)[0]).format('YYYY-MM-DD HH:mm:ss');
            params.indateEnd = moment(unref(date)[1]).format('YYYY-MM-DD HH:mm:ss');
        } catch (error) {
            useMessage().error('日期格式化失败，请检查输入的有效性');
            return false;
        }
    }

    try {
        loading.value = true;
        form.id ? await putObj(params) : await addObj(params);
        useMessage().success(params.id ? '修改成功' : '添加成功');
        visible.value = false;
        emit('refresh');
    } catch (err: any) {
        useMessage().error(err.msg);
    } finally {
        loading.value = false;
    }
};

// 初始化表单数据
const getssDeviceData = (id: string) => {
    // 获取数据
    loading.value = true;
    getObj(id)
        .then((res: any) => {
            let data = res.data;
            data.classRoomId = data.classRoomId !== '-1' ? data.classRoomId : undefined;
            Object.assign(form, data);
            if (form.indateForever === '0') {
                date.value = [data.indateStart, data.indateEnd];
            }
            form.campusName = data.campusName;

            // nextTick(async () => {
            //   if (res.data.campusName) {
            //     campusFuzzySearchRef.value.remoteMethod(res.data.campusName);
            //     // await getRoomList(res.data.campusId)
            //   }
            // })
        })
        .finally(() => {
            loading.value = false;
        });
};
// 暴露变量
defineExpose({
    openDialog,
});
</script>
