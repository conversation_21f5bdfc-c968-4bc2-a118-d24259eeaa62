<template>
    <div class="container">
        <el-dialog title="课程安排" v-model="visible" :close-on-click-modal="false" @close="close" width="1200px">
            <el-form ref="dataFormRef" :inline="true" :model="state.queryForm" label-width="80px" v-loading="loading">
                <el-form-item label="关联书籍" prop="booksName">
                    <el-input
                        placeholder="请输入书籍名称"
                        :maxLength="20"
                        v-model="state.queryForm.booksName"
                    ></el-input>
                </el-form-item>

                <el-form-item label="课程类型" prop="attendClassType">
                    <el-select
                        style="width: 120px"
                        v-model="state.queryForm.attendClassType"
                        placeholder="请选择"
                        clearable
                    >
                        <el-option
                            v-for="item in course_type"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        />
                    </el-select>
                </el-form-item>

                <el-form-item label="上课时间" prop="timePicker">
                    <el-date-picker
                        style="width: 360px"
                        v-model="timePicker"
                        type="datetimerange"
                        :shortcuts="shortcuts"
                        range-separator="~"
                        start-placeholder="开始时间"
                        end-placeholder="结束时间"
                        value-format="YYYY-MM-DD HH:mm:ss"
                        @change="addQuery"
                    />
                </el-form-item>
                <el-form-item>
                    <el-button @click="getDataList()" icon="search" type="primary">
                        {{ $t('common.queryBtn') }}
                    </el-button>
                </el-form-item>
            </el-form>
            <el-table :data="state.dataList" border>
                <el-table-column prop="className" label="班级名称" width="180" />
                <el-table-column prop="booksName" label="关联书籍" width="180" show-overflow-tooltip />
                <!-- 0 直播课 1 点播课 -->
                <el-table-column prop="attendClassType" label="课程类型" width="100">
                    <template #default="{ row }">
                        <dict-tag
                            :options="course_type"
                            :type="setTagType(row.attendClassType, { '1': 'primary', '2': 'info' })"
                            :value="row.attendClassType"
                        ></dict-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="attendClassDateStartTime" label="上课时间" width="180" />
                <el-table-column prop="liveDeviceName" label="上课教室" width="180">
                    <template #default="{ row }">
                        <dict-tag :options="live_room" :value="row.classRoomId" v-if="row.classRoomId"></dict-tag>
                        <span v-else>/</span>
                    </template>
                </el-table-column>
                <!-- ss_lecturer -->
                <el-table-column prop="lecturerName" label="主讲老师" width="180" />
                <!--  -->
                <el-table-column prop="attendClassState" label="状态" width="100">
                    <template #default="{ row }">
                        <dict-tag
                            :options="attend_class_state"
                            :type="
                                setTagType(row.attendClassState, {
                                    '0': 'primary',
                                    '1': 'success',
                                    '2': 'warn',
                                    '3': 'success',
                                    '4': 'info',
                                })
                            "
                            :value="row.attendClassState"
                        ></dict-tag>
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="90" fixed="right">
                    <template #default="{ row }">
                        <el-button text type="primary" :underline="true" @click="open(row)">
                            {{
                                row.attendClassState == ClassStatus.underway || row.attendClassState == ClassStatus.end
                                    ? '查看'
                                    : '编辑'
                            }}
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
            <pagination
                @size-change="sizeChangeHandle"
                @current-change="currentChangeHandle"
                v-bind="state.pagination"
                :pageSizes="[10, 20, 50]"
                :layout="layout"
            />
            <class-time ref="classTimeRef" :disabled="disable" @refresh="refresh"></class-time>
        </el-dialog>
    </div>
</template>

<script setup lang="ts" name="SsClassDialog">
import { useDict } from '/@/hooks/dict';
import { BasicTableProps, useTable } from '/@/hooks/table';
import { getPageClassTimeByClassId } from '../../../../api/eduConnect/ssClass';
import { ClassTimeEditEnums, ClassStatus } from '/@/components/EduComponents/enums/eduEnums';
const ClassTime = defineAsyncComponent(() => import('/@/components/EduComponents/classTime/index.vue'));
// 定义查询字典
const { course_type, attend_class_state, live_room } = useDict('course_type', 'attend_class_state', 'live_room');

// 定义变量内容
const visible = ref(false);
const loading = ref(false);
const disable = ref(false);
const layout = ref('->, total,prev, pager, next,sizes');
const classTimeRef = ref();
// 时间
const timePicker = ref([]);

const state: BasicTableProps = reactive<BasicTableProps>({
    createdIsNeed: false,
    queryForm: {},
    pagination: {
        current: 1,
        size: 10,
    },
    pageList: getPageClassTimeByClassId,
});
//  table hook
const { getDataList, currentChangeHandle, sizeChangeHandle } = useTable(state);

const open = (row: Record<string, any>) => {
    disable.value = row.attendClassState === ClassStatus.underway || row.attendClassState === ClassStatus.end;
    const { attendClassType, id } = row;
    const attr = {
        type: attendClassType,
        editType: ClassTimeEditEnums.EDIT,
        id,
    };
    // 打开对话框
    classTimeRef.value.openDialog(attr);
};

const setTagType = (key: any, obj: any) => {
    return obj[key];
};

const addQuery = () => {
    if (timePicker.value != null) {
        state.queryForm.selectAttendClassStartTime = timePicker.value[0];
        state.queryForm.selectAttendClassEndTime = timePicker.value[1];
    } else {
        state.queryForm.selectAttendClassStartTime = '';
        state.queryForm.selectAttendClassEndTime = '';
    }
};
// 设置0点 - 24h
const resetTime = (start: Date, end: Date) => {
    start.setHours(0);
    start.setMinutes(0);
    start.setSeconds(0);
    start.setMilliseconds(0);

    end.setHours(23);
    end.setMinutes(59);
    end.setSeconds(59);
    end.setMilliseconds(59);
};
// 时间节点
const shortcuts = [
    {
        text: '今天',
        value: () => {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 0);
            resetTime(start, end);
            return [start, end];
        },
    },
    {
        text: '本周',
        value: () => {
            const end = new Date();
            const start = new Date();
            const nows = start.getDay() || 7; //注意周日算第一天，如果周日查询本周的话，天数是0，所有如     果是0，默认设置为7
            start.setTime(start.getTime() - 3600 * 1000 * 24 * (nows - 1));
            resetTime(start, end);
            return [start, end];
        },
    },
    {
        text: '上周',
        value: () => {
            const dataValue = new Date();
            const year = dataValue.getFullYear();
            const month = dataValue.getMonth() + 1;
            const day = dataValue.getDate();
            var thisWeekStart; //本周周一的时间
            if (dataValue.getDay() == 0) {
                //周天的情况；
                thisWeekStart =
                    new Date(year + '/' + month + '/' + day).getTime() - (dataValue.getDay() + 6) * 86400000;
            } else {
                thisWeekStart =
                    new Date(year + '/' + month + '/' + day).getTime() - (dataValue.getDay() - 1) * 86400000;
            }
            const prevWeekStart = thisWeekStart - 7 * 86400000; //上周周一的时间
            const prevWeekEnd = thisWeekStart - 1 * 86400000; //上周周日的时间
            const start = new Date(prevWeekStart); //开始时间
            const end = new Date(prevWeekEnd); //结束时间
            resetTime(start, end);
            return [start, end];
        },
    },
    {
        text: '本月',
        value: () => {
            const end = new Date();
            const start = new Date();
            start.setDate(1);
            resetTime(start, end);
            return [start, end];
        },
    },
    {
        text: '上月',
        value: () => {
            const start = new Date();
            const end = new Date(start);
            end.setMonth(start.getMonth());
            start.setMonth(start.getMonth() - 1);
            end.setDate(0);
            start.setDate(1);
            resetTime(start, end);
            return [start, end];
        },
    },
];
// dialog
const openDialog = async (row: any, diskey: boolean, type: number) => {
    state.queryForm.classId = row.id;
    visible.value = diskey;
    if (type == 1) {
        state.queryForm.attendClassState = ClassStatus.end;
    }
    getDataList();
};
// 关闭表单
const close = () => {
    state.queryForm = {};
    timePicker.value = [];
    state.dataList = [];
    const activeElement = document.activeElement as HTMLElement;
    if (activeElement) {
        activeElement.blur();
    }
};

const refresh = () => {
    getDataList();
};
// 暴露变量
defineExpose({
    openDialog,
});
</script>

<style scoped lang="scss"></style>
