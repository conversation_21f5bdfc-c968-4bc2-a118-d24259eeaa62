<template>
    <el-dialog :title="title" v-model="visible" :close-on-click-modal="false" @close="handleCloseDialog" width="1200px">
        <el-form ref="formRef" :model="form" :rules="dataRules" label-width="120px" v-if="!loading">
            <el-row :gutter="24">
                <el-col :span="12" class="mb20">
                    <el-form-item label="班级名称" prop="className">
                        <el-input :disabled="mode === 'auth'" v-model="form.className" placeholder="请输入班级名称" />
                    </el-form-item>
                </el-col>

                <el-col :span="12" class="mb20">
                    <el-form-item label="阶段" prop="grade">
                        <el-select :disabled="mode === 'auth'" v-model="form.grade" placeholder="请选择阶段">
                            <el-option
                                v-for="item in stageOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                </el-col>

                <el-col :span="24" class="mb20">
                    <el-form-item label="班级类型" prop="classType" required style="display: flex; align-items: center">
                        <el-radio-group v-model="form.classType">
                            <el-radio value="0" size="large" :label="0">读书会</el-radio>
                            <el-radio value="1" size="large" :label="1">点播课</el-radio>
                        </el-radio-group>
                    </el-form-item>
                </el-col>

                <el-col :span="24" class="mb20">
                    <el-form-item label="授权门店" prop="ssClassAuthInfoDTOList">
                        <YdTransfer
                            v-model="selectedDevices"
                            :dataSource="transferOptions"
                            rowKey="key"
                            :titles="['未选中门店', '选中门店']"
                            filterable
                            :filterPlaceholder="['请输入要搜索的门店', '请输入要搜索的门店']"
                            @change="handleDeviceChange"
                        >
                            <template #default="{ item }">
                                <el-tooltip :content="item.label" placement="top" :show-after="500">
                                    <span>{{ item.label }}</span>
                                </el-tooltip>
                            </template>
                        </YdTransfer>
                    </el-form-item>
                </el-col>

                <el-col :span="12" class="mb20" v-show="!form.id">
                    <el-form-item label="是否同步校管家" prop="isSyncXiaogj" class="syncSchool" required>
                        <el-radio-group v-model="form.isSyncXiaogj">
                            <el-radio :value="is_sync_xiaogj[1].value" size="large" :label="1">是</el-radio>
                            <el-radio :value="is_sync_xiaogj[0].value" size="large" :label="0">否(通课时选择)</el-radio>
                        </el-radio-group>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <el-skeleton :rows="10" animated v-else />

        <template #footer>
            <span class="dialog-footer">
                <el-button @click="visible = false">取消</el-button>
                <el-button type="primary" @click="submitForm" :disabled="loading" :loading="submitLoading">
                    确认
                </el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script setup lang="ts" name="SsClassDialog">
import { useDict } from '/@/hooks/dict';
import { useStageList } from '/@/hooks/stage';
import { useMessage, useMessageBox } from '/@/hooks/message';
import {
    getObj,
    addObj,
    putObj,
    getClassRoomDeviceList,
    getAuthDeviceListByClassId,
    putClassRoomDeviceList,
} from '/@/api/eduConnect/ssClass';
import type { FormItemRule } from 'element-plus';
import YdTransfer from '/@/components/YdTransfer/index.vue'; // 路径根据您的项目结构调整

// 定义接口类型
interface ClassRoomDevice {
    id: number | string;
    deviceName: string;
    campusName: string;
    classRoomName: string;
    campusId: number | string;
    classRoomId: number | string;
    xgjCampusId: number | string;
    xgjClassRoomId: number | string;
}

interface AuthInfo {
    deviceId: number | string;
    campusId: number | string;
    classRoomId: number | string;
    xgjCampusId: number | string;
    xgjClassRoomId: number | string;
}

interface Option {
    key: number;
    label: string;
    depSort: string;
}

interface ClassForm {
    id?: number | string;
    className: string;
    grade: string;
    isSyncXiaogj: number;
    classType: number;
    ssClassAuthInfoDTOList: AuthInfo[];
}

// 定义查询字典
const { is_sync_xiaogj } = useDict('is_sync_xiaogj');

// 使用阶段封装
const { stageOptions } = useStageList();
const emit = defineEmits(['refresh']);
// 定义变量内容
const visible = ref(false);
const loading = ref(false);
const submitLoading = ref(false);
const classRoomDevice = ref<ClassRoomDevice[]>([]);
const mode = ref('add');

// 过滤的门店值
const campusDisplayNames = ref<string[]>([]);
// 排序用数组
const deviceNamesSortKey = ref<string[]>([]);
// 过渡框data
const transferOptions = ref<Option[]>([]);
const formRef = ref();

// 使用单一数据源 - 选中的设备对象数组
const selectedDevices = ref<Option[]>([]);

// 防止循环更新的标志
const updatingForm = ref(false);

const form = reactive<ClassForm>({
    className: '',
    grade: '',
    isSyncXiaogj: 1,
    classType: 0,
    ssClassAuthInfoDTOList: [],
});

// 定义校验规则
const dataRules = ref({
    className: [{ required: true, message: '班级名称不能为空', trigger: ['blur'] }],
    grade: [{ required: true, message: '阶段不能为空', trigger: 'change' }],
    ssClassAuthInfoDTOList: [
        {
            validator: (_rule: FormItemRule, value: any, callback: (error?: Error) => void) => {
                if (!selectedDevices.value?.length || !value || value.length === 0) {
                    callback(new Error('至少授权一个门店'));
                } else {
                    callback();
                }
            },
            trigger: ['change'],
        },
    ],
});

const title = computed(() => {
    switch (mode.value) {
        case 'add':
            return '新增班级';
        case 'auth':
            return '授权门店';
        case 'edit':
            return '编辑班级';
        default:
            return '';
    }
});

// 数组排序函数
function compareValues(key: keyof Option, order = 'asc') {
    return function innerSort(a: Option, b: Option) {
        if (!a.hasOwnProperty(key) || !b.hasOwnProperty(key)) {
            return 0;
        }

        const varA = typeof a[key] === 'string' ? a[key].toUpperCase() : a[key];
        const varB = typeof b[key] === 'string' ? b[key].toUpperCase() : b[key];

        let comparison = 0;
        if (varA > varB) {
            comparison = 1;
        } else if (varA < varB) {
            comparison = -1;
        }
        return order === 'desc' ? comparison * -1 : comparison;
    };
}

const resetFormData = () => {
    form.id = undefined;
    form.className = '';
    form.grade = '';
    form.isSyncXiaogj = 1;
    form.classType = 0;
    form.ssClassAuthInfoDTOList = [];
    // 重置表单验证
    nextTick(() => {
        formRef.value?.clearValidate();
    });
};

const generateTransferOptions = () => {
    transferOptions.value = []; // 清空之前的数据，避免重复添加
    campusDisplayNames.value.forEach((item: string, index: number) => {
        transferOptions.value.push({
            label: item,
            key: index,
            depSort: deviceNamesSortKey.value[index] || '',
        });
    });
    transferOptions.value.sort(compareValues('depSort'));
};

// 关闭dialog
const handleCloseDialog = () => {
    campusDisplayNames.value = [];
    transferOptions.value = [];
    selectedDevices.value = [];
    deviceNamesSortKey.value = [];
    const activeElement = document.activeElement as HTMLElement;
    if (activeElement) {
        activeElement.blur();
    }
    resetFormData();
};

// 从selectedDevices更新表单数据
const updateFormFromSelectedDevices = () => {
    if (updatingForm.value) return;

    updatingForm.value = true;

    try {
        // 获取所有选中设备的索引
        const selectedIndices = selectedDevices.value.map((item) => item.key);

        // 根据索引获取设备详情并更新表单
        form.ssClassAuthInfoDTOList = selectedIndices.map((index) => ({
            deviceId: classRoomDevice.value[index].id,
            campusId: classRoomDevice.value[index].campusId,
            classRoomId: classRoomDevice.value[index].classRoomId,
            xgjCampusId: classRoomDevice.value[index].xgjCampusId,
            xgjClassRoomId: classRoomDevice.value[index].xgjClassRoomId,
        }));
    } finally {
        updatingForm.value = false;
    }
};

// YdTransfer的change事件处理
const handleDeviceChange = (value: Option[]) => {
    selectedDevices.value = value;
    updateFormFromSelectedDevices();
};

// 打开弹窗
const openDialog = async (operationMode: string, row?: any) => {
    // 重置状态
    resetFormData();
    campusDisplayNames.value = [];
    transferOptions.value = [];
    deviceNamesSortKey.value = [];
    selectedDevices.value = [];

    mode.value = operationMode;
    visible.value = true;

    // 获取门店信息
    loading.value = true;
    try {
        const res = await getClassRoomDeviceList();
        classRoomDevice.value = res.data || [];

        classRoomDevice.value.forEach((item) => {
            if (item.deviceName != null) {
                campusDisplayNames.value.push(
                    item.campusName + '(' + item.deviceName + '--' + item.classRoomName + ')'
                );
                deviceNamesSortKey.value.push(item.deviceName);
            }
        });

        generateTransferOptions();

        // 获取ssClass信息
        if (row && row.id) {
            form.id = row.id;
            await getDetailData(row.id);

            // 通过id获取已授权的设备信息
            const selectedDeviceRes = await getAuthDeviceListByClassId(row.id);
            const selectedDevice = selectedDeviceRes.data || [];

            // 找出已授权设备对应的索引
            const selectedIndices: number[] = [];
            selectedDevice.forEach((item: any) => {
                const index = classRoomDevice.value.findIndex((value) => value.id == item.deviceId);
                if (index !== -1) {
                    selectedIndices.push(index);
                }
            });

            // 一次性设置所有选中的设备
            selectedDevices.value = selectedIndices
                .map((index) => transferOptions.value.find((option) => option.key === index))
                .filter(Boolean) as Option[];

            // 更新表单中的授权信息
            updateFormFromSelectedDevices();
        }
    } catch (error) {
        useMessage().error('加载数据失败');
    } finally {
        loading.value = false;
    }
};

// 提交
const submitForm = async () => {
    // 检查同步校管家选项
    if (form.isSyncXiaogj === 0 && !form.id) {
        try {
            await useMessageBox().confirm(
                '您未选择同步校管家，该班级将不会在校管家同步创建的课程，确认后无法更改，请再次确认!'
            );
        } catch {
            return;
        }
    }

    // 表单验证
    try {
        await formRef.value.validate();
    } catch {
        return false;
    }

    // 提交数据
    try {
        submitLoading.value = true;
        const formData = {
            ...form,
        };
        const api = mode.value === 'add' ? addObj : mode.value === 'auth' ? putClassRoomDeviceList : putObj;
        const res = await api(formData);
        if (res) {
            useMessage().success(res?.msg);
        }
        visible.value = false;
        emit('refresh');
    } catch (err: any) {
        useMessage().error(err.msg);
    } finally {
        submitLoading.value = false;
    }
};

// 初始化表单数据
const getDetailData = async (id: string) => {
    try {
        const res = await getObj(id);
        if (res && res.data) {
            const { className, grade, isSyncXiaogj, classType } = res.data;
            form.className = className || '';
            form.grade = grade ? String(grade) : ''; // 确保是字符串
            form.isSyncXiaogj = isSyncXiaogj;
            form.classType = classType;
        }
    } catch (error) {
        useMessage().error('获取详情数据失败');
    }
};

// 监听selectedDevices变化，更新表单数据
watch(
    selectedDevices,
    () => {
        updateFormFromSelectedDevices();
    },
    { deep: true }
);

// 暴露变量
defineExpose({
    openDialog,
});
</script>

<style scoped lang="scss">
.el-transfer {
    width: 100%;

    :deep(.el-transfer-panel) {
        width: 460px;
    }

    :deep(.el-transfer__buttons) {
        padding: 0 12px;
        text-align: center;
    }

    :deep(.el-transfer-panel__body) {
        height: 400px;
    }

    :deep(.el-transfer__button) {
        display: block;
        margin: 10px 0;
    }
}

:deep(.high-performance-transfer) {
    width: 100%;

    .transfer-panel {
        width: 460px;
    }

    .panel-body {
        height: 400px;
    }
}
</style>
