<template>
    <div class="layout-padding">
        <div class="layout-padding-auto layout-padding-view">
            <el-row class="ml10" v-show="showSearch">
                <el-form :inline="true" :model="state.queryForm" ref="queryRef" label-width="80px">
                    <el-form-item label="班级名称" prop="className">
                        <el-input
                            placeholder="请输入班级名称"
                            clearable
                            class="w-[240px]"
                            v-model="state.queryForm.className"
                        />
                    </el-form-item>
                    <el-form-item label="教室名称" prop="classRoomDeviceId">
                        <fuzzy-search
                            class="w-[240px]"
                            placeholder="输入搜索教室名称"
                            v-model="state.queryForm.classRoomDeviceId"
                            :requestUrl="getClassRoomDeviceList"
                            request-name="searchContent"
                            data-label="classRoomName"
                            clearable
                        ></fuzzy-search>
                    </el-form-item>
                    <el-form-item label="阶段" prop="grade">
                        <el-select v-model="state.queryForm.grade" placeholder="请选择阶段" class="w-[240px]" clearable>
                            <el-option
                                v-for="item in stageOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            />
                        </el-select>
                    </el-form-item>

                    <el-form-item label="班级状态" prop="classState">
                        <el-select
                            v-model="state.queryForm.classState"
                            placeholder="请选择班级状态"
                            class="w-[240px]"
                            clearable
                        >
                            <el-option
                                v-for="item in class_state"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="班级类型" prop="classType">
                        <el-select v-model="state.queryForm.classType" placeholder="请选择班级类型" class="w-[240px]">
                            <el-option
                                v-for="item in class_type"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            />
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-button @click="getDataList" icon="search" type="primary" v-auth="'edusystem_ssClass_view'">
                            {{ $t('common.queryBtn') }}
                        </el-button>
                        <el-button @click="resetQuery" icon="Refresh" v-auth="'edusystem_ssClass_view'">
                            {{ $t('common.resetBtn') }}
                        </el-button>
                    </el-form-item>
                </el-form>
            </el-row>
            <el-row type="flex" class="mb-[8px]" justify="space-between">
                <div>
                    <el-button
                        icon="folder-add"
                        type="primary"
                        class="ml10"
                        @click="formDialogRef.openDialog('add')"
                        v-auth="'edusystem_ssClass_add'"
                    >
                        新增
                    </el-button>
                    <el-button
                        plain
                        :disabled="multiple"
                        icon="Delete"
                        type="primary"
                        v-auth="'edusystem_ssClass_del'"
                        @click="handleDelete(selectObjs)"
                    >
                        删除
                    </el-button>
                </div>
                <right-toolbar v-model:showSearch="showSearch" @queryTable="getDataList"></right-toolbar>
            </el-row>
            <el-table
                :data="state.dataList"
                v-loading="state.loading"
                border
                :cell-style="tableStyle.cellStyle"
                :header-cell-style="tableStyle.headerCellStyle"
                @selection-change="selectionChangHandle"
                @sort-change="sortChangeHandle"
            >
                <el-table-column type="selection" width="40" align="center" :selectable="selectable" />
                <el-table-column type="index" label="序号" width="60" />
                <el-table-column prop="className" label="班级名称" show-overflow-tooltip min-width="200" />
                <el-table-column prop="grade" label="阶段">
                    <template #default="{ row }">
                        <el-tag type="primary">{{
                            stageOptions.find((item: any) => parseInt(item.value) == row.grade)?.label
                        }}</el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="classAuthCount" label="授权门店" width="100px">
                    <template #default="{ row }">
                        <el-button
                            class="w-[50px]"
                            size="small"
                            @click="formDialogRef.openDialog('auth', row)"
                            type="primary"
                            plain
                            v-auth="'edusystem_ssClass_auth_device'"
                        >
                            <span>
                                {{ row.classAuthCount }}
                            </span>
                        </el-button>
                    </template>
                </el-table-column>
                <el-table-column prop="scheduledClassCount" label="已排课次" width="100px">
                    <template #default="{ row }">
                        <el-button
                            class="w-[50px]"
                            size="small"
                            @click="lessonDialogRef.openDialog(row, true, 0)"
                            type="primary"
                            plain
                            v-auth="'edusystem_ssClass_auth_device'"
                        >
                            {{ row.scheduledClassCount }}
                        </el-button>
                    </template>
                </el-table-column>
                <el-table-column prop="lessonsAttendedCount" label="已上课次" width="100px">
                    <template #default="{ row }">
                        <el-button
                            class="w-[50px]"
                            size="small"
                            @click="lessonDialogRef.openDialog(row, true, 1)"
                            type="primary"
                            plain
                        >
                            {{ row.lessonsAttendedCount }}
                        </el-button>
                    </template>
                </el-table-column>
                <el-table-column prop="classState" label="班级状态" width="100px">
                    <template #default="{ row }">
                        <dict-tag
                            :options="class_state"
                            mode="text"
                            :type="setTagType(row.classState, { '0': 'primary', '1': 'info' })"
                            :value="row.classState"
                        ></dict-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="isSyncXiaogj" label="同步校管家" min-width="100">
                    <template #default="{ row }">
                        <dict-tag
                            mode="text"
                            :options="is_sync_xiaogj"
                            :type="setTagType(row.isSyncXiaogj, { '0': 'warning', '1': 'primary' })"
                            :value="row.isSyncXiaogj"
                        ></dict-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="classType" label="班级类型" width="100px">
                    <template #default="{ row }">
                        <dict-tag mode="text" type="info" :options="class_type" :value="row.classType"></dict-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="ctime" label="创建时间" width="160" />
                <el-table-column prop="creator" label="创建者" show-overflow-tooltip />
                <el-table-column label="操作" width="250" fixed="right">
                    <template #default="{ row }">
                        <el-row type="flex" justify="center" align="middle">
                            <el-button
                                icon="edit-pen"
                                text
                                type="primary"
                                v-auth="'edusystem_ssClass_edit'"
                                @click="formDialogRef.openDialog('edit', row)"
                            >
                                编辑
                            </el-button>
                            <el-dropdown
                                class="ml-[10px] mr-[10px]"
                                :multiple="false"
                                :hide-on-click="true"
                                trigger="click"
                                :visible-change="true"
                                :disabled="row.classState == FinishSchool.finish"
                            >
                                <span
                                    :class="{
                                        'el-dropdown-link': true,
                                    }"
                                    >排课<el-icon class="ml-[3px]"><arrow-down /></el-icon>
                                </span>
                                <template #dropdown>
                                    <el-dropdown-menu>
                                        <el-dropdown-item
                                            v-for="(item, index) in class_type"
                                            :key="index"
                                            @click="openClassSchedulingDialog(row, item.value)"
                                        >
                                            {{ item.label }}
                                        </el-dropdown-item>
                                    </el-dropdown-menu>
                                </template>
                            </el-dropdown>

                            <el-button
                                icon="stamp"
                                text
                                type="primary"
                                v-auth="'edusystem_ssClass_complete'"
                                @click="handleFinishClass(row.id)"
                                :disabled="row.classState == FinishSchool.finish"
                            >
                                结业
                            </el-button>

                            <el-button
                                icon="delete"
                                text
                                type="primary"
                                v-auth="'edusystem_ssClass_del'"
                                :disabled="row.lessonsAttendedCount != 0 || row.scheduledClassCount != 0"
                                @click="handleDelete([row.id])"
                            >
                                删除
                            </el-button>
                        </el-row>
                    </template>
                </el-table-column>
            </el-table>
            <pagination
                @size-change="sizeChangeHandle"
                @current-change="currentChangeHandle"
                v-bind="state.pagination"
            />
        </div>

        <!-- 编辑、新增  -->
        <form-dialog ref="formDialogRef" @refresh="getDataList(false)" />
        <!-- 课程安排 -->
        <lesson-dialog ref="lessonDialogRef" />
        <!-- 排课 -->
        <course-scheduling ref="courseDialogRef" @refresh="refresh"> </course-scheduling>
        <!-- <UploadVideo></UploadVideo> -->
    </div>
</template>

<script setup lang="ts" name="systemSsClass">
import { BasicTableProps, useTable } from '/@/hooks/table';
import { fetchList, delObjs, getClassRoomDeviceList, finishSchools } from '/@/api/eduConnect/ssClass';
import { useMessage, useMessageBox } from '/@/hooks/message';
import { useDict } from '/@/hooks/dict';
import { useStageList } from '/@/hooks/stage';
import { FinishSchool } from '/@/components/EduComponents/enums/eduEnums';
import fuzzySearch from '/@/components/EduComponents/fuzzySearch.vue';
// 引入组件
const FormDialog = defineAsyncComponent(() => import('./form.vue'));
const LessonDialog = defineAsyncComponent(() => import('./lessonDialog.vue'));
const CourseScheduling = defineAsyncComponent(() => import('/@/components/EduComponents/courseScheduling/index.vue'));
// const UploadVideo = defineAsyncComponent(() => import('/@/components/EduComponents/uploadVideo.vue'))
// 定义查询字典
const { class_type, class_state, is_sync_xiaogj } = useDict('class_type', 'class_state', 'is_sync_xiaogj');

// 使用阶段封装
const { stageOptions } = useStageList();

// 定义变量内容
const formDialogRef = ref();
const lessonDialogRef = ref();
const courseDialogRef = ref();
// 搜索变量
const queryRef = ref();
const showSearch = ref(true);
// 多选变量
const selectObjs = ref([]) as any;
const multiple = ref(true);

const state: BasicTableProps = reactive<BasicTableProps>({
    queryForm: {},
    pageList: fetchList,
});

// table hook
const { getDataList, currentChangeHandle, sizeChangeHandle, sortChangeHandle, tableStyle } = useTable(state);

const selectable = (row: any) => {
    return row.scheduledClassCount === 0;
};

const setTagType = (key: any, obj: any) => {
    return obj[key];
};

const openClassSchedulingDialog = (row: any, type: string) => {
    const classType = Number(type);
    courseDialogRef.value.openDialog(classType, row);
};

// 清空搜索条件
const resetQuery = () => {
    // 清空搜索条件
    queryRef.value?.resetFields();
    // 清空多选
    selectObjs.value = [];
    getDataList();
};

const refresh = () => {
    getDataList();
};

// 多选事件
const selectionChangHandle = (objs: { id: string }[]) => {
    selectObjs.value = objs.map(({ id }) => id);
    multiple.value = !objs.length;
};

// 结业
const handleFinishClass = async (id: any) => {
    try {
        await useMessageBox().confirm('设为结业后，此班级不可再排课，请谨慎操作，是否继续？');
    } catch {
        return;
    }
    try {
        let res = await finishSchools({
            id: id,
        });
        getDataList();
        if (res.code == 0) {
            useMessage().success('设置结业成功');
        } else {
            useMessage().error(res.msg);
        }
    } catch (err: any) {
        useMessage().error(err.msg);
    }
};

// 删除操作
const handleDelete = async (ids: string[]) => {
    try {
        await useMessageBox().confirm('此操作将永久删除');
    } catch {
        return;
    }
    try {
        await delObjs(ids);
        getDataList();
        useMessage().success('删除成功');
        state.queryForm = {};
    } catch (err: any) {
        useMessage().error(err.msg);
    }
};
</script>
<style scoped lang="scss">
.el-dropdown {
    :deep(.el-dropdown-link) {
        cursor: pointer;
        color: var(--el-color-primary);
        font-weight: var(--el-font-weight-primary);
        display: flex;
        align-items: center;
    }
}
.el-dropdown.is-disabled {
    :deep(.el-dropdown-link) {
        cursor: no-drop;
        color: var(--el-color-primary-light-5);
        font-weight: var(--el-font-weight-primary);
        display: flex;
        align-items: center;
    }
}
</style>
