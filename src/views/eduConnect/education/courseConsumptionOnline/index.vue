<template>
    <div class="layout-padding">
        <div class="layout-padding-auto layout-padding-view" v-if="isMask">
            <el-row class="ml10 top-3" v-show="showSearch">
                <el-form
                    :inline="true"
                    :model="state.queryForm"
                    ref="queryRef"
                    label-width="80px"
                    @keyup.enter="getDataList"
                >
                    <!-- 查询直播间字段 -->
                    <el-form-item label="门店名称">
                        <fuzzy-search
                            class="w-[240px]"
                            placeholder="请输入门店名称"
                            v-model="state.queryForm.storeId"
                            :requestUrl="getStoreSelectList"
                            request-name="campusName"
                            data-label="campusName"
                            clearable
                        ></fuzzy-search>
                    </el-form-item>
                    <el-form-item label="上课时间" prop="live_room_id">
                        <el-date-picker
                            class="w-[240px]"
                            v-model="queryDate"
                            type="daterange"
                            range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            @change="queryDateChange(queryDate)"
                        />
                    </el-form-item>
                    <el-form-item>
                        <el-button @click="getDataList" formDialogRef icon="search" type="primary">
                            {{ $t('common.queryBtn') }}
                        </el-button>
                        <el-button @click="resetQuery" formDialogRef icon="Refresh"
                            >{{ $t('common.resetBtn') }}
                        </el-button>
                    </el-form-item>
                </el-form>
            </el-row>
            <el-row>
                <div class="mb8" style="width: 100%">
                    <right-toolbar
                        v-model:showSearch="showSearch"
                        class="ml10 mr20"
                        style="float: right"
                        :export="true"
                        @exportExcel="exportExcel"
                        @queryTable="getDataList"
                    ></right-toolbar>
                </div>
            </el-row>
            <el-table
                :data="state.dataList"
                v-loading="state.loading"
                border
                :cell-style="tableStyle.cellStyle"
                :header-cell-style="tableStyle.headerCellStyle"
                @sort-change="sortChangeHandle"
            >
                <el-table-column type="index" label="序号" width="60" />
                <el-table-column prop="storeName" label="门店名称" show-overflow-tooltip min-width="200px" />
                <el-table-column prop="lessonName" label="课节名称" show-overflow-tooltip min-width="200px" />
                <el-table-column prop="courseType" label="类型" show-overflow-tooltip width="100px">
                    <template #default="{ row }">
                        <dict-tag :options="timetable_course_type" :value="row.courseType"></dict-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="className" label="班级名称" show-overflow-tooltip width="200px" />
                <el-table-column prop="teacherName" label="带班老师" show-overflow-tooltip width="150px" />
                <el-table-column prop="classDate" label="原上课时间" show-overflow-tooltip min-width="280px">
                    <template #default="{ row }">
                        {{ row.classDate }} {{ row.classStartTime }} - {{ row.classEndTime }}
                    </template>
                </el-table-column>
                <el-table-column prop="validityTime" label="补课视频有效期" show-overflow-tooltip min-width="320px">
                    <template #default="{ row }"> {{ row.validityStartTime }}至{{ row.validityEndTime }} </template>
                </el-table-column>
                <el-table-column prop="expectedNumber" label="补课学员" show-overflow-tooltip min-width="100px">
                    <template #default="{ row }">
                        <el-button
                            size="small"
                            type="primary"
                            style="width: 60px"
                            @click="showDialog(row.id, row.lessonName)"
                            plain
                        >
                            {{ row.expectedNumber }}</el-button
                        >
                    </template>
                </el-table-column>

                <el-table-column prop="actualNumber" label="出勤学员" show-overflow-tooltip min-width="100px">
                    <template #default="{ row }">
                        <el-button
                            size="small"
                            type="primary"
                            style="width: 60px"
                            plain
                            @click="showDialog(row.id, row.lessonName)"
                        >
                            {{ row.actualNumber }}</el-button
                        >
                    </template>
                </el-table-column>
                <el-table-column prop="trialNumber" label="试听课次消耗" show-overflow-tooltip min-width="150px" />
                <el-table-column prop="consumeNumber" label="正式课次消耗" show-overflow-tooltip min-width="150px" />
                <el-table-column prop="giftNumber" label="赠送课次消耗" show-overflow-tooltip min-width="150px" />
                <el-table-column prop="createTime" label="创建时间" show-overflow-tooltip min-width="150px" />
                <!-- <el-table-column label="操作" width="100px" fixed="right" v-auth="'edusystem_consumption_details_view'">
                    <template #default="{ row }">
                        <el-button text type="primary" @click="linkToDetails(row)">详情</el-button>
                    </template>
                </el-table-column> -->
            </el-table>
            <pagination
                @size-change="sizeChangeHandle"
                @current-change="currentChangeHandle"
                v-bind="state.pagination"
            />
            <el-dialog v-model="dialogVisible" title="课节补课详情" width="800">
                <el-descriptions :column="2" border size="large">
                    <el-descriptions-item label="课节名称">
                        {{ courseMakeUpOnlineData?.lessonName || '无课节名称' }}
                    </el-descriptions-item>
                    <el-descriptions-item label="原上课时间">
                        {{ courseMakeUpOnlineData?.classDate }}
                    </el-descriptions-item>
                    <el-descriptions-item label="补课学员名单" :span="2">
                        {{ courseMakeUpOnlineData?.expectedNames.join(',') }}
                    </el-descriptions-item>
                    <el-descriptions-item label="出勤学员名单" :span="2">
                        {{ courseMakeUpOnlineData?.actualNames.join(',') }}
                    </el-descriptions-item>
                </el-descriptions>
            </el-dialog>
        </div>
    </div>
</template>

<script setup lang="ts" name="courseConsumption">
import { BasicTableProps, useTable } from '/@/hooks/table';
import { courseMakeUpOnlineApi, onLineCourseConsumptionListApi } from '/@/api/eduConnect/timetable';
import fuzzySearch from '/@/components/EduComponents/fuzzySearch.vue';
import { getSchoolStoreList } from '/@/api/eduConnect/campusStore';
import { useDict } from '/@/hooks/dict';
import { formatDate } from '/@/utils/formatTime';
import moment from 'moment';
import { ElMessageBox } from 'element-plus';

const isMask = ref(true);
// const router = useRouter();
// const route = useRoute();
// 定义查询字典
let { timetable_course_type } = useDict('timetable_course_type');

// 定义变量内容
const queryRef = ref();
const showSearch = ref(true);
const queryDate = ref();
const dialogVisible = ref(false);
const courseMakeUpOnlineData = ref();

const state: BasicTableProps = reactive<BasicTableProps>({
    queryForm: {},
    isShowWarning: true,
    pageList: onLineCourseConsumptionListApi,
});

//  table hook
const { getDataList, currentChangeHandle, sizeChangeHandle, sortChangeHandle, tableStyle, downBlobFile } =
    useTable(state);

// 设置默认31天查询范围
onMounted(() => {
    // 设置默认31天查询范围（包含今天）
    const today = moment();
    const startDate = moment().subtract(30, 'days');
    queryDate.value = [startDate.toDate(), today.toDate()];
    queryDateChange(queryDate.value);
    getDataList();
});

const showDialog = async (id: string, lessonName?: string) => {
    dialogVisible.value = true;
    const res = await courseMakeUpOnlineApi(id);
    courseMakeUpOnlineData.value = {
        ...res.data,
        lessonName,
    };
};
// 清空搜索条件
const resetQuery = () => {
    queryRef.value?.resetFields();
    queryDate.value = undefined;
    state.queryForm = {};
    getDataList();
};

//处理日期格式
const queryDateChange = (date: Date[]) => {
    state.queryForm.classStartDateTime = date ? formatDate(date[0] as Date, 'YYYY-mm-dd') + ' 00:00:00' : '';
    state.queryForm.classEndDateTime = date ? formatDate(date[1] as Date, 'YYYY-mm-dd') + ' 23:59:59' : '';
};

const getStoreSelectList = async (params: {}) => {
    const res = await getSchoolStoreList(params);
    return { data: res.data.records };
};

// 生成导出文件名
const generateFileName = async () => {
    let fileName = '课程补课记录';
    const parts = [];

    // 添加门店名称
    if (state.queryForm.storeId) {
        try {
            const res = await getSchoolStoreList({ id: state.queryForm.storeId });
            if (res.data.records && res.data.records.length > 0) {
                parts.push(res.data.records[0].campusName);
            }
        } catch (error) {
            console.warn('获取门店名称失败:', error);
        }
    }

    // 添加时间范围
    if (state.queryForm.classStartDateTime && state.queryForm.classEndDateTime) {
        const startDate = state.queryForm.classStartDateTime.split(' ')[0];
        const endDate = state.queryForm.classEndDateTime.split(' ')[0];
        parts.push(`${startDate}至${endDate}`);
    }

    // 拼接文件名
    if (parts.length > 0) {
        fileName += '_' + parts.join('_');
    }

    return fileName + '.xlsx';
};

// 导出Excel
const exportExcel = async () => {
    try {
        // 验证时间范围不能超过31天
        if (state.queryForm.classStartDateTime && state.queryForm.classEndDateTime) {
            const startDate = moment(state.queryForm.classStartDateTime);
            const endDate = moment(state.queryForm.classEndDateTime);
            const daysDiff = endDate.diff(startDate, 'days') + 1; // +1 包含结束日期当天

            if (daysDiff > 31) {
                await ElMessageBox.alert('导出数据不能大于31天，请更改条件后重试', '提示', {
                    confirmButtonText: '确定',
                    type: 'warning',
                });
                return;
            }
        }

        const fileName = await generateFileName();
        downBlobFile('/edusystem/courseMakeUpOnline/export', state.queryForm, fileName);
    } catch (error: any) {
        console.error('导出失败:', error);
        // 这里可以添加错误提示，如果需要的话
    }
};
</script>

<style scoped lang="scss">
.el-dropdown {
    display: flex;
    align-items: center;
    justify-content: center;

    :deep(.el-dropdown-link) {
        cursor: pointer;
        color: var(--el-color-primary);
        display: flex;
        align-items: center;
    }
}

:deep(.el-overlay .el-overlay-dialog .el-dialog .el-dialog__body) {
    text-align: center;
}

:deep(.el-dialog__header) {
    padding: 0;
}
</style>
