<template>
    <el-dialog v-model="visible" :close-on-click-modal="false" title="查看截图" width="80%">
        <el-row :gutter="12" v-viewer>
            <el-col :lg="6" :sm="12" v-for="(item, index) in screenshotList" :key="item.id">
                <el-card shadow="never" :body-style="bodyStyle" class="mb-4 screen-shoot-card">
                    <template #header>
                        <span>{{ item.resourcesName }}</span>
                        <el-tooltip placement="bottom" effect="light">
                            <div>
                                <el-button type="primary" link class="ml-[2px]">
                                    人员总数:{{ item.recognitionTotalNum || '--' }}
                                </el-button>
                                <el-button type="primary" link class="ml-[2px]">
                                    人体识别数:{{ item.recognitionHumanNum || '--' }}
                                </el-button>
                            </div>

                            <template #content>
                                <div>识别幼儿数量:{{ item.recognitionChildrenNum }}</div>
                                <div>识别青少年数量:{{ item.recognitionTeenagersNum }}</div>
                                <div>识别青年数量:{{ item.recognitionYouthNum }}</div>
                                <div>识别中年数量:{{ item.recognitionMiddleNum }}</div>
                                <div>识别老年数量:{{ item.recognitionElderlyNum }}</div>
                            </template>
                        </el-tooltip>
                    </template>

                    <!-- <img :src="item.resourcesPath" class="max-w-full max-h-52" /> -->
                    <el-image
                        class="max-w-full max-h-52"
                        :src="item.resourcesPath"
                        :zoom-rate="1.2"
                        :max-scale="7"
                        :min-scale="0.2"
                        :preview-src-list="srcList"
                        :initial-index="index"
                        fit="contain"
                    />
                </el-card>
            </el-col>
        </el-row>
        <el-empty v-if="!srcList.length">
            <el-button type="primary" @click="getList">刷新</el-button>
        </el-empty>
        <el-pagination
            v-model:current-page="state.current"
            :page-size="state.size"
            layout="total, prev, pager, next"
            :total="state.total"
            :page-sizes="[10, 20, 30, 40]"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
        />
    </el-dialog>
</template>

<script lang="ts" setup>
import { ssScreenshotDetail } from '/@/api/eduConnect/ssClassTime';
const visible = ref(false);
const bodyStyle = {
    padding: 0,
    display: 'flex',
    justifyContent: 'center',
};
const state = reactive({
    current: 1,
    size: 16,
    total: 0,
});
const screenshotList = ref([] as any);
const queryId = ref();
const srcList = ref([] as any);
const openDialog = (id: any) => {
    visible.value = true;
    srcList.value = [];
    screenshotList.value = [];
    queryId.value = id;
    getList();
};
const getList = () => {
    ssScreenshotDetail({
        ...state,
        ...{
            classTimeId: unref(queryId),
        },
    }).then((res) => {
        screenshotList.value = res.data.records;
        srcList.value = res.data.records.map((item: any) => item.resourcesPath);
        state.total = res.data.total;
    });
};
const handleSizeChange = (val: number) => {
    state.size = val;
    getList();
};
const handleCurrentChange = (val: number) => {
    state.current = val;
    getList();
};

defineExpose({ openDialog });
</script>

<style lang="scss" scoped>
.screen-shoot-card {
    border-radius: 4px;
    :deep(.el-card__header) {
        padding: 8px;
    }
}
</style>
