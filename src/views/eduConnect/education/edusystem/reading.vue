<template>
    <div class="layout-padding">
        <div class="layout-padding-auto layout-padding-view">
            <el-row class="ml10" v-show="showSearch">
                <el-form :inline="true" :model="state.queryForm" ref="queryRef">
                    <el-form-item label="班级名称" prop="deviceNo">
                        <yd-autocomplete
                            class="w-[240px]"
                            placeholder="输入搜索班级信息"
                            v-model="state.queryForm.classIdList"
                            :request-url="getSsClassList"
                            request-name="className"
                            data-label="className"
                            data-key="id"
                            behavior="idList"
                        ></yd-autocomplete>
                    </el-form-item>
                    <el-form-item label="关联书籍" prop="booksName">
                        <el-input
                            class="w-[240px]"
                            placeholder="请输入关联书籍"
                            clearable
                            v-model="state.queryForm.booksName"
                        />
                    </el-form-item>
                    <el-form-item label="授权门店" prop="campusId">
                        <fuzzy-search
                            class="w-[240px]"
                            placeholder="输入搜索授权门店"
                            v-model="state.queryForm.campusId"
                            :requestUrl="getCampusList"
                            request-name="query"
                            data-label="campusName"
                            clearable
                        ></fuzzy-search>
                    </el-form-item>
                    <el-form-item label="上课教室" prop="classRoomId">
                        <el-select
                            class="w-[240px]"
                            v-model="state.queryForm.classRoomId"
                            placeholder="请选择上课教室"
                            clearable
                        >
                            <el-option
                                v-for="item in live_room"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="主讲老师" prop="lecturerId">
                        <fuzzy-search
                            class="w-[240px]"
                            placeholder="输入搜索主讲老师"
                            v-model="state.queryForm.lecturerId"
                            :requestUrl="getLecturerList"
                            request-name="query"
                            data-label="lecturerName"
                            clearable
                        ></fuzzy-search>
                    </el-form-item>
                    <el-form-item label="课程状态" prop="attendClassState">
                        <el-select
                            class="w-[240px]"
                            v-model="state.queryForm.attendClassState"
                            placeholder="请选择课程状态"
                            clearable
                        >
                            <el-option
                                v-for="item in attend_class_state"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="上课时间">
                        <el-date-picker
                            class="w-[240px]"
                            v-model="queryDate"
                            type="daterange"
                            range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            @change="queryDateChange(queryDate)"
                        />
                    </el-form-item>
                    <el-form-item>
                        <el-button @click="getDataList" icon="search" type="primary">
                            {{ $t('common.queryBtn') }}
                        </el-button>
                        <el-button @click="resetQuery" icon="Refresh">{{ $t('common.resetBtn') }} </el-button>
                    </el-form-item>
                </el-form>
            </el-row>
            <el-row>
                <div class="mb8" style="width: 100%">
                    <el-button
                        icon="folder-add"
                        type="primary"
                        class="ml10"
                        @click="formDialogRef.openDialog(attendClassType)"
                        v-auth="'edusystem_ssCourseSchedule_reading_add'"
                    >
                        新增
                    </el-button>
                    <el-button
                        plain
                        :disabled="multiple"
                        icon="Delete"
                        type="danger"
                        v-auth="'edusystem_ssClassTime_getSupervisionUrl'"
                        @click="handleDelete(selectObjs)"
                    >
                        删除
                    </el-button>
                    <el-button icon="Refresh" type="primary" plain class="ml10" @click="handleSyncToSchoolManager">
                        同步今日排课到校管家
                    </el-button>
                    <right-toolbar
                        v-model:showSearch="showSearch"
                        :export="false"
                        @exportExcel="exportExcel"
                        class="ml10 mr20"
                        style="float: right"
                        @queryTable="getDataList"
                    ></right-toolbar>
                </div>
            </el-row>
            <el-table
                :data="state.dataList"
                v-loading="state.loading"
                border
                :cell-style="tableStyle.cellStyle"
                :header-cell-style="tableStyle.headerCellStyle"
                @selection-change="selectionChangHandle"
                @sort-change="sortChangeHandle"
            >
                <el-table-column type="selection" width="40" align="center" fixed="left" :selectable="setSelectable" />
                <el-table-column type="index" label="序号" width="60" fixed="left" />
                <el-table-column prop="className" label="班级名称" show-overflow-tooltip width="180" fixed="left" />
                <el-table-column prop="booksName" label="关联书籍" show-overflow-tooltip width="200" />
                <el-table-column prop="lecturerName" label="主讲老师" show-overflow-tooltip width="160" />
                <el-table-column prop="classRoomId" label="上课教室" show-overflow-tooltip width="120">
                    <template #default="{ row }">
                        <dict-tag :options="live_room" :value="row.classRoomId"></dict-tag>
                    </template>
                </el-table-column>

                <el-table-column
                    prop="authRoomCount"
                    label="授权门店"
                    show-overflow-tooltip
                    width="100"
                    v-auth="'course_schedule_reading_add'"
                >
                    <template #default="{ row }">
                        <el-button circle @click="accreditCampusRef?.openDialog(row.id)">{{
                            row.authRoomCount
                        }}</el-button>
                    </template>
                </el-table-column>
                <el-table-column prop="attendClassDate" label="上课时间" width="160">
                    <template #default="{ row }">
                        <div>{{ row.attendClassDate }}</div>
                        <div>{{ row.attendClassStartTime }} - {{ row.attendClassEndTime }}</div>
                    </template>
                </el-table-column>
                <el-table-column prop="attendClassType" label="上课类型" show-overflow-tooltip width="90">
                    <template #default="scope">
                        <dict-tag
                            :options="course_type"
                            :type="
                                setTagType(scope.row.deviceState, {
                                    [AttendClassType.liveType]: 'success',
                                    [AttendClassType.pointType]: 'danger',
                                })
                            "
                            :value="scope.row.attendClassType"
                        ></dict-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="attendClassState" label="状态" show-overflow-tooltip width="90">
                    <template #default="scope">
                        <dict-tag :options="attend_class_state" :value="scope.row.attendClassState"></dict-tag>
                    </template>
                </el-table-column>

                <el-table-column
                    prop="supervisionClassUrl"
                    label="监课链接"
                    show-overflow-tooltip
                    width="160"
                    v-auth="'edusystem_ssClassTime_getSupervisionUrl'"
                >
                    <template #default="{ row }">
                        <el-button
                            type="primary"
                            icon="Link"
                            :disabled="[ClassStatus.end].includes(row.attendClassState)"
                            text
                            @click="seeClassUrl(row, 1)"
                        >
                            查看
                        </el-button>
                        <el-button
                            icon="CopyDocument"
                            :disabled="[ClassStatus.end].includes(row.attendClassState)"
                            text
                            type="primary"
                            @click="seeClassUrl(row, 0)"
                        >
                            复制
                        </el-button>
                    </template>
                </el-table-column>

                <el-table-column
                    prop="supervisionClassStartTime"
                    label="监课开始时间"
                    show-overflow-tooltip
                    width="160"
                >
                    <template #default="{ row }">{{ row.supervisionClassStartTime || '--' }}</template>
                </el-table-column>
                <el-table-column prop="supervisionClassEndTime" label="监课结束时间" show-overflow-tooltip width="160">
                    <template #default="{ row }">{{ row.supervisionClassEndTime || '--' }}</template>
                </el-table-column>
                <el-table-column prop="" label="课程截图" show-overflow-tooltip width="140">
                    <template #default="{ row }">
                        <el-button
                            link
                            :disabled="!row.isScreenshot"
                            :type="row.isScreenshot ? 'primary' : 'info'"
                            @click="screenshot.openDialog(row.id)"
                        >
                            {{ row.isScreenshot ? '查看截图' : '当前课程无截图' }}
                        </el-button>
                    </template>
                </el-table-column>
                <el-table-column prop="creator" label="创建人" show-overflow-tooltip width="120" />
                <el-table-column prop="ctime" label="创建时间" show-overflow-tooltip width="160" />
                <el-table-column label="操作" width="180" fixed="right">
                    <template #default="scope">
                        <el-button
                            :icon="[ClassStatus.end].includes(scope.row.attendClassState) ? 'view' : 'edit-pen'"
                            text
                            type="primary"
                            v-auth="'edusystem_ssClassTime_getRoomTimeCode'"
                            @click="
                                classFormDialogRef?.openDialog({
                                    type: attendClassType,
                                    editType: ClassTimeEditEnums.EDIT,
                                    id: scope.row.id,
                                }),
                                    (editFormDisabled = [ClassStatus.end].includes(scope.row.attendClassState))
                            "
                            >{{ [ClassStatus.end].includes(scope.row.attendClassState) ? '查看' : '编辑' }}</el-button
                        >
                        <el-button
                            icon="plus"
                            text
                            type="primary"
                            v-auth="'edusystem_ssClassTime_getRoomTimeCode'"
                            @click="
                                classFormDialogRef?.openDialog({
                                    type: attendClassType,
                                    editType: ClassTimeEditEnums.ADD,
                                    id: scope.row.id,
                                })
                            "
                        >
                            新增课次
                        </el-button>
                        <el-popover placement="left" :width="200" trigger="click">
                            <template #reference>
                                <el-button
                                    icon="VideoCamera"
                                    text
                                    type="primary"
                                    v-auth="'edusystem_ssClassTime_getRoomTimeCode'"
                                    :disabled="[ClassStatus.end].includes(scope.row.attendClassState)"
                                    >上课码</el-button
                                >
                            </template>
                            <template #default>
                                <div>
                                    <span>主讲端上课码: </span>
                                    <el-button
                                        v-if="!scope.row.lecturerRoomCode"
                                        type="primary"
                                        text
                                        size="small"
                                        @click="createCode(scope.row.id, scope.$index, 1)"
                                    >
                                        生成
                                    </el-button>
                                    <template v-if="scope.row.lecturerRoomCode">
                                        <span>{{ scope.row.lecturerRoomCode }}</span>
                                        <el-icon
                                            class="ml-2 cursor-pointer"
                                            @click="copyText(scope.row.lecturerRoomCode)"
                                        >
                                            <CopyDocument />
                                        </el-icon>
                                    </template>
                                </div>
                                <div>
                                    <span>教室端上课码: </span>
                                    <el-button
                                        type="primary"
                                        v-if="!scope.row.classRoomCode"
                                        text
                                        size="small"
                                        @click="createCode(scope.row.id, scope.$index, 2)"
                                    >
                                        生成
                                    </el-button>
                                    <template v-if="scope.row.classRoomCode">
                                        <span>{{ scope.row.classRoomCode }}</span>
                                        <el-icon class="ml-2 cursor-pointer" @click="copyText(scope.row.classRoomCode)">
                                            <CopyDocument />
                                        </el-icon>
                                    </template>
                                </div>
                            </template>
                        </el-popover>

                        <!-- <el-button icon="view" text type="primary"
                            v-auth="'edusystem_course_schedule_edit'">上课码</el-button> -->
                        <el-button
                            icon="delete"
                            text
                            type="primary"
                            v-auth="'edusystem_ssClassTime_getSupervisionUrl'"
                            @click="handleDelete([scope.row.id])"
                            :disabled="[ClassStatus.underway, ClassStatus.end].includes(scope.row.attendClassState)"
                            >删除</el-button
                        >
                    </template>
                </el-table-column>
            </el-table>
            <pagination
                @size-change="sizeChangeHandle"
                @current-change="currentChangeHandle"
                v-bind="state.pagination"
            />
        </div>
        <!-- 授权门店 -->
        <accredit-campus ref="accreditCampusRef" @refresh="getDataList(false)"></accredit-campus>

        <!-- 查看截图 -->
        <ss-screenshot ref="screenshot"></ss-screenshot>

        <!-- 新增排课  -->
        <course-scheduling ref="formDialogRef" @refresh="getDataList(false)" />

        <!-- 新增课次 -->
        <edit-form ref="classFormDialogRef" :disabled="editFormDisabled" @refresh="getDataList(false)"></edit-form>
    </div>
</template>

<script setup lang="ts" name="systemSsClassTime">
import { BasicTableProps, useTable } from '/@/hooks/table';
import {
    fetchList,
    delObjs,
    genRoomTimeCode,
    getSupervisionUrl,
    inRoomByRoomUuid,
    syncToSchoolManager,
} from '/@/api/eduConnect/ssClassTime';
import { useMessage, useMessageBox } from '/@/hooks/message';
import { useDict } from '/@/hooks/dict';
import { getSsClassList, getLecturerList, getCampusList } from '/@/api/eduConnect/ssQuery';
import fuzzySearch from '/@/components/EduComponents/fuzzySearch.vue';
import YdAutocomplete from '/@/components/YdAutocomplete/index.vue';
import {
    AttendClassType,
    setTagType,
    ClassTimeEditEnums,
    RoomCodeEnums,
    ClassStatus,
} from '/@/components/EduComponents/enums/eduEnums';
import { formatDate } from '/@/utils/formatTime';
import ssScreenshot from './ssScreenshot.vue';
import { ClassTimeEditExpose } from '/@/components/EduComponents/types/classTimeType';
import accreditCampus from './accreditCampus.vue';
import commonFunction from '/@/utils/commonFunction';
import { ElLoading } from 'element-plus';

// 引入组件
const CourseScheduling = defineAsyncComponent(() => import('/@/components/EduComponents/courseScheduling/index.vue'));
const EditForm = defineAsyncComponent(() => import('/@/components/EduComponents/classTime/index.vue'));
// 定义查询字典
const { live_room, course_type, attend_class_state } = useDict('live_room', 'course_type', 'attend_class_state');
// 定义变量内容
const formDialogRef = ref();
const classFormDialogRef = ref<(InstanceType<typeof EditForm> & ClassTimeEditExpose) | null>(null);
const accreditCampusRef = ref();
const screenshot = ref();
const attendClassType = ref<AttendClassType>(AttendClassType.liveType); //默认是直播类型
const editFormDisabled = ref(false);
// 搜索变量
const queryRef = ref();
const showSearch = ref(true);
// 多选变量
const selectObjs = ref([]) as any;
const multiple = ref(true);
const queryDate = ref();
const state: BasicTableProps = reactive<BasicTableProps>({
    queryForm: {
        classIdList: [],
    },
    pageList: fetchList,
});
//  table hook
const { getDataList, currentChangeHandle, sizeChangeHandle, sortChangeHandle, downBlobFile, tableStyle } =
    useTable(state);

// 清空搜索条件
const resetQuery = () => {
    // 清空搜索条件
    queryRef.value?.resetFields();
    queryDate.value = undefined;
    state.queryForm = {};
    // 清空多选
    selectObjs.value = [];
    getDataList();
};

// 导出excel
const exportExcel = () => {
    downBlobFile(
        '/eduConnect/ssClassTime/export',
        Object.assign(state.queryForm, { ids: selectObjs }),
        'ssClassTime.xlsx'
    );
};

//处理日期格式
const queryDateChange = (date: Date[]) => {
    state.queryForm.selectAttendClassStartTime = date ? formatDate(date[0] as Date, 'YYYY-mm-dd') + ' 00:00:00' : '';
    state.queryForm.selectAttendClassEndTime = date ? formatDate(date[1] as Date, 'YYYY-mm-dd') + ' 23:59:59' : '';
};
// 多选事件
const selectionChangHandle = (objs: { id: string }[]) => {
    selectObjs.value = objs.map(({ id }) => id);
    multiple.value = !objs.length;
};
const { copyText } = commonFunction();
// 删除操作
const handleDelete = async (ids: string[]) => {
    try {
        await useMessageBox().confirm('此操作将永久删除');
    } catch {
        return;
    }

    try {
        await delObjs(ids);
        getDataList();
        useMessage().success('删除成功');
    } catch (err: any) {
        useMessage().error(err.msg);
    }
};
const createCode = (id: string, index: number, type: any) => {
    genRoomTimeCode({
        id,
        roomCodeType: type,
    })
        .then((res) => {
            if (state && state.dataList && index >= 0 && index < state.dataList.length) {
                type == RoomCodeEnums.lecturerRoomCode
                    ? (state.dataList[index].lecturerRoomCode = res.msg)
                    : (state.dataList[index].classRoomCode = res.msg);
            }
        })
        .catch((err) => {
            useMessage().error(err.msg);
        });
};
const setSelectable = (row: any) => {
    return ![ClassStatus.underway, ClassStatus.end].includes(row.attendClassState);
};
const seeClassUrl = async (row: any, type: number) => {
    if (!row.id) {
        return useMessage().warning('当前课程状态无法进入监课');
    }
    const loading = ElLoading.service({
        lock: true,
        text: '生成中',
        background: 'rgba(0, 0, 0, 0.7)',
    });
    let urlParams = await getSupervisionUrl({
        id: row.id,
    })
        .then((res) => {
            return Promise.resolve(res.data);
        })
        .catch((err) => {
            useMessage().error(err.msg);
            loading.close();
        });

    await inRoomByRoomUuid({
        roomUuid: urlParams.roomUuid,
    })
        .then((res) => {
            const userInfo = res.data;
            let params = {
                roomUuid: userInfo.roomUuid,
                rtmToken: userInfo.rtmToken,
                userUuid: userInfo.userUuid,
                userName: userInfo.userName,
                roleType: userInfo.roleType,
                roomName: userInfo.roomName,
                duration: userInfo.duration,
                className: row.className,
                attendClassDateTime: userInfo.classDateTime,
                appId: userInfo.appId,
                id: row.id,
                // startTime: +new Date()
            };
            let url = urlParams.supervisionClassUrl + '?' + setData(params);
            if (type == 1) {
                window.open(url);
            } else {
                copyText(url);
            }
            loading.close();
        })
        .catch((err) => {
            useMessage().error(err.msg);
            loading.close();
        });
};
const setData = (obj: any) => {
    let list = [];
    for (const key in obj) {
        list.push(key + '=' + encodeURIComponent(obj[key]));
    }
    return list.join('&');
};

// 同步今日排课到校管家
const handleSyncToSchoolManager = async () => {
    try {
        await useMessageBox().confirm('此操作将同步今日排课到校管家');
    } catch {
        return;
    }
    try {
        await syncToSchoolManager();
        useMessage().success('同步成功');
    } catch (err: any) {
        useMessage().error(err.msg);
    }
};
</script>
