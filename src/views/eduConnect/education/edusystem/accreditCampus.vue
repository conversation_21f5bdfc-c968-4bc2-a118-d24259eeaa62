<template>
    <el-dialog v-model="visible" draggable title="授权门店" width="1200">
        <el-transfer
            v-model="targetKeys"
            :data="allRoom"
            filterable
            :props="{
                key: 'id',
                label: 'customName',
            }"
            :titles="['未选中门店', '选中门店']"
        />
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="visible = false">取 消</el-button>
                <el-button type="primary" @click="onSubmit" :loading="loading">确 认</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script lang="ts" setup>
import { differenceWith } from 'lodash';
import { authRoomList, authClassTimeRoom } from '/@/api/eduConnect/ssClassTime';
import { authVodRoomList, authVodClassTimeRoom } from '/@/api/eduConnect/ssPoint';
import { AttendClassType } from '/@/components/EduComponents/enums/eduEnums';
import { useMessage } from '/@/hooks/message';
const visible = ref(false);
const allRoom = ref([]);
const targetKeys = ref([]);
const loading = ref(false);
const classTimeId = ref();
const emit = defineEmits(['refresh']);
const attendClassType = ref<AttendClassType>(AttendClassType.liveType);
const openDialog = (id: string, type: AttendClassType) => {
    visible.value = true;
    targetKeys.value = [];
    allRoom.value = [];
    classTimeId.value = id;
    attendClassType.value = type;
    getList(id);
};
const getList = (id: string) => {
    let url = unref(attendClassType) === AttendClassType.liveType ? authRoomList : authVodRoomList;
    url(id).then((res) => {
        allRoom.value = res.data.map((item: any) => {
            return {
                ...item,
                customName: `${item.campusName} (${item.deviceName}--${item.classRoomName})`,
            };
        });
        targetKeys.value = res.data.filter((item: any) => item.authState == 1).map((item: any) => `${item.id}`);
    });
};
const onSubmit = () => {
    if (!unref(targetKeys).length) {
        useMessage().warning('请选择授权门店');
        return false;
    }
    const values = differenceWith(
        unref(allRoom),
        differenceWith(unref(allRoom), unref(targetKeys), (a: any, b: any) => a.id == b),
        (a: any, b: any) => a.id == b.id
    ).map((item) => {
        return {
            classRoomId: item.classRoomId,
            campusId: item.campusId,
            deviceId: item.id,
            xgjCampusId: item.xgjCampusId,
            xgjClassRoomId: item.xgjClassRoomId,
        };
    });
    loading.value = true;
    let url = unref(attendClassType) === AttendClassType.liveType ? authClassTimeRoom : authVodClassTimeRoom;
    url({
        id: unref(classTimeId),
        authDeviceList: values,
    })
        .then(() => {
            visible.value = false;
            emit('refresh');
        })
        .finally(() => {
            loading.value = false;
        });
};
defineExpose({ openDialog });
</script>

<style lang="scss" scoped>
::v-deep(.el-transfer-panel) {
    width: 300px;
}

::v-deep(.el-transfer__buttons) {
    padding: 0px 8px;
}

::v-deep(.el-transfer-panel) {
    width: 500px;
}

::v-deep(.el-transfer-panel__body) {
    height: 400px;
}
</style>
