<template>
    <div class="layout-padding">
        <div class="layout-padding-auto layout-padding-view">
            <!-- <div class="mb15">
                <el-form :inline="true" @submit.prevent>
                    <el-form-item>
                        <el-select v-model="queryForm.year" placeholder="年份" clearable>
                            <el-option v-for="year in yearOptions" :key="year" :label="year" :value="year" />
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="getDataList">
                            <el-icon>
                                <ele-Search />
                            </el-icon>
                            查询
                        </el-button>
                    </el-form-item>
                </el-form>
            </div> -->

            <el-table
                ref="dataTableRef"
                v-loading="loading"
                :data="dataList"
                style="width: 100%"
                :cell-style="tableStyle.cellStyle"
                :header-cell-style="tableStyle.headerCellStyle"
            >
                <el-table-column label="周期" prop="cycleType">
                    <template #default="scope">
                        <span v-if="scope.row.cycleType === 1">本周期</span>
                        <span v-if="scope.row.cycleType === 2">上周期及之前</span>
                    </template>
                </el-table-column>
                <el-table-column label="开始时间" prop="beginDate" />
                <el-table-column label="结束时间" prop="endDate" />
                <el-table-column label="锁定状态" prop="checkinLocked">
                    <template #default="scope">
                        <span v-if="scope.row.checkinLocked === 0">未锁定</span>
                        <span v-if="scope.row.checkinLocked === 1">已锁定</span>
                    </template>
                </el-table-column>
                <el-table-column label="操作" fixed="right" width="180">
                    <template #default="scope">
                        <el-button
                            :disabled="scope.row.checkinLocked === 1"
                            size="small"
                            type="primary"
                            @click="handleEdit(scope.row)"
                            v-if="scope.row.cycleType === 1"
                        >
                            修改
                        </el-button>

                        <el-button
                            v-if="scope.row.checkinLocked === 0"
                            size="small"
                            type="warning"
                            @click="handleLock(scope.row)"
                        >
                            锁定
                        </el-button>
                        <el-button v-else size="small" type="warning" plain @click="handleUnlock(scope.row)">
                            解锁
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>

            <!-- 表单弹窗 -->
            <el-dialog
                v-model="dialog.visible"
                :title="dialog.title"
                width="500px"
                destroy-on-close
                @closed="resetForm"
            >
                <el-form ref="formRef" :model="formData" :rules="formRules" label-width="100px">
                    <el-form-item label="开始时间" prop="beginDate">
                        <el-date-picker
                            v-model="formData.beginDate"
                            type="date"
                            placeholder="选择开始日期"
                            format="YYYY-MM-DD"
                            value-format="YYYY-MM-DD"
                            style="width: 260px"
                            @change="handleBeginDateChange"
                        />
                    </el-form-item>
                    <el-form-item label="结束时间" prop="endDate">
                        <el-date-picker
                            v-model="formData.endDate"
                            type="date"
                            placeholder="选择结束日期"
                            format="YYYY-MM-DD"
                            value-format="YYYY-MM-DD"
                            style="width: 260px"
                            :disabled="true"
                        />
                        <div class="date-tip">结束时间为开始时间后的第6天</div>
                    </el-form-item>
                </el-form>
                <template #footer>
                    <div class="dialog-footer">
                        <el-button @click="dialog.visible = false">取消</el-button>
                        <el-button type="primary" @click="submitForm">确定</el-button>
                    </div>
                </template>
            </el-dialog>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ElMessageBox, FormInstance, FormRules } from 'element-plus';
import { useMessage } from '/@/hooks/message';
import { TableStyle } from '/@/hooks/table';
import { getCycleList, putObj, lockCycle, unlockCycle } from '/@/api/eduConnect/attendanceLockCycle';

// 查询表单
// const queryForm = reactive({
//     year: new Date().getFullYear().toString(),
// });

// 表格样式
const tableStyle: TableStyle = {
    cellStyle: { textAlign: 'center' },
    headerCellStyle: {
        textAlign: 'center',
        background: 'var(--el-table-row-hover-bg-color)',
        color: 'var(--el-text-color-primary)',
    },
};

// 数据列表和加载状态
const dataList = ref([]);
const loading = ref(false);

// 表格引用
const dataTableRef = ref();

// 可选年份
// const yearOptions = ['2024', '2025', '2026'];

// 弹窗状态
const dialog = reactive({
    visible: false,
    title: '新增考勤锁定周期',
    type: 'add', // add or edit
});

// 表单引用
const formRef = ref<FormInstance>();

interface FormData {
    id: number | null;
    beginDate: string;
    endDate: string;
}

// 表单数据
const formData = reactive<FormData>({
    id: null,
    beginDate: '',
    endDate: '',
});

// 表单校验规则
const formRules = reactive<FormRules>({
    beginDate: [{ required: true, message: '请选择开始日期', trigger: 'blur' }],
    endDate: [{ required: true, message: '请选择结束日期', trigger: 'blur' }],
});

// 处理开始日期变化
const handleBeginDateChange = (date: string) => {
    if (date) {
        const beginDate = new Date(date);
        const endDate = new Date(beginDate);
        endDate.setDate(beginDate.getDate() + 6);

        // 格式化为 YYYY-MM-DD
        const year = endDate.getFullYear();
        const month = String(endDate.getMonth() + 1).padStart(2, '0');
        const day = String(endDate.getDate()).padStart(2, '0');
        formData.endDate = `${year}-${month}-${day}`;
    } else {
        formData.endDate = '';
    }
};

// 获取数据列表
const getDataList = async () => {
    try {
        loading.value = true;
        const res = await getCycleList();
        if (res.data) {
            dataList.value = res.data || [];
        }
    } catch (error: any) {
        useMessage().error(error);
    } finally {
        loading.value = false;
    }
};

// 初始化数据
onMounted(() => {
    getDataList();
});

// 重置表单
const resetForm = () => {
    formData.id = null;
    formData.beginDate = '';
    formData.endDate = '';
    if (formRef.value) {
        formRef.value.resetFields();
    }
};

// 处理编辑
const handleEdit = (row: any) => {
    dialog.type = 'edit';
    dialog.title = '修改考勤锁定周期';
    dialog.visible = true;

    formData.id = Number(row.id);
    formData.beginDate = row.beginDate;
    formData.endDate = row.endDate;
};

// 处理锁定
const handleLock = (row: any) => {
    ElMessageBox.confirm('确认要锁定该周期吗？锁定后将无法修改', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    })
        .then(async () => {
            try {
                await lockCycle(row.id, 1);
                useMessage().success('锁定成功');
                getDataList();
            } catch (error: any) {
                useMessage().error(error);
            }
        })
        .catch(() => {});
};

// 处理解锁
const handleUnlock = (row: any) => {
    ElMessageBox.confirm('确认要解锁该周期吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    })
        .then(async () => {
            try {
                await unlockCycle(row.id, 0);
                useMessage().success('解锁成功');
                getDataList();
            } catch (error: any) {
                useMessage().error(error);
            }
        })
        .catch(() => {});
};

// 提交表单
const submitForm = async () => {
    if (!formRef.value) return;
    await formRef.value.validate(async (valid) => {
        if (valid) {
            try {
                loading.value = true;
                await putObj(formData);
                useMessage().success('修改成功');
                dialog.visible = false;
                resetForm();
                getDataList();
            } catch (error: any) {
                useMessage().error(error.msg);
            } finally {
                loading.value = false;
            }
        }
    });
};
</script>

<style scoped>
.date-tip {
    font-size: 12px;
    color: #909399;
    margin-top: 5px;
    padding-left: 5px;
}
</style>
