<template>
    <div class="layout-padding">
        <div class="layout-padding-auto layout-padding-view">
            <el-row v-show="showSearch">
                <el-form :model="state.queryForm" ref="queryRef" :inline="true" @keyup.enter="getDataList">
                    <el-form-item label="门店名称" prop="storeId">
                        <fuzzy-search
                            class="w-96"
                            placeholder="请输入门店名称"
                            v-model="state.queryForm.storeId"
                            :requestUrl="getStoreSelectList"
                            request-name="campusName"
                            data-label="campusName"
                            clearable
                        ></fuzzy-search>
                    </el-form-item>
                    <el-form-item label="课程类型" prop="courseType">
                        <el-select v-model="state.queryForm.courseType" placeholder="请选择课程类型" clearable>
                            <el-option
                                v-for="item in courseTypeList"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="收款单位" prop="orgId">
                        <el-select v-model="state.queryForm.orgId" placeholder="请选择收款单位" clearable>
                            <el-option
                                v-for="item in joint_signing_subject"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            />
                        </el-select>
                    </el-form-item>

                    <el-form-item label="状态" prop="status">
                        <el-select v-model="state.queryForm.status" placeholder="请选择状态" clearable>
                            <el-option
                                v-for="item in joint_bill_status"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="收费日期" prop="paymentDate">
                        <el-date-picker
                            v-model="state.queryForm.paymentDate"
                            type="date"
                            placeholder="请选择日期"
                            clearable
                        />
                    </el-form-item>
                    <el-form-item>
                        <el-button icon="Search" type="primary" @click="getDataList">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-row>

            <el-row>
                <div class="mb8" style="width: 100%">
                    <right-toolbar
                        v-model:showSearch="showSearch"
                        class="ml10 mr20"
                        style="float: right"
                        @queryTable="getDataList"
                    />
                </div>
            </el-row>

            <el-table
                :data="state.dataList"
                v-loading="state.loading"
                border
                :cell-style="tableStyle.cellStyle"
                :header-cell-style="tableStyle.headerCellStyle"
            >
                <el-table-column type="index" label="序号" width="60" />
                <el-table-column prop="id" label="收费单编号" width="180" />
                <el-table-column prop="courseType" label="收费课程" width="120">
                    <template #default="{ row }">
                        {{ getCourseName(row.courseType) }}
                    </template>
                </el-table-column>
                <el-table-column prop="jointPayType" label="收费类别" width="120">
                    <template #default="{ row }">
                        <dict-tag :options="joint_charge_type" :value="row.jointPayType"></dict-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="studentName" label="学员姓名" width="120" />
                <el-table-column prop="studentPhone" label="学员手机号" width="120" />
                <el-table-column prop="campusName" label="所属门店" min-width="180" />
                <el-table-column label="用户姓名" width="100">
                    <template #default="{ row }">
                        {{ row.campusJoint.customerName }}
                    </template>
                </el-table-column>
                <el-table-column label="用户手机号" width="120">
                    <template #default="{ row }">
                        {{ row.campusJoint.signingSubjectPhone }}
                    </template>
                </el-table-column>
                <el-table-column label="支付方式" width="120">
                    <template #default="{ row }">
                        <dict-tag :options="joint_pay_type" :value="row.payType"></dict-tag>
                    </template>
                </el-table-column>
                <el-table-column label="支付类型" width="120">
                    <template #default="{ row }">
                        <dict-tag :options="joint_pay_method" :value="row.payMethod"></dict-tag>
                    </template>
                </el-table-column>
                <el-table-column label="联营支付模式" width="120">
                    <template #default="{ row }">
                        <dict-tag :options="joint_pay_mode" :value="row.payMode"></dict-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="totalAmount" label="收费金额" width="120">
                    <template #default="{ row }">
                        {{ row.totalAmount.toFixed(2) }}
                    </template>
                </el-table-column>
                <el-table-column label="收款单位" min-width="260">
                    <template #default="{ row }">
                        {{ row.campusJoint.signingSubject }}
                    </template>
                </el-table-column>
                <el-table-column label="业务部门" width="140">
                    <template #default="{ row }">
                        {{ row.campusJoint.departmentName }}
                    </template>
                </el-table-column>
                <el-table-column prop="createBy" label="创建人" width="120" />
                <el-table-column prop="nullify" label="状态" width="100">
                    <template #default="{ row }">
                        <dict-tag
                            :options="joint_bill_status"
                            :type="row.status === 1 ? 'primary' : 'danger'"
                            :value="row.status"
                        ></dict-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="message" label="异常原因" width="140" show-overflow-tooltip>
                    <template #default="{ row }">
                        {{ row.message || '--' }}
                    </template>
                </el-table-column>
                <el-table-column prop="feeDate" label="收费日期" width="120" />
                <el-table-column prop="notes" label="备注" width="140" show-overflow-tooltip />
                <el-table-column label="操作" width="120" fixed="right">
                    <template #default="{ row }">
                        <el-button v-if="row.status !== 1" type="primary" link icon="Refresh" @click="handleRetry(row)">
                            重新推送
                        </el-button>
                        <span v-else> -- </span>
                    </template>
                </el-table-column>
            </el-table>

            <pagination
                @size-change="sizeChangeHandle"
                @current-change="currentChangeHandle"
                v-bind="state.pagination"
            />
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue';
import { BasicTableProps, useTable } from '/@/hooks/table';
import { useMessage } from '/@/hooks/message';
import { getJointBillList, retryJointBill } from '/@/api/eduConnect/jointBill';
import { allCourseTypeApi } from '/@/api/eduConnect/classType';
import { useDict } from '/@/hooks/dict';

// 直接使用接口定义，导入JointBillData类型
import type { JointBillData } from '/@/api/eduConnect/jointBill';
import { getSchoolStoreList } from '/@/api/eduConnect/campusStore';
import fuzzySearch from '/@/components/EduComponents/fuzzySearch.vue';

// 定义变量内容
const queryRef = ref();
const showSearch = ref(true);

// 下拉选项数据
const courseTypeList = ref<Array<{ value: string; label: string }>>([]);

// joint_charge_type  联营收费类型字典
// joint_pay_method  联营支付方式字典
// joint_pay_type  联营支付类型字典
// joint_bill_status 联营制单状态  字典
// joint_pay_mode 联营支付模式 字典

const {
    joint_charge_type,
    joint_pay_method,
    joint_pay_type,
    joint_bill_status,
    joint_signing_subject,
    joint_pay_mode,
} = useDict(
    'joint_charge_type',
    'joint_pay_method',
    'joint_pay_type',
    'joint_bill_status',
    'joint_signing_subject',
    'joint_pay_mode'
);

// 直接使用接口数据，无需转换

// 定义表格数据结构
const state: BasicTableProps = reactive<BasicTableProps>({
    queryForm: {
        storeName: '',
        courseType: '',
        orgId: undefined,
        status: '',
        paymentDate: '',
    },
    pageList: getJointBillList,
});

// table hook
const { getDataList, currentChangeHandle, sizeChangeHandle, tableStyle } = useTable(state);

// 清空搜索条件
const resetQuery = () => {
    queryRef.value?.resetFields();
    getDataList();
};

// 加载课程类型列表
const loadCourseTypeList = async () => {
    try {
        const { data } = await allCourseTypeApi();
        courseTypeList.value =
            data?.map((item: any) => ({
                value: item.id,
                label: item.name,
            })) || [];
    } catch (error) {
        console.error('获取课程类型列表失败:', error);
    }
};

// 重新推送
const handleRetry = async (row: unknown) => {
    const billRecord = row as JointBillData;
    try {
        await retryJointBill(billRecord.id || '');
        useMessage().success('推送成功');
        getDataList();
    } catch (error: any) {
        useMessage().error(error.message || '推送失败');
    }
};

// 获取课程名称
const getCourseName = (courseType: string) => {
    return courseTypeList.value.find((item) => item.value === courseType)?.label || '';
};

const getStoreSelectList = async (params: {}) => {
    const res = await getSchoolStoreList(params);
    return { data: res.data.records };
};

// 初始化
onMounted(() => {
    loadCourseTypeList();
    getDataList();
});
</script>
