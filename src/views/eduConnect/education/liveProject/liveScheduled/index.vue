<template>
    <div class="layout-padding">
        <div class="layout-padding-auto layout-padding-view">
            <navi-bar
                class="mb20"
                :title="$route.query.title as string"
                :sub-title="$route.query.author as string"
                :from="decodeURIComponent($route.query.from as string)"
            />
            <div
                class="px-6 py-6 box-shadow rounded-lg border border-gray-200"
                style="box-shadow: 0px 4px 10px 2px rgba(0, 0, 0, 0.1)"
            >
                <el-row class="mb-4">
                    <el-col :span="6">
                        <span>直播间计划名称：</span>
                        <span>
                            {{ planData?.planName || '' }}
                        </span>
                    </el-col>
                    <el-col :span="6">
                        <span>阶段：</span>
                        <span>
                            <dict-tag :options="grade" type="primary" :value="planData?.stage"></dict-tag>
                        </span>
                    </el-col>
                    <el-col :span="6">
                        <span>直播间：</span>
                        <span>
                            <dict-tag :options="live_room" type="primary" :value="planData?.liveRoomId"></dict-tag>
                        </span>
                    </el-col>
                    <el-col :span="6">
                        <span>直播数量：</span>
                        <span>{{ planData?.planCount || 0 }}</span>
                    </el-col>
                </el-row>
                <div class="flex justify-end">
                    <el-button
                        :disabled="!planData?.planDetails?.length"
                        type="primary"
                        icon="promotion"
                        @click="publishPlan"
                    >
                        发布
                    </el-button>
                </div>
            </div>
            <el-dropdown trigger="click">
                <el-button type="primary" v-auth="'edusystem_LiveRoomPlanDetailDraft_add'" class="my-4" plain>
                    新增
                    <el-icon class="el-icon--right">
                        <arrow-down />
                    </el-icon>
                </el-button>
                <template #dropdown>
                    <el-dropdown-menu>
                        <el-dropdown-item @click="formDialogRef.openDialog(0, planId)">单节课</el-dropdown-item>
                        <el-dropdown-item @click="formDialogRef.openDialog(1, planId)">常规课</el-dropdown-item>
                        <el-dropdown-item @click="formDialogRef.openDialog(2, planId)">精品课</el-dropdown-item>
                    </el-dropdown-menu>
                </template>
            </el-dropdown>

            <el-table
                :data="planDetails"
                v-loading="loading"
                border
                class="flex-1"
                :cell-style="tableStyle.cellStyle"
                :header-cell-style="tableStyle.headerCellStyle"
                @sort-change="sortChangeHandle"
            >
                <el-table-column type="index" label="序号" width="60" />
                <el-table-column prop="classDate" label="上课日期" show-overflow-tooltip />
                <el-table-column prop="classStartTime" label="星期/时段" show-overflow-tooltip>
                    <template #default="{ row }">
                        <dict-tag
                            :options="week_type"
                            type="primary"
                            :value="row.classWeek"
                            style="display: inline-block"
                        ></dict-tag>
                        &nbsp;&nbsp;&nbsp;{{ row.dateName }}
                    </template>
                </el-table-column>
                <el-table-column prop="operatorName" label="操作人" show-overflow-tooltip />
                <el-table-column prop="updateTime" label="操作时间" show-overflow-tooltip />
                <el-table-column label="操作" width="150">
                    <template #default="{ row }">
                        <el-button
                            icon="edit-pen"
                            text
                            type="primary"
                            v-auth="'edusystem_LiveRoomPlanDetailDraft_edit'"
                            @click="formDialogRef.openDialog(null, null, row)"
                            :disabled="isSchedulePassed(row.classDate, row.classStartTime)"
                            >编辑</el-button
                        >
                        <el-button
                            icon="delete"
                            text
                            type="primary"
                            v-auth="'edusystem_LiveRoomPlanDetailDraft_del'"
                            @click="deleteSchedule(row)"
                            :disabled="isSchedulePassed(row.classDate, row.classStartTime)"
                            >删除</el-button
                        >
                    </template>
                </el-table-column>
            </el-table>
        </div>

        <!-- 编辑、新增  -->
        <form-dialog ref="formDialogRef" @refresh="fetchPlanData()" />

        <el-dialog width="600px" v-model="showPublishSuccess" :show-close="false">
            <span style="font-size: 20px; color: #101010">发布成功</span>
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import { ElMessageBox } from 'element-plus';
import { useDict } from '/@/hooks/dict';
import { BasicTableProps, useTable } from '/@/hooks/table';
import { fetchList, delObjs, publish as publishLive } from '/@/api/eduConnect/liveProjectDetail';
import { useMessage, useMessageBox } from '/@/hooks/message';
import NaviBar from '/@/components/NaviBar/index.vue';
import { computed, ref, reactive, defineAsyncComponent } from 'vue';
import { useRoute, useRouter } from 'vue-router';

// 路由相关
const route = useRoute();
const router = useRouter();
const planId = parseInt(route.query.id as string);

// 状态定义
const loading = ref(false);
const planData = ref<Record<string, any> | null>(null);
const showPublishSuccess = ref(false);

// 引入表单对话框组件
const FormDialog = defineAsyncComponent(() => import('./form.vue'));
const formDialogRef = ref();

// 定义查询字典
const { grade, live_room, week_type } = useDict('grade', 'live_room', 'week_type');

// 直播计划明细列表
const planDetails = computed(() => planData.value?.planDetails || []);

// 表格配置
const tableState: BasicTableProps = reactive<BasicTableProps>({
    queryForm: {
        id: planId,
    },
    selectObjs: ['classDate', 'classStartTime'],
    isPage: false,
    pageList: fetchList,
});

/**
 * 检查课程是否已经过期（当前时间已超过课程时间）
 * 如果当天的课程，则允许在课程开始时间2小时前编辑/删除
 * @param dateString 课程日期
 * @param timeString 课程开始时间
 * @returns 是否已过期
 */
const isSchedulePassed = (dateString: string, timeString: string): boolean => {
    const now = new Date();
    const targetDateTime = new Date(`${dateString}T${timeString}`);

    // 检查是否为当天课程，如果是则加上2小时缓冲时间
    if (
        targetDateTime.getFullYear() === now.getFullYear() &&
        targetDateTime.getMonth() === now.getMonth() &&
        targetDateTime.getDate() === now.getDate()
    ) {
        targetDateTime.setHours(targetDateTime.getHours() + 2);
    }

    // 如果当前时间已超过目标时间（含缓冲时间），则课程已过期
    return now > targetDateTime;
};

/**
 * 发布直播间计划
 */
const publishPlan = async () => {
    // 发布确认
    try {
        await useMessageBox().confirm(`确定发布【${planData.value?.planName}】直播间计划？`, true);
    } catch {
        return;
    }

    // 执行发布
    try {
        await publishLive([planId]);
        useMessage().success('发布成功');
        router.push({
            path: '/eduConnect/education/liveProject/index',
        });
    } catch (err: any) {
        // 处理时间冲突情况
        if (err.errCode === '1504') {
            ElMessageBox.confirm(err.msg, '时间冲突提示', {
                confirmButtonText: '强制发布',
                cancelButtonText: '取消',
                type: 'warning',
            })
                .then(async () => {
                    try {
                        // 强制发布（第二个参数为true）
                        await publishLive([planId], true);
                        useMessage().success('发布成功');
                        router.push({
                            path: '/eduConnect/education/liveProject/index',
                        });
                    } catch (forceErr: any) {
                        useMessage().error(forceErr.msg);
                    }
                })
                .catch(() => {
                    // 用户取消强制发布，不做处理
                });
        } else {
            useMessage().error(err.msg);
        }
    }
};

/**
 * 删除课程排期
 * @param row 排期数据行
 */
const deleteSchedule = async (row: any) => {
    // 查找星期名称
    const weekItem = week_type.value.find((item: any) => item.value == row.classWeek);
    const weekLabel = weekItem ? weekItem.label : '';

    // 删除确认
    try {
        await useMessageBox().confirm(
            `删除后不可恢复,请谨慎操作! <br/> 确定删除${row.classDate} ${weekLabel}${row.dateName}?`,
            true
        );
    } catch {
        return;
    }

    // 执行删除
    try {
        await delObjs([row.id]);
        fetchPlanData();
        useMessage().success('删除成功');
    } catch (err: any) {
        useMessage().error(err.msg);
    }
};

/**
 * 获取直播计划数据
 */
const fetchPlanData = async () => {
    loading.value = true;
    try {
        const res = await fetchList(tableState.queryForm);
        planData.value = res.data;
    } catch (err: any) {
        useMessage().error(err.msg || '获取数据失败');
        planData.value = null;
    } finally {
        loading.value = false;
    }
};

// 使用表格相关钩子函数
const { sortChangeHandle, tableStyle } = useTable(tableState);

// 初始化获取数据
onMounted(() => {
    fetchPlanData();
});
</script>

<style lang="scss" scoped>
// .el-dropdown {
//     display: flex;
//     align-items: center;
//     justify-content: center;

//     :deep(.el-dropdown-link) {
//         cursor: pointer;
//         color: var(--el-color-primary);
//         display: flex;
//         align-items: center;
//     }
// }

:deep(.el-overlay .el-overlay-dialog .el-dialog .el-dialog__body) {
    text-align: center;
}

:deep(.el-dialog__header) {
    padding: 0;
}
</style>
