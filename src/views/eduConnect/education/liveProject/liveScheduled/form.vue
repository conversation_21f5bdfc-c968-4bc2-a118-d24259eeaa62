<template>
    <!-- .${pk.attrName} -->
    <el-dialog
        :title="
            titleCon == LessonType.singles
                ? '新增单节课'
                : titleCon == LessonType.common
                ? '新增常规课'
                : titleCon == LessonType.fine
                ? '新增精品课'
                : '编辑排期'
        "
        v-model="visible"
        :close-on-click-modal="false"
        draggable
        style="width: 500px"
    >
        <el-form
            ref="dataFormRef"
            :model="form"
            :rules="dataRules"
            formDialogRef
            label-width="90px"
            v-loading="loading"
            v-if="titleCon == LessonType.singles || edit == true"
        >
            <el-form-item :label="titleCon ? '选择日期' : '上课日期'" prop="classDate">
                <el-date-picker
                    v-model="form.classDate"
                    type="date"
                    placeholder="请选择上课日期"
                    value-format="YYYY-MM-DD"
                    style="width: 260px"
                >
                </el-date-picker>
            </el-form-item>

            <el-form-item label="上课时段:" prop="timeSlotId">
                <el-select v-model="form.timeSlotId" placeholder="请选择上课时段" style="width: 260px">
                    <el-option v-for="item in lessonTime" :key="item.id" :label="item.name" :value="item.id">
                    </el-option>
                </el-select>
            </el-form-item>
        </el-form>

        <el-form
            ref="dataFormRef"
            :model="form"
            :rules="dataRules"
            formDialogRef
            label-width="90px"
            v-loading="loading"
            class="fromSec"
            v-else-if="titleCon == LessonType.common"
        >
            <el-form-item label="开始日期:" prop="classDate">
                <el-date-picker
                    v-model="form.classDate"
                    type="date"
                    placeholder="请选择开始日期"
                    value-format="YYYY-MM-DD"
                    style="width: 260px"
                >
                </el-date-picker>
            </el-form-item>

            <el-form-item label="课次数量:" prop="lessonCount">
                <el-input-number
                    style="width: 260px"
                    v-model="form.lessonCount"
                    placeholder="请输入课次数量"
                    :min="1"
                    :controls-position="'right'"
                />
            </el-form-item>

            <el-form-item label="星期:" prop="week">
                <el-select v-model="form.week" placeholder="请选择星期" style="width: 260px">
                    <el-option v-for="item in weekOptions" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                </el-select>
            </el-form-item>

            <el-form-item label="上课时段:" prop="timeSlotId">
                <el-select v-model="form.timeSlotId" placeholder="请选择上课时段" style="width: 260px">
                    <el-option v-for="item in lessonTime" :key="item.id" :label="item.name" :value="item.id">
                    </el-option>
                </el-select>
            </el-form-item>
        </el-form>

        <el-form
            ref="dataFormRef"
            :model="form"
            :rules="dataRules"
            formDialogRef
            label-width="90px"
            v-loading="loading"
            v-else
        >
            <el-form-item :label="titleCon ? '选择日期' : '上课日期'" prop="classDate">
                <el-date-picker
                    v-model="form.classDate"
                    type="dates"
                    placeholder="请选择上课日期"
                    value-format="YYYY-MM-DD"
                    style="width: 260px"
                    @change="updateSelectedDatesCount"
                >
                </el-date-picker>
                <p v-if="selectedDatesCount" class="ml-2">
                    {{ selectedDatesCount }}
                </p>
            </el-form-item>

            <el-form-item label="上课时段:" prop="timeSlotId">
                <el-select v-model="form.timeSlotId" placeholder="请选择上课时段" style="width: 260px">
                    <el-option v-for="item in lessonTime" :key="item.id" :label="item.name" :value="item.id">
                    </el-option>
                </el-select>
            </el-form-item>
        </el-form>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="visible = false">取消</el-button>
                <el-button type="primary" @click="handleSubmit" :disabled="loadingSave">确认</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script setup lang="ts" name="LiveRoomScheduleDialog">
import { useDict } from '/@/hooks/dict';
import { addObj, putObj, getTime as getTimeApi } from '/@/api/eduConnect/liveProjectDetail';
import { LessonType } from '/@/components/EduComponents/enums/eduEnums';
import { useMessage } from '/@/hooks/message';
import { debounce } from 'lodash-es';

// 导入moment
import moment from 'moment';
import 'moment/locale/zh-cn';
// 设置语言为中文
moment.locale('zh-cn');

// 定义表单数据类型
interface FormData {
    id?: number;
    // 上课日期
    classDate?: string | string[];
    // 课次数量
    lessonCount?: number;
    // 星期
    week?: string;
    // 上课时段
    timeSlotId?: number | string;
    // 课程类型
    detailType?: number | string;
    // 计划ID
    planId?: number | string | null;
    // 添加索引签名允许任意属性操作
    [key: string]: any;
}

// 定义课程详情项类型
interface DetailItem {
    classDate: string;
    timeSlotId?: number | string;
    detailType?: number | string | null;
    planId?: number | string | null;
    id?: number;
    [key: string]: any; // 允许动态删除属性
}

// 定义时间段类型
interface TimeSlot {
    id: number | string;
    name: string;
    [key: string]: any;
}

// 路由和事件定义
const $route = useRoute();
const emit = defineEmits(['refresh']);

// 状态变量
const visible = ref(false);
const loading = ref(false); // 加载数据时的loading状态
const loadingSave = ref(false); // 保存数据时的loading状态
const edit = ref(false);
const titleCon = ref<number | null>(null);
const dataFormRef = ref();
const lessonTime = ref<TimeSlot[]>([]);
const selectedDatesCount = ref('');

// 定义字典
const { week_type } = useDict('week_type');

// 星期选择的下拉列表选项
const weekOptions = computed(() => {
    return week_type.value || [];
});

// 表单数据
const form = reactive<FormData>({});

// 组件初始化
onMounted(() => {
    loadTimeSlots();
});

/**
 * 加载可用的时间段
 */
const loadTimeSlots = async () => {
    loading.value = true;
    try {
        const res = await getTimeApi();
        lessonTime.value = res.data;
    } catch (error) {
        // 静默处理错误或使用其他方式记录
    } finally {
        loading.value = false;
    }
};

/**
 * 更新已选择的日期数量显示
 */
const updateSelectedDatesCount = () => {
    if (Array.isArray(form.classDate) && form.classDate.length > 0) {
        selectedDatesCount.value = '已选' + form.classDate.length + '天';
    } else {
        selectedDatesCount.value = '';
    }
};

/**
 * 表单字段验证
 */
const validateFormField = (rule: any, value: any, callback: any) => {
    if (value === '' || value == null || value == '0') {
        switch (rule.field) {
            case 'classDate':
                callback(new Error('上课日期不能为空'));
                break;
            case 'lessonCount':
                callback(new Error('课次数量不能为0或空'));
                break;
            case 'week':
                callback(new Error('星期不能为空'));
                break;
            case 'timeSlotId':
                callback(new Error('上课时段不能为空'));
                break;
        }
    } else {
        callback();
    }
};

/**
 * 表单验证规则
 */
const dataRules = ref({
    classDate: [{ required: true, validator: validateFormField, trigger: 'change' }],
    lessonCount: [{ required: true, validator: validateFormField, trigger: 'change' }],
    week: [{ required: true, validator: validateFormField, trigger: 'change' }],
    timeSlotId: [{ required: true, validator: validateFormField, trigger: 'change' }],
});

/**
 * 打开弹窗
 * @param type 课程类型 0: 单节课, 1: 常规课, 2: 精品课
 * @param searchId 计划ID
 * @param row 编辑时的行数据
 */
const openDialog = (type: number | null = null, searchId?: any, row?: any) => {
    // 重置表单数据
    nextTick(() => {
        edit.value = false;
        dataFormRef.value?.resetFields();
        // 清空表单
        Object.keys(form).forEach((key) => {
            delete form[key];
        });

        titleCon.value = type;
        if (row) {
            form.id = row.id;
            form.timeSlotId = row.timeSlotId;
            form.classDate = row.classDate;
            edit.value = true;
        }
        selectedDatesCount.value = '';
    });

    visible.value = true;
    if (searchId) {
        form.planId = searchId;
    }
    if (type != null) {
        titleCon.value = type;
    }
};

/**
 * 添加天数到指定日期
 * @param dateString 日期字符串 (YYYY-MM-DD)
 * @param days 要添加的天数
 * @returns 新的日期字符串 (YYYY-MM-DD)
 */
const addDays = (dateString: string, days: number): string => {
    return moment(dateString).add(days, 'days').format('YYYY-MM-DD');
};

/**
 * 根据给定日期和目标星期几，计算得到最近的目标星期几的日期
 * 注意：如果输入日期和目标星期相同，则返回输入日期本身
 * @param inputDate 起始日期（字符串格式：YYYY-MM-DD）
 * @param targetDayOfWeekFromDict 目标星期几（1-7，其中7表示周日）
 * @returns 目标日期（字符串格式：YYYY-MM-DD）
 */
const getDateByWeekday = (inputDate: string, targetDayOfWeekFromDict: number | string): string => {
    const inputMoment = moment(inputDate, 'YYYY-MM-DD');
    const inputIsoWeekday = inputMoment.isoWeekday();
    const dictValue = Number(targetDayOfWeekFromDict);

    if (isNaN(dictValue) || dictValue < 1 || dictValue > 7) {
        console.error(
            'Invalid targetDayOfWeekFromDict provided to getDateByWeekday, expected 1-7 (Mon-Sun): ',
            targetDayOfWeekFromDict
        );
        return inputDate;
    }

    const targetIsoWeekday = dictValue;
    let resultMoment: moment.Moment;

    if (inputIsoWeekday === targetIsoWeekday) {
        resultMoment = inputMoment.clone();
    } else {
        let daysDiff = targetIsoWeekday - inputIsoWeekday;
        if (daysDiff < 0) {
            daysDiff += 7;
        }
        resultMoment = inputMoment.clone().add(daysDiff, 'days');
    }

    return resultMoment.format('YYYY-MM-DD');
};

/**
 * 处理单节课数据提交
 */
const handleSingleLessonSubmit = (detailList: DetailItem[]) => {
    detailList.push({
        classDate: form.classDate as string,
        timeSlotId: form.timeSlotId,
        detailType: titleCon.value,
        planId: form.planId,
    });
};

/**
 * 处理常规课数据提交
 */
const handleRegularLessonSubmit = (detailList: DetailItem[]) => {
    if (!form.lessonCount || !form.week) return;

    // 保存原始开始日期
    const originalDate = form.classDate as string;

    // 确保week字段是数字类型
    const weekNum = Number(form.week);

    // 获取第一个课程日期（确保是正确的星期几）
    let firstClassDate = getDateByWeekday(originalDate, weekNum);

    // 生成所有课程日期
    const classDates = [];
    let currentDate = firstClassDate;

    for (let index = 0; index < form.lessonCount; index++) {
        classDates.push(currentDate);

        // 计算下一周的日期
        currentDate = addDays(currentDate, 7);
    }

    // 将生成的日期添加到详情列表
    classDates.forEach((classDate) => {
        detailList.push({
            classDate: classDate,
            timeSlotId: form.timeSlotId,
            detailType: titleCon.value,
            planId: form.planId,
        });
    });
};

/**
 * 处理精品课数据提交
 */
const handlePremiumLessonSubmit = (detailList: DetailItem[]) => {
    const classDates = form.classDate as string[];

    if (!Array.isArray(classDates) || classDates.length === 0) {
        return;
    }

    for (const date of classDates) {
        detailList.push({
            classDate: date,
            timeSlotId: form.timeSlotId,
            detailType: titleCon.value,
            planId: form.planId,
        });
    }
};

/**
 * 表单提交处理
 */
const handleSubmit = debounce(async () => {
    try {
        // 表单验证
        const valid = await dataFormRef.value.validate().catch(() => false);
        if (!valid) return;

        // 准备提交数据
        const detailLists: { detailList: DetailItem[] } = {
            detailList: [],
        };

        // 根据课程类型处理数据
        if (form.lessonCount) {
            // 常规课程处理
            handleRegularLessonSubmit(detailLists.detailList);
        } else if (titleCon.value === LessonType.fine) {
            // 精品课程处理
            handlePremiumLessonSubmit(detailLists.detailList);
        } else {
            // 单节课处理
            handleSingleLessonSubmit(detailLists.detailList);
        }

        // 提交处理
        loadingSave.value = true;

        // 设置计划ID
        const planId = parseInt($route.query.id as string);
        detailLists.detailList.forEach((item) => {
            item.planId = planId;
        });

        // 处理编辑模式
        if (form.id) {
            detailLists.detailList.forEach((item) => {
                item.id = form.id;
                delete item['detailType'];
                delete item['planId'];
            });
        }

        // API调用
        if (form.id) {
            await putObj(detailLists.detailList[0]);
        } else {
            await addObj(detailLists);
        }

        // 成功处理
        useMessage().success(form.id ? '修改成功' : '添加成功');
        visible.value = false;
        emit('refresh');
    } catch (err: any) {
        useMessage().error(err.msg || '提交失败');
    } finally {
        loadingSave.value = false;
    }
}, 500);

// 暴露变量和方法
defineExpose({
    openDialog,
});
</script>

<style lang="scss" scoped></style>
