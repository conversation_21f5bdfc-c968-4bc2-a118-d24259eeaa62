<template>
    <div class="layout-padding">
        <div class="layout-padding-auto layout-padding-view" v-if="isMask">
            <el-row class="ml10" v-show="showSearch">
                <el-form :inline="true" :model="state.queryForm" ref="queryRef">
                    <!-- 查询直播间字段 -->
                    <el-form-item label="直播间计划名称" prop="planName">
                        <el-input
                            v-model="state.queryForm.planName"
                            placeholder="请输入直播间计划名称"
                            clearable
                            style="width: 200px"
                        />
                    </el-form-item>
                    <el-form-item label="直播间" prop="liveRoomId">
                        <el-select
                            v-model="state.queryForm.liveRoomId"
                            placeholder="请选择直播间"
                            clearable
                            filterable
                            @clear="resetQuery"
                            style="width: 200px"
                        >
                            <el-option
                                v-for="item in reverseLivingRoomDict"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            />
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-button @click="getDataList" formDialogRef icon="search" type="primary">
                            {{ $t('common.queryBtn') }}
                        </el-button>
                        <el-button @click="resetQuery" icon="Refresh">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-row>
            <el-row>
                <div class="mb8" style="width: 100%">
                    <el-button
                        @click="formDialogRef.openDialog()"
                        type="primary"
                        icon="folder-add"
                        v-auth="'edusystem_LiveRoomPlanDraft_add'"
                    >
                        新增直播间计划
                    </el-button>
                    <right-toolbar
                        class="ml10 mr20"
                        v-model:showSearch="showSearch"
                        :export="false"
                        style="float: right"
                        @queryTable="getDataList"
                    />
                </div>
            </el-row>
            <el-table
                :data="state.dataList"
                v-loading="state.loading"
                border
                :cell-style="tableStyle.cellStyle"
                :header-cell-style="tableStyle.headerCellStyle"
                @sort-change="sortChangeHandle"
            >
                <el-table-column type="index" label="序号" width="60" />
                <el-table-column prop="planName" label="直播间计划名称" show-overflow-tooltip min-width="150" />
                <el-table-column prop="stage" label="阶段" min-width="80">
                    <template #default="{ row }">
                        <el-tag type="primary">{{ getStageName(row.stage) }}</el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="liveRoomId" label="直播间" min-width="120">
                    <template #default="{ row }">
                        <dict-tag
                            :options="live_room"
                            :type="setTagType(row.liveRoomId, { '0': 'primary', '1': 'warn' })"
                            :value="row.liveRoomId"
                        ></dict-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="planCount" label="直播数量" show-overflow-tooltip min-width="90" />
                <el-table-column prop="haveTeaching" label="关联教学计划" min-width="110">
                    <template #default="{ row }">
                        <el-link
                            type="primary"
                            :underline="false"
                            @click="linkToTeach(row.teachingId)"
                            :disabled="row.haveTeaching == 0"
                        >
                            {{ row.haveTeaching ? '已关联' : '未关联' }}
                        </el-link>
                    </template>
                </el-table-column>
                <!-- :0-未发布;1-已发布 -->
                <el-table-column prop="planStatus" label="发布状态" min-width="100">
                    <template #default="{ row }">
                        <dict-tag
                            :options="publish_status"
                            :type="setTagType(row.planStatus, { '0': 'info', '1': 'success' })"
                            :value="row.planStatus"
                        ></dict-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="publish" label="发布人" min-width="100">
                    <template #default="{ row }">
                        <span v-if="row.publish">{{ row.publish }}</span>
                        <span v-else>/</span>
                    </template>
                </el-table-column>
                <el-table-column prop="publishTime" label="发布时间" min-width="170">
                    <template #default="{ row }">
                        <span v-if="row.publishTime">{{ row.publishTime }}</span>
                        <span v-else>/</span>
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="200" fixed="right">
                    <template #default="{ row }">
                        <!-- <el-dropdown>
                            <span class="el-dropdown-link">
                                更多
                            </span>
                            <template #dropdown>
                                <el-dropdown-menu>
                                    <el-dropdown-item @click="linkToPai(row.id)"
                                        v-auth="'edusystem_LiveRoomPlanDraft_edit'">详情</el-dropdown-item>
                                    <el-dropdown-item @click="formDialogRef.openDialog(row)"
                                        v-auth="'edusystem_LiveRoomPlanDraft_edit'">编辑</el-dropdown-item>
                                    <el-dropdown-item @click="deleteSech(row)"
                                        v-auth="'edusystem_LiveRoomPlanDraft_del'">删除</el-dropdown-item>
                                </el-dropdown-menu>
                            </template>
    </el-dropdown> -->

                        <el-button
                            icon="view"
                            text
                            type="primary"
                            v-auth="'edusystem_LiveRoomPlanDraft_edit'"
                            @click="linkToPai(row.id)"
                            >详情</el-button
                        >
                        <el-button
                            icon="edit-pen"
                            text
                            type="primary"
                            v-auth="'edusystem_LiveRoomPlanDraft_edit'"
                            @click="formDialogRef.openDialog(row)"
                            :disabled="row.planEnd == 1"
                            >编辑</el-button
                        >
                        <el-button
                            icon="edit-pen"
                            text
                            type="primary"
                            v-auth="'edusystem_LiveRoomPlanDraft_del'"
                            @click="deleteSech(row)"
                            :disabled="row.haveTeaching == 1 || row.planEnd == 1"
                            >删除</el-button
                        >
                    </template>
                </el-table-column>
            </el-table>
            <pagination
                @size-change="sizeChangeHandle"
                @current-change="currentChangeHandle"
                v-bind="state.pagination"
            />
        </div>
        <form-dialog ref="formDialogRef" @refresh="getDataList(true)" :stageList="stageList"></form-dialog>
        <!-- <el-dialog width="600px" v-model="sucessDel" :show-close="false">
            <span style="font-size: 20px;color: #101010;">删除成功</span>
        </el-dialog> -->
    </div>
</template>

<script setup lang="ts" name="systemLiveRoomSchedule">
import { BasicTableProps, useTable } from '/@/hooks/table';
import { fetchList, delObjs } from '/@/api/eduConnect/liveProject';
import { useMessage, useMessageBox } from '/@/hooks/message';
import { useDict } from '/@/hooks/dict';
import { useStageList, useStageDisplay } from '/@/hooks/stage';

const router = useRouter();
const isMask = ref(true);
const $router = useRouter();
const $route = useRoute();
// 定义查询字典
let { publish_status, live_room } = useDict('publish_status', 'live_room');
// 引入组件
const FormDialog = defineAsyncComponent(() => import('./form.vue'));
let arr: any = ref([]);
const reverseLivingRoomDict = computed(() => {
    if (live_room.value) {
        live_room.value.forEach((element: any) => {
            arr.value.unshift(element);
        });
    }
    return arr.value;
});

const { stages: stageList } = useStageList();
const { getStageName } = useStageDisplay();

// 定义变量内容
const formDialogRef = ref();
// const sucessDel = ref(false);
// 搜索变量
const queryRef = ref();
const showSearch = ref(true);
// 多选变量
// const selectObjs = ref([]) as any;
// const multiple = ref(true);

const state: BasicTableProps = reactive<BasicTableProps>({
    queryForm: {
        planName: '',
        liveRoomId: '',
    },
    // descs: ['publishTime'],
    // ascs: ['desc'],
    pageList: fetchList,
});
const setTagType = (key: any, obj: any) => {
    return obj[key];
};

//  table hook
const { getDataList, currentChangeHandle, sizeChangeHandle, sortChangeHandle, tableStyle } = useTable(state);
// 删除
const deleteSech = async (row: any) => {
    try {
        await useMessageBox().confirm(`删除后不可恢复,请谨慎操作! <br/> 确定删除【${row.planName}】直播间计划?`, true);
    } catch {
        return;
    }

    try {
        await delObjs([row.id]);
        getDataList();
        useMessage().success('删除成功');

        // 提示信息打开和关闭
        // sucessDel.value = true
        // setTimeout(() => {
        //     sucessDel.value = false
        // }, 3000)
    } catch (err: any) {
        useMessage().error(err.msg);
    }
};
//linkToPai
const linkToPai = (id: number) => {
    $router.push({
        path: '/eduConnect/education/liveProject/liveScheduled/index',
        query: {
            id: id,
            from: encodeURIComponent(router.currentRoute.value.fullPath),
        },
    });
    isMask.value = false;
};
// linkToTeach
const linkToTeach = (id: number) => {
    $router.push({
        // path: '/eduConnect/teach/teachPlan/detail',
        path: '/eduConnect/education/liveProject/teachPlanDetail',
        query: {
            id: id,
            from: encodeURIComponent($route.fullPath),
        },
    });
};

// 清空搜索条件
const resetQuery = () => {
    // 清空搜索条件
    queryRef.value?.resetFields();
    // 清空多选
    // selectObjs.value = []
    getDataList();
};
</script>

<style scoped lang="scss">
.el-dropdown {
    display: flex;
    align-items: center;
    justify-content: center;

    :deep(.el-dropdown-link) {
        cursor: pointer;
        color: var(--el-color-primary);
        display: flex;
        align-items: center;
    }
}

:deep(.el-overlay .el-overlay-dialog .el-dialog .el-dialog__body) {
    text-align: center;
}

:deep(.el-dialog__header) {
    padding: 0;
}
</style>
