<template>
    <el-dialog
        :title="dialogTitle"
        v-model="visible"
        :close-on-click-modal="false"
        width="600px"
        @closed="handleDialogClosed"
    >
        <el-form
            ref="dataFormRef"
            :model="form"
            :rules="dataRules"
            formDialogRef
            label-width="90px"
            v-loading="loading"
        >
            <el-row :gutter="24">
                <el-col :span="24" class="mb20">
                    <el-form-item label="名称:" prop="planName">
                        <el-input v-model="form.planName" placeholder="请输入直播间计划名称" class="w-60" />
                        <p class="ml-2 text-red-600 text-xs">名称命名建议：阶段+星期+时段</p>
                    </el-form-item>
                </el-col>

                <el-col :span="24" class="mb20">
                    <el-form-item label="阶段:" prop="stage">
                        <el-select v-model="form.stage" placeholder="请选择阶段" style="width: 240px">
                            <el-option
                                v-for="item in props.stageList"
                                :key="item.id"
                                :label="item.stageName"
                                :value="item.id"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                </el-col>

                <el-col :span="24" class="mb20">
                    <el-form-item label="直播间:" prop="liveRoomId">
                        <el-select v-model="form.liveRoomId" placeholder="请选择直播间" style="width: 240px" filterable>
                            <el-option
                                v-for="item in reverseLivingRoomDict"
                                :key="item.id"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="visible = false">取消</el-button>
                <el-button type="primary" @click="onSubmit" :disabled="loading">确认</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script setup lang="ts" name="LiveRoomScheduleDialog">
import { ref, reactive, computed, nextTick } from 'vue';
import { useDict } from '/@/hooks/dict';
import { useMessage } from '/@/hooks/message';
import { addObj, putObj } from '/@/api/eduConnect/liveProject';
import type { StageItem } from '/@/types/common';
interface FormType {
    id?: number | undefined;
    planName: string;
    planId?: any;
    stage: number | string;
    liveRoomId: number | string;
}
const props = defineProps({
    stageList: {
        type: Array<StageItem>,
        default: () => [],
    },
});

// 定义字典
const { live_room } = useDict('live_room');
const emit = defineEmits(['refresh']);

// 定义变量内容
const dataFormRef = ref();
const visible = ref(false);
const loading = ref(false);

// 初始表单数据
const initialForm = (): FormType => ({
    id: undefined,
    planName: '',
    stage: '',
    liveRoomId: '',
});

// 表单数据
const form = reactive<FormType>(initialForm());

// 优化后的computed属性，避免在computed中修改数组
const reverseLivingRoomDict = computed(() => {
    return live_room.value ? [...live_room.value].reverse() : [];
});

// 计算对话框标题
const dialogTitle = computed(() => {
    return form.id ? '编辑直播间计划' : '新增直播间计划';
});

// 通用验证器生成函数
const createValidator = (fieldName: string, maxLength?: number) => {
    return (rule: any, value: any, callback: any) => {
        if (value === '' || value == null) {
            callback(new Error(`${fieldName}不能为空`));
        } else if (maxLength && value.length > maxLength) {
            callback(new Error(`${fieldName}不能大于${maxLength}字`));
        } else {
            callback();
        }
    };
};

// 定义校验规则
const dataRules = {
    planName: [{ required: true, validator: createValidator('直播间计划名称', 50), trigger: 'change' }],
    stage: [{ required: true, validator: createValidator('阶段'), trigger: 'change' }],
    liveRoomId: [{ required: true, validator: createValidator('直播间'), trigger: 'change' }],
};

// 打开弹窗
const openDialog = (row: any) => {
    visible.value = true;
    // 重置表单数据
    Object.assign(form, initialForm());
    if (form.id !== undefined) {
        delete form.id;
    }
    nextTick(() => {
        dataFormRef.value?.resetFields();
        if (row) {
            const { id, planName, stage, liveRoomId } = row;
            Object.assign(form, { id, planName, stage, liveRoomId });
        }
    });
};

// 处理弹窗关闭
const handleDialogClosed = () => {
    // 重置表单数据
    Object.assign(form, initialForm());
    if (form.id !== undefined) {
        delete form.id;
    }
    dataFormRef.value?.resetFields();
};

// 提交
const onSubmit = async () => {
    try {
        await dataFormRef.value.validate();

        loading.value = true;

        if (form.id) {
            form.id = Number(form.id);
            await putObj(form);
            useMessage().success('修改成功');
        } else {
            await addObj(form);
            useMessage().success('添加成功');
        }

        visible.value = false;
        emit('refresh');
    } catch (err: any) {
        if (err.msg) {
            useMessage().error(err.msg);
        }
    } finally {
        loading.value = false;
    }
};

// 暴露变量
defineExpose({
    openDialog,
});
</script>

<style lang="scss" scoped>
.el-col {
    margin-top: 20px;
    margin-bottom: 0px;
}
</style>
