<template>
    <div class="layout-padding">
        <div class="layout-padding-auto layout-padding-view">
            <el-table
                :data="state.dataList"
                border
                :cell-style="tableStyle.cellStyle"
                :header-cell-style="tableStyle.headerCellStyle"
            >
                <el-table-column prop="number" label="序号" />
                <el-table-column prop="time" label="课节名称" />
                <el-table-column prop="time" label="关联书籍" />
                <el-table-column prop="time" label="上课时段" />

                <el-table-column prop="correspondingTime" label="主讲老师" />
            </el-table>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { BasicTableProps, useTable } from '/@/hooks/table';

import { fetchList } from '/@/api/eduConnect/liveProject';

const $route = useRoute();

const state: BasicTableProps = reactive<BasicTableProps>({
    queryForm: {
        id: $route.query.id,
    },
    pageList: fetchList,
});

const { tableStyle } = useTable(state);
</script>
