<template>
    <div class="layout-padding">
        <div class="layout-padding-auto layout-padding-view">
            <navi-bar
                ref="naviBarRef"
                class="mb28"
                :title="`直播间计划名称: ${teachPlanData.planName || '-'}`"
                :sub-title="`课程名称: ${teachPlanData.courseName || '-'}`"
                :from="decodeURIComponent(route.query.from as string)"
            >
            </navi-bar>
            <el-table
                :data="teachPlanData.teachDetails || []"
                v-loading="state.loading"
                border
                :cell-style="tableStyle.cellStyle"
                :header-cell-style="tableStyle.headerCellStyle"
            >
                <el-table-column type="index" label="序号" width="60" />
                <el-table-column prop="lessonName" label="课节名称" show-overflow-tooltip>
                    <template #default="scope">{{ scope.row.lessonName || '/' }} </template>
                </el-table-column>
                <el-table-column prop="bookName" label="关联书籍" show-overflow-tooltip>
                    <template #default="scope">{{ scope.row.bookName || '/' }} </template>
                </el-table-column>
                <el-table-column prop="courseName" label="上课时段" show-overflow-tooltip>
                    <template #default="scope">
                        <p v-if="scope.row.slotId">
                            {{
                                scope.row.classDate +
                                ' ' +
                                week_type.find((item: any) => item.value == scope.row.classWeek)?.label +
                                ' ' +
                                classTimeArray.find((item: any) => item.id == scope.row.slotId)?.name
                            }}
                        </p>
                        <p v-else>/</p>
                    </template>
                </el-table-column>
                <el-table-column prop="lectureId" label="主讲老师" show-overflow-tooltip>
                    <template #default="scope">
                        <p v-if="scope.row.lectureId">{{ getTeacherName(scope.row.lectureId) }}</p>
                        <p v-else>/</p>
                    </template>
                </el-table-column>
            </el-table>
        </div>
    </div>
</template>

<script setup lang="ts">
import NaviBar from '/@/components/NaviBar/index.vue';
import { BasicTableProps, useTable } from '/@/hooks/table';
import { useMessage } from '/@/hooks/message';
import { fetchTeachPlanInfo } from '/@/api/eduConnect/teachPlan';
import { fetchAllTeacher } from '/@/api/eduConnect/teacher';
import { fetchList } from '/@/api/eduConnect/timePeriod';
import { useDict } from '/@/hooks/dict';

const route = useRoute();
const teachPlanData = reactive<any>({});
const teacherArray = ref<any[]>([]);
const classTimeArray = ref<any[]>([]);
const state: BasicTableProps = reactive<BasicTableProps>({
    isPage: false,
    pagination: {},
});
const { tableStyle } = useTable(state);
const naviBarRef = ref();
const { week_type } = useDict('week_type');

onMounted(() => {
    loadData();
    fetchAllTeacher()
        .then((res) => {
            teacherArray.value = res.data || [];
        })
        .catch((err) => {
            useMessage().error(err.msg);
        });
    fetchList()
        .then((res) => {
            classTimeArray.value = res.data || [];
        })
        .catch((err) => {
            useMessage().error(err.msg);
        });
});

const getTeacherName = (id: number) => {
    const teacher = teacherArray.value.find((item: any) => item.userId == id);
    if (teacher) {
        return teacher.name + (teacher.nickname ? `(${teacher.nickname})` : '');
    } else {
        return '/';
    }
};

const loadData = () => {
    state.loading = true;
    fetchTeachPlanInfo(route.query.id as string)
        .then((res) => {
            Object.assign(teachPlanData, res.data || {});
        })
        .catch((err) => {
            useMessage().error(err.msg);
        })
        .finally(() => {
            state.loading = false;
        });
};
</script>

<style lang="scss" scoped></style>
