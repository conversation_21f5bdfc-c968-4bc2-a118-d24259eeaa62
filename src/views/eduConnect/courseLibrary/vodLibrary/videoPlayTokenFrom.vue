<template>
    <el-dialog title="创建视频播放凭证" v-model="visible" width="500">
        <el-form
            ref="dataFormRef"
            :model="form"
            :rules="dataRules"
            formDialogRef
            label-width="110px"
            v-loading="loading"
        >
            <el-row :gutter="24">
                <el-col :span="24" class="mb20">
                    <el-form-item label="授权校区" prop="campusId">
                        <fuzzy-search
                            ref="fuzzySearchRef"
                            class="!w-full"
                            v-model="form.campusId"
                            placeholder="输入搜索授权校区"
                            :requestUrl="getCampusList"
                            request-name="query"
                            data-label="campusName"
                            clearable
                            @change="(e) => (form.campusName = e.campusName)"
                        />
                    </el-form-item>
                </el-col>
                <el-col :span="24" class="mb20">
                    <el-form-item label="视频过期时间" prop="tokenExpire">
                        <el-date-picker
                            class="!w-full"
                            v-model="form.tokenExpire"
                            type="date"
                            format="YYYY/MM/DD"
                            value-format="YYYY-MM-DD"
                            placeholder="请选择"
                            :disabled-date="(time: any) => time.getTime() < Date.now() - 8.64e7"
                        />
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="visible = false">取消</el-button>
                <el-button type="primary" :disabled="loading" @click="onSubmit">确认</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
import { getVideoPlayToken } from '/@/api/eduConnect/ssRecording';
import { getCampusList } from '/@/api/eduConnect/ssQuery';
import { useMessage } from '/@/hooks/message';
import fuzzySearch from '/@/components/EduComponents/fuzzySearch.vue';
import commonFunction from '/@/utils/commonFunction';

const visible = ref(false);
const dataFormRef = ref();
const fuzzySearchRef = ref();
const loading = ref(false);
const form = reactive({
    videoId: '',
    playUrl: '',
    tokenExpire: '',
    campusId: '336',
    campusName: '约读书房cs',
});
const dataRules = ref({
    tokenExpire: [{ required: true, message: '视频过期时间不能为空', trigger: 'blur' }],
    campusId: [{ required: true, message: '授权校区不能为空', trigger: 'blur' }],
});
const { copyText } = commonFunction();

// 打开弹窗
const openDialog = (videoId: string, playUrl: string) => {
    visible.value = true;

    // 等待DOM更新后设置默认值
    nextTick(() => {
        // 重置表单验证状态
        dataFormRef.value?.resetFields();

        form.videoId = videoId;
        form.playUrl = playUrl;
        form.campusId = '336';
        form.campusName = '约读书房cs';

        // 手动触发回显
        if (fuzzySearchRef.value && fuzzySearchRef.value.remoteMethod) {
            fuzzySearchRef.value.remoteMethod('约读书房cs');
        }
    });
};

// 提交
const onSubmit = async () => {
    const valid = await dataFormRef.value.validate().catch(() => {});
    if (!valid) return false;

    try {
        loading.value = true;
        // 如果campusId是对象，则提取id属性
        let finalCampusId = form.campusId;
        if (typeof finalCampusId === 'object' && finalCampusId !== null) {
            finalCampusId = (finalCampusId as any).id || '';
        }

        const res = await getVideoPlayToken({
            videoId: form.videoId,
            playUrl: form.playUrl,
            tokenExpire: form.tokenExpire + ' 23:59:59',
            campusId: finalCampusId,
            campusName: form.campusName,
        });

        await copyText(`${import.meta.env.VITE_VIDEO_PLAY_URL}?token=${res.msg}`);
        visible.value = false;
    } catch (err: any) {
        useMessage().error(err.msg);
    } finally {
        loading.value = false;
    }
};

// 暴露变量
defineExpose({
    open: openDialog,
});
</script>

<style lang="scss" scoped></style>
