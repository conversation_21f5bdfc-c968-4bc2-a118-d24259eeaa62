<template>
    <el-dialog v-model="dialogVisible" title="上传视频" width="600px" :before-close="handleClose" destroy-on-close>
        <div class="upload-container">
            <div class="upload-area" @click="handleFileClick" @dragover.prevent @drop.prevent="handleFileDrop">
                <div class="upload-icon">
                    <el-icon :size="48" color="#67C23A"><upload-filled /></el-icon>
                </div>
                <div class="upload-text">
                    单击或拖动文件到此区域以上传
                    <div class="upload-hint">格式MP4、AVI、MOV</div>
                </div>
                <input
                    ref="fileInputRef"
                    type="file"
                    accept="video/mp4,video/avi,video/quicktime"
                    style="display: none"
                    @change="handleFileChange"
                />
            </div>

            <div v-if="uploadInfo.file" class="file-info">
                <div class="file-name">{{ uploadInfo.file.name }}</div>
                <div class="file-size">{{ formatFileSize(uploadInfo.file.size) }}</div>
            </div>

            <div v-if="uploadInfo.status === 'uploading'" class="upload-progress">
                <el-progress
                    :percentage="uploadInfo.progress"
                    :status="uploadInfo.progress === 100 ? 'success' : ''"
                    :stroke-width="10"
                />
                <div class="progress-text">{{ uploadInfo.progressText }}</div>
            </div>

            <div v-if="uploadInfo.status === 'error'" class="upload-error">
                <el-alert title="上传失败" type="error" :description="uploadInfo.errorMessage" show-icon />
            </div>
        </div>

        <template #footer>
            <span class="dialog-footer">
                <el-button @click="handleClose">取消</el-button>
                <el-button
                    type="primary"
                    @click="handleUpload"
                    :disabled="!uploadInfo.file || uploadInfo.status === 'uploading'"
                    :loading="uploadInfo.status === 'uploading'"
                >
                    确定
                </el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script setup lang="ts" name="UploadVideoDialog">
import { ref, reactive, defineExpose, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { useMessage } from '/@/hooks/message';
import { UploadFilled } from '@element-plus/icons-vue';
import { getUploadAuth, refreshUploadAuth } from '/@/api/eduConnect/ssRecording';
import { useUserInfo } from '/@/stores/userInfo';

const stores = useUserInfo();
// 声明AliyunUpload类型，避免TypeScript错误
declare global {
    interface Window {
        writer: any;
        AliyunUpload: any;
    }

    namespace AliyunUpload {
        class Vod {
            constructor(options: any);
            setUploadAuthAndAddress(uploadInfo: any, uploadAuth: string, uploadAddress: string, videoId: string): void;
            addFile(file: File, startTime?: any, userData?: any, cateId?: any, metadata?: any): void;
            startUpload(): void;
            resumeUploadWithAuth(uploadAuth: string): void;
        }
    }
}

const { userInfos } = storeToRefs(stores);

// 加载SDK
onMounted(() => {
    if (window.AliyunUpload) {
        uploadInfo.sdkLoaded = true;
        return;
    }

    const script = document.createElement('script');
    script.src = '/lib/aliyun-upload-sdk-1.5.6/aliyun-upload-sdk-1.5.7.min.js';
    script.async = true;
    script.onload = () => {
        uploadInfo.sdkLoaded = true;
    };
    script.onerror = () => {
        useMessage().error('SDK加载失败，请刷新页面重试');
    };
    document.body.appendChild(script);
});

// 对外暴露的事件
const emit = defineEmits(['refresh', 'success']);

// 是否显示对话框
const dialogVisible = ref(false);

// 文件输入引用
const fileInputRef = ref<HTMLInputElement>();

// 上传信息
const uploadInfo = reactive({
    file: null as File | null,
    status: 'idle', // idle, uploading, success, error
    progress: 0,
    progressText: '',
    errorMessage: '',
    videoId: '',
    uploadAuth: '',
    uploadAddress: '',
    fileName: '',
    sdkLoaded: false,
});

// 格式化文件大小
const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 处理文件点击
const handleFileClick = () => {
    fileInputRef.value?.click();
};

// 处理文件拖放
const handleFileDrop = (e: DragEvent) => {
    e.preventDefault();
    const files = e.dataTransfer?.files;
    if (files && files.length > 0) {
        handleFiles(files[0]);
    }
};

// 处理文件变更
const handleFileChange = (e: Event) => {
    const files = (e.target as HTMLInputElement).files;
    if (files && files.length > 0) {
        handleFiles(files[0]);
    }
};

// 处理文件
const handleFiles = (file: File) => {
    // 检查文件类型
    const acceptedTypes = ['video/mp4', 'video/avi', 'video/quicktime'];
    if (!acceptedTypes.includes(file.type)) {
        ElMessage.error('请上传MP4、AVI或MOV格式的视频文件');
        return;
    }

    // 检查文件大小 (2048MB = 2GB)
    // const maxSize = 2048 * 1024 * 1024;
    // if (file.size > maxSize) {
    //     ElMessage.error('文件大小不能超过2048MB');
    //     return;
    // }

    // 设置文件
    uploadInfo.file = file;
    uploadInfo.status = 'idle';
    uploadInfo.progress = 0;
    uploadInfo.progressText = '';
    uploadInfo.errorMessage = '';
};

// 获取上传凭证
const getUploadAuthInfo = async () => {
    try {
        if (!uploadInfo.file) {
            return false;
        }

        const fileName = uploadInfo.file.name;

        const res = await getUploadAuth({
            title: fileName,
            fileName,
        });

        if (res.code === 0 && res.data) {
            uploadInfo.uploadAuth = res.data.uploadAuth;
            uploadInfo.uploadAddress = res.data.uploadAddress;
            uploadInfo.videoId = res.data.videoId;
            uploadInfo.fileName = fileName;
            return true;
        } else {
            uploadInfo.errorMessage = res.msg || '获取上传凭证失败';
            uploadInfo.status = 'error';
            return false;
        }
    } catch (error: any) {
        uploadInfo.errorMessage = error.message || '获取上传凭证失败';
        uploadInfo.status = 'error';
        return false;
    }
};

// 处理上传
const handleUpload = async () => {
    const userId = userInfos.value.user.userId;
    if (!uploadInfo.file) {
        ElMessage.warning('请先选择要上传的视频文件');
        return;
    }

    if (!uploadInfo.sdkLoaded || !window.AliyunUpload) {
        ElMessage.warning('SDK加载中，请稍后再试');
        return;
    }

    uploadInfo.status = 'uploading';
    uploadInfo.progress = 0;
    uploadInfo.progressText = '准备上传...';

    // 获取上传凭证
    const authSuccess = await getUploadAuthInfo();
    if (!authSuccess) {
        return;
    }

    try {
        // 初始化阿里云上传SDK
        const uploader = new AliyunUpload.Vod({
            // 阿里账号ID，必须
            userId: userId,
            // 分片大小默认1M，不能小于100K
            partSize: 1 * 1024 * 1024,
            // 并行上传分片个数，默认5
            parallel: 5,
            // 网络原因失败时，重新上传次数，默认为3
            retryCount: 3,
            // 网络原因失败时，重新上传间隔时间，默认为2秒
            retryDuration: 2,
            // 上传到点播的地域，默认为'cn-shanghai'
            region: '',
            // 开始上传
            onUploadstarted: function (sdkUploadInfo: Record<string, any>) {
                if (sdkUploadInfo.videoId) {
                    // 续传：刷新上传凭证
                    refreshUploadAuth({ videoId: sdkUploadInfo.videoId }).then((res) => {
                        if (res.code === 0 && res.data) {
                            uploader.setUploadAuthAndAddress(
                                sdkUploadInfo,
                                res.data.uploadAuth,
                                res.data.uploadAddress,
                                res.data.videoId
                            );
                        }
                    });
                } else {
                    // 新建上传：设置上传凭证
                    uploader.setUploadAuthAndAddress(
                        sdkUploadInfo,
                        uploadInfo.uploadAuth,
                        uploadInfo.uploadAddress,
                        uploadInfo.videoId
                    );
                }
            },

            // 文件上传成功
            onUploadSucceed: function () {
                // 更新组件的响应式状态，而不是参数
                uploadInfo.progress = 100;
                uploadInfo.progressText = '上传成功';
                uploadInfo.status = 'success';

                useMessage().success('视频上传成功');
                emit('success', {
                    videoId: uploadInfo.videoId,
                    fileName: uploadInfo.fileName,
                });

                setTimeout(() => {
                    dialogVisible.value = false;
                    emit('refresh');
                }, 1000);
            },

            // 文件上传失败
            onUploadFailed: function (sdkUploadInfo: Record<string, any>, code: number, message: string) {
                // 更新组件的响应式状态
                uploadInfo.status = 'error';
                uploadInfo.errorMessage = `上传失败: ${message}`;
                useMessage().error(`上传失败: ${message}`);
            },

            // 文件上传进度
            onUploadProgress: function (sdkUploadInfo: Record<string, any>, totalSize: number, loadedPercent: number) {
                // 更新组件的响应式状态
                uploadInfo.progress = Math.floor(loadedPercent * 100);
                uploadInfo.progressText = `上传中 ${uploadInfo.progress}%`;
            },

            // 上传凭证超时
            onUploadTokenExpired: async function () {
                // 重新获取上传凭证
                const res = await refreshUploadAuth({
                    videoId: uploadInfo.videoId,
                });

                if (res.code === 0 && res.data) {
                    const newUploadAuth = res.data.uploadAuth;
                    uploader.resumeUploadWithAuth(newUploadAuth);
                } else {
                    uploadInfo.status = 'error';
                    uploadInfo.errorMessage = '上传凭证刷新失败';
                    useMessage().error('上传凭证刷新失败');
                }
            },

            // 全部文件上传结束
            onUploadEnd: function () {},
        });

        const paramData = {
            Vod: {
                VideoId: uploadInfo.videoId,
                UploadAuth: uploadInfo.uploadAuth,
                UploadAddress: uploadInfo.uploadAddress,
            },
        };

        // 添加文件并上传
        uploader.addFile(uploadInfo.file, null, null, null, JSON.stringify(paramData));
        uploader.startUpload();
    } catch (error: any) {
        uploadInfo.status = 'error';
        uploadInfo.errorMessage = error.message || '上传过程中发生错误';
        useMessage().error(`上传错误: ${error.message}`);
    }
};
// 处理关闭
const handleClose = () => {
    // 如果正在上传，则提示用户
    if (uploadInfo.status === 'uploading') {
        if (confirm('正在上传中，确定要关闭吗？')) {
            dialogVisible.value = false;
        }
    } else {
        dialogVisible.value = false;
    }
};

// 打开对话框
const openDialog = () => {
    dialogVisible.value = true;
    // 重置上传信息
    uploadInfo.file = null;
    uploadInfo.status = 'idle';
    uploadInfo.progress = 0;
    uploadInfo.progressText = '';
    uploadInfo.errorMessage = '';
    uploadInfo.videoId = '';
    uploadInfo.uploadAuth = '';
    uploadInfo.uploadAddress = '';
};

// 对外暴露方法
defineExpose({
    openDialog,
});
</script>

<style scoped lang="scss">
.upload-container {
    padding: 20px;
}

.upload-area {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 180px;
    border: 2px dashed #dcdfe6;
    border-radius: 6px;
    background-color: #f8f9fa;
    cursor: pointer;
    transition: border-color 0.3s;
    margin-bottom: 20px;

    &:hover {
        border-color: #67c23a;
    }
}

.upload-icon {
    margin-bottom: 15px;
}

.upload-text {
    text-align: center;
    color: #606266;
    font-size: 14px;
}

.upload-hint {
    font-size: 12px;
    color: #909399;
    margin-top: 8px;
}

.file-info {
    padding: 10px;
    border: 1px solid #ebeef5;
    border-radius: 4px;
    background-color: #f8f9fa;
    margin-bottom: 15px;
}

.file-name {
    font-weight: bold;
    margin-bottom: 5px;
    word-break: break-all;
}

.file-size {
    color: #909399;
    font-size: 12px;
}

.upload-progress {
    margin-top: 15px;
}

.progress-text {
    text-align: center;
    margin-top: 5px;
    color: #409eff;
    font-size: 12px;
}

.upload-error {
    margin-top: 15px;
}
</style>
