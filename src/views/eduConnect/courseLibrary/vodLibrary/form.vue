<template>
    <el-dialog title="编辑录课信息" v-model="visible" :close-on-click-modal="false" @closed="handleDialogClosed">
        <!--		<h2>{{ form.id }} {{ typeof form.id }}</h2>-->
        <!--		<div>{{ form.recordingResources }}</div>-->
        <el-form
            ref="dataFormRef"
            :model="form"
            :rules="dataRules"
            formDialogRef
            label-width="120px"
            v-loading="loading"
        >
            <el-row>
                <el-col :span="20" :offset="1">
                    <el-form-item label="录课视频">
                        <div class="video-wrapper" v-loading="isVideoLoading">
                            <Player
                                v-if="visible && videoUrl"
                                style="width: 100%"
                                :source="videoUrl"
                                ref="playerRef"
                            ></Player>
                        </div>
                    </el-form-item>
                    <el-form-item label="阶段" prop="grade">
                        <el-select v-model="form.grade" placeholder="请选择" clearable>
                            <el-option
                                v-for="item in grade"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="录课书籍" prop="booksName">
                        <el-input v-model="form.booksName" placeholder="请输入书籍名称" />
                    </el-form-item>
                    <el-form-item label="主讲老师" prop="lecturerId">
                        <!--						<el-input v-model="form.lecturerName" :placeholder="form.lecturerName || ''" />-->
                        <fuzzy-search
                            ref="lecturerRef"
                            class="w-96"
                            placeholder="输入搜索主讲老师"
                            v-model="form.lecturerId"
                            :requestUrl="getLecturerList"
                            request-name="query"
                            data-label="lecturerName"
                            clearable
                        ></fuzzy-search>
                    </el-form-item>
                    <el-form-item label="原上课时间" prop="originalCourseRangeDate">
                        <el-date-picker
                            v-model="form.originalCourseRangeDate"
                            type="datetimerange"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            :value-format="dateTimeStr"
                        />
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="visible = false">取 消</el-button>
                <el-button type="primary" @click="onSubmit" :disabled="loading">确认</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script setup lang="ts" name="SsRecordingDialog">
import fuzzySearch from '/@/components/EduComponents/fuzzySearch.vue';
import { useDict } from '/@/hooks/dict';
import { useMessage } from '/@/hooks/message';
// eslint-disable-next-line
import { getObj, addObj, putObj, validateExist } from '/@/api/eduConnect/ssRecording';
// eslint-disable-next-line
import { rule } from '/@/utils/validate';
import { dateTimeStr } from '/@/utils/formatTime';

const { grade } = useDict('grade');
const emit = defineEmits(['refresh']);
import Player from '/@/components/EduComponents/player/player.vue';
import { getLecturerList } from '/@/api/eduConnect/ssQuery';

// 定义变量内容
const dataFormRef = ref(); // form组件ref
const visible = ref(false); // 可见性
const loading = ref(false); // loading状态
const isVideoLoading = ref(false); // 视频加载状态，控制loading动画
const lecturerRef = ref();
const playerRef = ref();
const videoUrl = ref(''); // 视频URL，用于控制视频播放和停止

// 定义字典
interface Vod {
    id: string;
    grade: string; //阶段
    booksId: string; // 书籍ID
    booksName: string; // 书籍name
    lecturerId: string; // 讲师id
    lecturerName: string; // 讲师 name
    originalCourseRangeDate?: string; // 上课时间范围 <前端自定义>
    originalCourseStartDate: string; // 上课开始时间
    originalCourseEndDate: string; // 上课结束时间
    recordingResources: string; // m3u8视频链接
    [key: string]: any;
}

// 提交表单数据
const form = reactive<Vod>({
    id: '',
    grade: '', //阶段
    booksId: '', // 书籍ID
    booksName: '', // 书籍name
    lecturerId: '', // 讲师id
    lecturerName: '', // 讲师 name
    originalCourseRangeDate: '', // 上课时间范围 <前端自定义>
    originalCourseStartDate: '', // 上课开始时间
    originalCourseEndDate: '', // 上课结束时间
    recordingType: '',
    deviceId: '',
    agoraRecordId: '',
    roomUuid: '',
    shelfStatus: '',
    recordingStatus: '',
    storageType: '',
    vodVideoId: '',
    recordingResources: '',
    agoraCloudRecordId: '',
    cloudRecordingResources: '',
    agoraCloudRecordIndividualResourceId: '',
    agoraCloudRecordIndividualId: '',
    ctime: '',
    creator: '',
    mtime: '',
    modifer: '',
});

// 定义校验规则
const dataRules = ref({
    originalCourseRangeDate: [{ required: true, trigger: 'blur', message: '请录入原上课时间' }],
    grade: [{ required: true, trigger: 'blur', message: '请录入阶段' }],
    booksName: [{ required: true, trigger: 'blur', message: '请录入录课书籍' }],
    lecturerId: [{ required: true, trigger: 'blur', message: '请录入主讲老师' }],
});
// 将开始/结束时间转换到对应字段
watch(
    () => form.originalCourseRangeDate,
    (v) => {
        if (Array.isArray(v)) {
            const [originalCourseStartDate, originalCourseEndDate] = v;
            Object.assign(form, { originalCourseStartDate, originalCourseEndDate });
        } else {
            Object.assign(form, { originalCourseStartDate: '', originalCourseEndDate: '' });
        }
    },
    { flush: 'sync', deep: true }
);

// 关闭弹窗时清除视频播放
const handleDialogClosed = () => {
    // 清空视频URL将导致Player组件卸载，从而停止播放
    videoUrl.value = '';
    // 确保不会有任何遗留的loading状态
    isVideoLoading.value = false;
};

// 打开弹窗
const openDialog = async (id: string = '227') => {
    if (!id) {
        return;
    }
    // 重置表单和视频状态
    form.id = '';
    videoUrl.value = ''; // 清空视频URL，确保旧视频停止播放

    // 重置查询表单数据
    nextTick(() => {
        dataFormRef.value?.resetFields();
    });

    // 设置ID并显示对话框
    form.id = id;
    visible.value = true;

    try {
        // 显示视频加载状态
        isVideoLoading.value = true;
        await getSsRecordingData(id);
    } catch (error) {
        /* empty */
    } finally {
        // 无论成功失败，延迟结束loading状态，确保良好的用户体验
        setTimeout(() => {
            isVideoLoading.value = false;
        }, 2000);
    }
};

// 提交
const onSubmit = async () => {
    const valid = await dataFormRef.value.validate().catch(() => {});
    if (!valid) return false;
    try {
        loading.value = true;
        form.id ? await putObj(form) : await addObj(form);
        useMessage().success(form.id ? '修改成功' : '添加成功');
        visible.value = false;
        emit('refresh');
    } catch (err: any) {
        useMessage().error(err.msg);
    } finally {
        loading.value = false;
    }
};

// 初始化表单数据
const getSsRecordingData = (id: string) => {
    // 获取数据
    loading.value = true;
    getObj({ id: id })
        .then(({ data }) => {
            if (data.grade) {
                data.grade = String(data.grade);
            }
            const { originalCourseStartDate, originalCourseEndDate } = data;
            if (originalCourseStartDate && originalCourseEndDate) {
                data.originalCourseRangeDate = [originalCourseStartDate, originalCourseEndDate];
            }
            Object.assign(form, data);
            // 为了解决 远程检索的
            if (data.lecturerId) {
                lecturerRef.value.remoteMethod(form.lecturerName);
            }

            // 设置视频源 - 延迟执行以确保组件已完全挂载
            if (data.recordingResources) {
                // 延迟设置视频URL，避免过早加载，同时保持loading状态
                setTimeout(() => {
                    // 只有在对话框仍然打开时才设置视频URL
                    if (visible.value) {
                        videoUrl.value = data.recordingResources;
                    }
                }, 500);
            }
        })
        .catch((err) => {
            console.error(err);
        })
        .finally(() => {
            loading.value = false;
        });
};
// 暴露变量
defineExpose({
    openDialog,
});
</script>

<style scoped lang="scss">
.video-wrapper {
    width: 100%;
    min-height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f5f7fa;
    border-radius: 4px;
    position: relative;
    overflow: hidden;

    &[class*='el-loading'] {
        &::before {
            opacity: 1;
        }
    }
}
</style>
