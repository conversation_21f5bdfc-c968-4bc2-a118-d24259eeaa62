<template>
    <div class="layout-padding">
        <div class="layout-padding-auto layout-padding-view">
            <!--搜索栏-->
            <el-row class="ml10" v-show="showSearch">
                <el-form :inline="true" :model="state.queryForm" ref="queryRef">
                    <el-form-item label="书籍名称" prop="booksName">
                        <el-input
                            placeholder="请输入书籍名称"
                            style="width: 200px"
                            clearable
                            v-model="state.queryForm.booksName"
                        />
                    </el-form-item>
                    <el-form-item label="主讲老师" prop="lecturerId">
                        <fuzzy-search
                            placeholder="输入搜索主讲老师"
                            v-model="state.queryForm.lecturerId"
                            :requestUrl="getLecturerList"
                            request-name="query"
                            data-label="lecturerName"
                            clearable
                        ></fuzzy-search>
                    </el-form-item>

                    <el-form-item label="阶段" prop="grade">
                        <el-select
                            v-model="state.queryForm.grade"
                            placeholder="请选择阶段"
                            clearable
                            style="width: 200px"
                        >
                            <el-option
                                v-for="item in grade"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="原上课时间">
                        <el-date-picker
                            v-model="originalCourseDateRange"
                            type="datetimerange"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            :value-format="dateTimeStr"
                        />
                    </el-form-item>

                    <el-form-item label="录制时间">
                        <el-date-picker
                            v-model="recordingDateRange"
                            type="datetimerange"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            :value-format="dateTimeStr"
                        />
                    </el-form-item>

                    <el-form-item>
                        <!--查询按钮-->
                        <el-button
                            v-auth="'edusystem_ssRecording_view'"
                            @click="handleQuery"
                            icon="search"
                            type="primary"
                        >
                            {{ $t('common.queryBtn') }}
                        </el-button>
                        <!--重置按钮-->
                        <el-button v-auth="'edusystem_ssRecording_view'" @click="resetQuery" icon="Refresh"
                            >{{ $t('common.resetBtn') }}
                        </el-button>
                    </el-form-item>
                </el-form>
            </el-row>

            <el-row>
                <div class="mb8" style="width: 100%">
                    <el-button
                        v-auth="'edusystem_ssRecording_add'"
                        class="ml10"
                        plain
                        icon="Upload"
                        type="primary"
                        @click="handleUploadVideo"
                    >
                        上传视频
                    </el-button>
                    <el-button
                        v-auth="'edusystem_ssRecording_del'"
                        class="ml10"
                        plain
                        :disabled="multiple"
                        icon="Delete"
                        type="danger"
                        @click="handleDelete(selectObjs)"
                    >
                        批量删除
                    </el-button>
                    <right-toolbar
                        v-model:showSearch="showSearch"
                        :export="false"
                        @queryTable="getDataList"
                        class="ml10 mr20"
                        style="float: right"
                    ></right-toolbar>
                </div>
            </el-row>

            <el-table
                :data="state.dataList"
                v-loading="state.loading"
                border
                :cell-style="tableStyle.cellStyle"
                :header-cell-style="tableStyle.headerCellStyle"
                @selection-change="selectionChangHandle"
                @sort-change="sortChangeHandle"
            >
                <el-table-column type="selection" width="40" align="center" />
                <el-table-column type="index" label="序号" width="60" fixed="left" />
                <el-table-column label="录课视频" width="140">
                    <template #default="scope">
                        <div class="videoCard">
                            <video-placeholder
                                v-if="scope?.row?.recordingResources"
                                @click.native="previewVideo(scope.row)"
                            />
                        </div>
                    </template>
                </el-table-column>
                <el-table-column prop="recordingStatus" label="录制状态" show-overflow-tooltip>
                    <template #default="{ row }">
                        <dict-tag :options="recording_status" type="primary" :value="row.recordingStatus" />
                    </template>
                </el-table-column>
                <el-table-column prop="grade" label="阶段" show-overflow-tooltip>
                    <template #default="{ row }">
                        <dict-tag :options="grade" type="primary" :value="row.grade" />
                    </template>
                </el-table-column>
                <el-table-column prop="booksName" label="录课书籍" show-overflow-tooltip />
                <el-table-column prop="lecturerName" label="主讲师" show-overflow-tooltip />
                <el-table-column prop="originalCourseStartDate" label="原上课时间" show-overflow-tooltip>
                    <template #default="{ row }">
                        {{ row.originalCourseStartDate }} - {{ row.originalCourseEndDate }}</template
                    >
                </el-table-column>
                <el-table-column prop="recordingTime" label="录制时间" show-overflow-tooltip></el-table-column>
                <el-table-column label="操作">
                    <template #default="scope">
                        <el-dropdown>
                            <el-button type="primary" link>
                                操作<el-icon class="el-icon--right"><arrow-down /></el-icon>
                            </el-button>
                            <template #dropdown>
                                <el-dropdown-menu>
                                    <el-dropdown-item
                                        v-auth="'edusystem_ssRecording_edit'"
                                        icon="edit-pen"
                                        @click="handleEdit(scope.row.id)"
                                    >
                                        编辑
                                    </el-dropdown-item>
                                    <el-dropdown-item
                                        v-auth="'edusystem_ssRecording_del'"
                                        icon="delete"
                                        @click="handleDelete([scope.row.id])"
                                    >
                                        删除
                                    </el-dropdown-item>
                                    <el-dropdown-item icon="view" @click="previewVideo(scope.row)">
                                        预览
                                    </el-dropdown-item>
                                    <el-dropdown-item
                                        v-auth="'edusystem_ssRecording_download'"
                                        icon="download"
                                        @click="handleDownload(scope.row)"
                                        :class="{
                                            'is-disabled':
                                                scope.row.originalCourseStartDate && scope.row.recordingStatus != '2',
                                        }"
                                        :style="{
                                            cursor:
                                                scope.row.originalCourseStartDate && scope.row.recordingStatus != '2'
                                                    ? 'not-allowed'
                                                    : 'pointer',
                                        }"
                                    >
                                        下载
                                    </el-dropdown-item>
                                    <el-dropdown-item
                                        v-auth="'edusystem_ssRecording_download'"
                                        icon="CopyDocument"
                                        @click="
                                            () => {
                                                if (!scope.row.recordingResources) return;
                                                handleGetVideoToken(scope.row);
                                            }
                                        "
                                        :class="{ 'is-disabled': !scope.row.recordingResources }"
                                        :style="{ cursor: !scope.row.recordingResources ? 'not-allowed' : 'pointer' }"
                                    >
                                        复制视频链接
                                    </el-dropdown-item>
                                    <el-dropdown-item
                                        v-auth="'edusystem_ssRecording_download'"
                                        icon="CopyDocument"
                                        @click="
                                            () => {
                                                if (!scope.row.recordingResources) return;
                                                console.log(scope.row);
                                                copyText(scope.row.recordingResources);
                                            }
                                        "
                                        :class="{ 'is-disabled': !scope.row.recordingResources }"
                                        :style="{ cursor: !scope.row.recordingResources ? 'not-allowed' : 'pointer' }"
                                    >
                                        复制补课链接
                                    </el-dropdown-item>
                                </el-dropdown-menu>
                            </template>
                        </el-dropdown>
                    </template>
                </el-table-column>
            </el-table>

            <pagination
                @size-change="sizeChangeHandle"
                @current-change="currentChangeHandle"
                v-bind="state.pagination"
            />
        </div>

        <!-- 编辑  -->
        <form-dialog ref="formDialogRef" @refresh="getDataList(false)" />
        <player-dialog ref="playerDialogRef"></player-dialog>
        <video-play-token-dialog ref="videoPlayTokenDialogRef" />
        <upload-video-dialog ref="uploadVideoDialogRef" @refresh="getDataList(false)" @success="handleUploadSuccess" />
    </div>
</template>

<script setup lang="ts" name="systemSsRecording">
import fuzzySearch from '/@/components/EduComponents/fuzzySearch.vue';
import VideoPlaceholder from '/src/components/EduComponents/player/videoPlaceholder.vue';
import { BasicTableProps, useTable } from '/@/hooks/table';
import { fetchList, delObjs, getDownloadSource, updateVideoInfo } from '/@/api/eduConnect/ssRecording';
import { useMessage, useMessageBox } from '/@/hooks/message';
import { useDict } from '/@/hooks/dict';
import FormDialog from './form.vue';
import { dateTimeStr } from '/@/utils/formatTime';
import { getLecturerList } from '/@/api/eduConnect/ssQuery';
import PlayerDialog from '/@/components/EduComponents/player/playerDialog.vue';
import streamSaver from 'streamsaver';
import commonFunction from '/@/utils/commonFunction';
import { ArrowDown } from '@element-plus/icons-vue';

const { copyText } = commonFunction();

const VideoPlayTokenDialog = defineAsyncComponent(() => import('./videoPlayTokenFrom.vue'));
const UploadVideoDialog = defineAsyncComponent(() => import('./UploadVideoDialog.vue'));
const videoPlayTokenDialogRef = ref();
const uploadVideoDialogRef = ref();

// 定义查询字典
const { grade, recording_status } = useDict('grade', 'recording_status');

// 视频播放器弹窗组件
const playerDialogRef = ref();

// 定义变量内容 ref
const formDialogRef = ref();
// const excelUploadRef = ref();
// 搜索变量
const queryRef = ref();
const showSearch = ref(true);
// 多选变量
const selectObjs = ref([]) as any;
const multiple = ref(true);

const state: BasicTableProps = reactive<BasicTableProps>({
    queryForm: {
        booksName: '', // 主讲师名称
        lecturerName: '', // 主讲师名称
        grade: '', // 阶段
        originalCourseStartDate: '', //上课时间范围
        originalCourseEndDate: '',
        startRecordingDate: '', // 录制时间范围
        endRecordingDate: '',
    },
    pageList: fetchList,
});

const originalCourseDateRange = ref('');
const recordingDateRange = ref('');

watch(
    originalCourseDateRange,
    (v) => {
        if (Array.isArray(v)) {
            const [originalCourseStartDate, originalCourseEndDate] = v;
            Object.assign(state.queryForm, { originalCourseStartDate, originalCourseEndDate });
        } else {
            Object.assign(state.queryForm, { originalCourseStartDate: '', originalCourseEndDate: '' });
        }
    },
    { flush: 'sync' }
);
watch(
    recordingDateRange,
    (v) => {
        if (Array.isArray(v)) {
            const [startRecordingDate, endRecordingDate] = v;
            Object.assign(state.queryForm, { startRecordingDate, endRecordingDate });
        } else {
            Object.assign(state.queryForm, { startRecordingDate: '', endRecordingDate: '' });
        }
    },
    { flush: 'sync' }
);

//  table hook
const { getDataList, currentChangeHandle, sizeChangeHandle, sortChangeHandle, tableStyle } = useTable(state);
const handleQuery = () => {
    getDataList();
};
// 清空搜索条件
const resetQuery = () => {
    // 清空搜索条件
    queryRef.value?.resetFields();
    // 清空多选
    selectObjs.value = [];
    // 清空日期时间选择
    originalCourseDateRange.value = '';
    recordingDateRange.value = '';
    getDataList();
};

// 视频预览
const previewVideo = (row: any) => {
    if (row?.recordingResources) {
        playerDialogRef.value?.openDialog(row.recordingResources);
    } else {
        useMessage().warning('无法预览,视频未上传');
    }
};
const handleEdit = (id: string) => formDialogRef.value.openDialog(id);

function downloadVideo(url: string, name: string) {
    fetch(url)
        .then((res) => {
            // 此处name为文件名，format为文件格式，例如 001.mp4
            const fileStream = streamSaver.createWriteStream(name + '.mp4', {
                size: res.headers.get('content-length'),
            });
            const readableStream: any = res.body;
            // more optimized
            if (window.WritableStream && readableStream.pipeTo) {
                return readableStream.pipeTo(fileStream).then(() => console.log('done writing'));
            }
            window.writer = fileStream.getWriter();
            const reader: any = res.body?.getReader();
            const pump = () =>
                reader
                    .read()
                    .then((res: any) => (res.done ? window.writer.close() : window.writer.write(res.value).then(pump)));
            pump();
        })
        // .then((blob) => {
        //   console.log(2)
        //   const a = document.createElement('a')
        //   document.body.appendChild(a)
        //   a.style.display = 'none'
        //   const url = window.URL.createObjectURL(blob)
        //   a.href = url
        //   a.download = name
        //   a.click()
        //   document.body.removeChild(a)
        //   window.URL.revokeObjectURL(url)
        //   console.log(3)
        // })
        .catch((err) => {
            console.log(err);
        });
}

const handleDownload = async (row: any) => {
    const { id, booksName, recordingStatus } = row;
    if (recordingStatus != 2) {
        useMessage().warning('当前录制状态无法下载');
        return;
    }
    try {
        const res = await getDownloadSource({ id });
        // debugger
        const { recordingResources } = res.data;
        if (!recordingResources) {
            useMessage().warning('下载地址获取失败');
        } else {
            downloadVideo(recordingResources, `${booksName || 'noBookName'}_${Date.now()}`);
        }
    } catch (err: any) {
        useMessage().error((err && err.msg) || '下载失败');
    }
};
// 多选事件
const selectionChangHandle = (objs: { id: string }[]) => {
    selectObjs.value = objs.map(({ id }) => id);
    multiple.value = !objs.length;
};

// 批量删除操作
const handleDelete = async (ids: string[]) => {
    try {
        await useMessageBox().confirm('此操作将永久删除');
    } catch {
        return;
    }
    try {
        await delObjs(ids).then((res) => {
            if (res.code !== 0) throw res;
        });
        getDataList();
        useMessage().success('删除成功');
    } catch (err: any) {
        useMessage().error(err.msg);
    }
};

// 打开上传视频对话框
const handleUploadVideo = () => {
    uploadVideoDialogRef.value.openDialog();
};

// 处理上传成功
const handleUploadSuccess = async (videoInfo: any) => {
    try {
        // 可以在这里处理上传成功后的逻辑，例如更新视频信息
        await updateVideoInfo({
            vodVideoId: videoInfo.videoId,
        });
        // useMessage().success('视频信息更新成功');
        getDataList();
    } catch (err: any) {
        useMessage().error((err && err.msg) || '更新视频信息失败');
    }
};

const handleGetVideoToken = async (val: any) => {
    videoPlayTokenDialogRef.value.open(val.vodVideoId, val.recordingResources);
};
</script>

<style scoped lang="scss">
.el-dropdown-link {
    cursor: pointer;
    color: var(--el-color-primary);
    display: flex;
    align-items: center;
}

:deep(.el-dropdown-menu__item.is-disabled) {
    color: var(--el-text-color-disabled);
    cursor: not-allowed;
    pointer-events: none;
}
</style>
