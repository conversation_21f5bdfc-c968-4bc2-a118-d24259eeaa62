<template>
    <div class="layout-padding">
        <div class="layout-padding-auto layout-padding-view">
            <el-form :inline="true" :model="state.queryForm">
                <el-row v-show="showSearch" class="ml10">
                    <el-form-item label="老师姓名" prop="name">
                        <el-input v-model="state.queryForm.name" placeholder="请输入老师姓名" clearable />
                    </el-form-item>
                    <el-form-item>
                        <el-button @click="getDataList" formDialogRef icon="search" type="primary">
                            {{ $t('common.queryBtn') }}
                        </el-button>
                        <el-button @click="resetQuery" formDialogRef icon="Refresh">
                            {{ $t('common.resetBtn') }}
                        </el-button>
                    </el-form-item>
                </el-row>
                <el-row>
                    <div class="mb8" style="width: 100%">
                        <el-button class="ml10" icon="folder-add" type="primary" @click="addTeachers"> 新增 </el-button>
                        <right-toolbar
                            class="ml10 mr20"
                            v-model:showSearch="showSearch"
                            :export="false"
                            style="float: right"
                            @queryTable="getDataList"
                        />
                    </div>
                </el-row>
            </el-form>

            <el-dialog v-model="visible" :title="$t(props.title)" :close-on-click-modal="false" style="width: 550px">
                <el-form
                    ref="dataFormRef"
                    :inline="true"
                    :model="form"
                    :rules="dataRules"
                    label-width="80px"
                    label-position="left"
                >
                    <el-row :gutter="24">
                        <el-col :span="24" class="mb20">
                            <el-form-item label="选择老师" prop="userId" style="width: 277px; margin-left: -11px">
                                <fuzzy-search
                                    placeholder="请选择老师"
                                    data-key="userId"
                                    v-model="form.userId"
                                    :requestUrl="getuser"
                                    request-name="name"
                                    data-label="name"
                                    clearable
                                ></fuzzy-search>
                            </el-form-item>
                            <div class="selectTeacher">可通过老师姓名进行搜索</div>
                        </el-col>
                        <el-col :span="24" class="mb20">
                            <el-form :inline="true" :model="form">
                                <el-form-item label="老师昵称">
                                    <el-input
                                        v-model="form.nickname"
                                        placeholder="请输入老师昵称"
                                        clearable
                                        maxlength="50"
                                    />
                                </el-form-item>
                            </el-form>
                        </el-col>
                    </el-row>
                </el-form>
                <template #footer>
                    <div class="dialog-footer">
                        <el-button @click="visible = false">取消</el-button>
                        <el-button type="primary" @click="add"> 确定 </el-button>
                    </div>
                </template>
            </el-dialog>
            <el-table
                :data="state.dataList"
                v-loading="state.loading"
                border
                :cell-style="tableStyle.cellStyle"
                :header-cell-style="tableStyle.headerCellStyle"
            >
                <el-table-column type="index" label="序号" width="60" />
                <el-table-column prop="name" label="老师姓名">
                    <template #default="scope">
                        <span>{{ scope.row.name }}</span>
                        <span v-if="scope.row.nickname">{{ '【' + scope.row.nickname + '】' }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="status" label="状态">
                    <template #default="scope">
                        <el-switch
                            v-model="scope.row.status"
                            :active-value="0"
                            :inactive-value="1"
                            class="ml-2"
                            width="60"
                            inline-prompt
                            active-text="带课"
                            inactive-text="不带课"
                            @click="handleSwitchChange(scope.row)"
                        />
                    </template>
                </el-table-column>
                <el-table-column prop="updateBy" label="操作人" />
                <el-table-column prop="updateTime" label="操作时间" />
            </el-table>
            <pagination
                @size-change="sizeChangeHandle"
                @current-change="currentChangeHandle"
                v-bind="state.pagination"
            />
        </div>

        <!-- 编辑、新增  -->
        <form-dialog ref="formDialogRef" @refresh="getDataList(false)" />
    </div>
</template>

<script setup lang="ts">
//引入方法
import { BasicTableProps, useTable } from '/@/hooks/table';
import { reactive, ref } from 'vue';
import { fetchList, addTeacher, getuser, editTeacher } from '/@/api/eduConnect/teacher';

//引入组件
import fuzzySearch from '/@/components/EduComponents/fuzzySearch.vue';
import { useMessage, useMessageBox } from '/@/hooks/message';

// 定义变量内容
const formDialogRef = ref();
const dataFormRef = ref();
const showSearch = ref(true);

const form = reactive<any>({
    name: '',
    nickname: '',
    userId: '',
});

const state: BasicTableProps = reactive<BasicTableProps>({
    queryForm: {
        userId: '',
    },
    pageList: fetchList,
});

const { getDataList, currentChangeHandle, sizeChangeHandle, tableStyle } = useTable(state);

const props = defineProps({
    title: {
        type: String,
        default: '新增老师',
    },
});

// 定义校验规则
const dataRules = ref({
    userId: [{ required: true, message: '老师不能为空', trigger: 'change' }],
});

// 清空搜索条件
const resetQuery = () => {
    // 清空搜索条件
    dataFormRef.value?.resetFields();
    state.queryForm.name = '';
    getDataList();
};

//新增
const visible = ref(false);

const addTeachers = () => {
    visible.value = true;
    form.nickname = '';
    dataFormRef.value.resetFields();
};

//确定
const add = async () => {
    if (form.userId) {
        try {
            await addTeacher(form);
            visible.value = false;
            getDataList();
        } catch (error: any) {
            useMessage().error(error.msg);
        }
    } else {
        useMessage().error('请填写完整信息');
    }
};

// 切换设备状态
const handleSwitchChange = async (row: any) => {
    try {
        const res = await editTeacher({ status: row.status, userId: row.userId });
        console.log(res);
        if (res.data) {
            getDataList();
        } else {
            useMessageBox().warning(row.name + '还有未结束的教学计划，不能修改状态，若想修改，请先修改教学计划');
            row.status = row.status === 0 ? 1 : 0;
        }
    } catch (error) {
        //FIXME: 这里临时这样修改，后期统一修改请求拦截器，对于失败的请求 应该在正常代码块中处理而不是在catch中处理
        console.error('catch', error);
    }
};
</script>

<style scoped>
.selectTeacher {
    display: flex;
    position: relative;
    top: -43px;
    left: 300px;
    color: red;
    font-size: 12px;
}
</style>
