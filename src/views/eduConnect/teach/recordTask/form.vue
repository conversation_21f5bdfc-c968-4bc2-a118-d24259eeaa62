<template>
    <el-dialog
        v-model="dialogVisible"
        :title="isReassociate ? '重新关联视频' : '关联视频'"
        width="720px"
        destroy-on-close
        @closed="resetForm"
    >
        <div v-loading="loading">
            <div class="task-info">
                <el-descriptions title="任务信息" :column="2" border>
                    <el-descriptions-item label="阶段">
                        {{ taskData.stageName }}
                    </el-descriptions-item>
                    <el-descriptions-item label="课节名称">{{ taskData.lessonName }}</el-descriptions-item>
                    <el-descriptions-item label="主讲老师">{{ taskData.lectureName }}</el-descriptions-item>
                    <el-descriptions-item label="上课时段">{{ taskData.dateName }}</el-descriptions-item>
                </el-descriptions>
            </div>

            <div class="search-container mt20">
                <fuzzy-search
                    v-model="selectedVideo"
                    :requestUrl="searchVideosApi"
                    request-name="booksName"
                    placeholder="请输入视频名称搜索"
                    data-key="id"
                    data-label="booksName"
                    :get-label="(row) => `${row.booksName} - ${row.lecturerName}`"
                    @change="handleVideoSelect"
                    class="w-full"
                />
            </div>

            <div v-if="selectedVideoInfo" class="video-preview mt20">
                <el-card shadow="hover">
                    <div class="video-info">
                        <div class="video-title">
                            <h3>{{ selectedVideoInfo.title }}</h3>
                            <span class="video-book">书籍：{{ selectedVideoInfo.booksName }}</span>
                            <span class="video-teacher">主讲：{{ selectedVideoInfo.lecturerName }}</span>
                        </div>
                        <div class="video-player">
                            <el-button type="primary" size="small" @click="playVideo">
                                <el-icon><VideoPlay /></el-icon> 预览视频
                            </el-button>
                        </div>
                    </div>
                </el-card>
            </div>

            <!-- 视频播放组件 -->
            <player-dialog ref="playerDialogRef"></player-dialog>
        </div>

        <template #footer>
            <div class="dialog-footer">
                <el-button @click="closeDialog">取消</el-button>
                <el-button type="primary" :disabled="!selectedVideo" @click="submitAssociation">
                    确认{{ isReassociate ? '重新关联' : '关联' }}
                </el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue';
import { useMessage } from '/@/hooks/message';
import { VideoPlay } from '@element-plus/icons-vue';
import { associateVideo, searchVideos as searchVideosApi } from '/@/api/eduConnect/recordTask';
import PlayerDialog from '/@/components/EduComponents/player/playerDialog.vue';
import FuzzySearch from '/@/components/EduComponents/fuzzySearch.vue';

// 定义事件
const emit = defineEmits(['success', 'close']);

// 弹窗可见状态
const dialogVisible = ref(false);

// 关联类型
const operationType = ref('associate'); // 'associate' 或 'reassociate'
const isReassociate = computed(() => operationType.value === 'reassociate');

// 任务数据
const taskData = ref<any>({});

// 阶段列表
const stage_list = ref<any[]>([]);

// 数据加载状态
const loading = ref(false);

// 视频搜索
const selectedVideo = ref('');
const selectedVideoInfo = ref<any>(null);

// 视频预览相关
const videoDialogVisible = ref(false);
const videoPlayer = ref<HTMLVideoElement | null>(null);
const playerDialogRef = ref();

// 打开弹窗
const open = (task: any, stageList: any[], type = 'associate') => {
    taskData.value = {
        ...task,
        stageName: getStageNameById(task.stageId, stageList),
    };
    operationType.value = type;
    stage_list.value = stageList;
    dialogVisible.value = true;
};

// 根据ID获取阶段名称
const getStageNameById = (stageId: string | number, stageList: any[]) => {
    if (!stageId || !stageList || !stageList.length) return '';
    const found = stageList.find((item: any) => item.value === stageId);
    return found ? found.label : '';
};

// 关闭弹窗
const closeDialog = () => {
    dialogVisible.value = false;
};

// 重置表单
const resetForm = () => {
    selectedVideo.value = '';
    selectedVideoInfo.value = null;
};

// 选择视频
const handleVideoSelect = (videoData: any) => {
    if (videoData) {
        // 从searchVideosApi请求中获取选中的视频详情
        selectedVideoInfo.value = videoData;
    }
};

// 播放视频
const playVideo = () => {
    if (selectedVideoInfo.value && selectedVideoInfo.value.recordingResources) {
        if (playerDialogRef.value) {
            playerDialogRef.value.openDialog(selectedVideoInfo.value.recordingResources);
        } else {
            videoDialogVisible.value = true;
            // 视频加载后自动播放
            setTimeout(() => {
                if (videoPlayer.value) {
                    videoPlayer.value.play().catch((e) => console.error('Video play failed:', e));
                }
            }, 300);
        }
    } else {
        useMessage().warning('该视频暂无预览地址');
    }
};

// 提交关联
const submitAssociation = async () => {
    if (!selectedVideo.value) {
        useMessage().warning('请先选择要关联的视频');
        return;
    }

    try {
        loading.value = true;
        const data = {
            id: taskData.value.id,
            resourcesId: selectedVideo.value,
        };

        const res = await associateVideo(data);
        if (res.code === 0) {
            useMessage().success('视频关联成功');
            emit('success');
            closeDialog();
        }
    } catch (error: any) {
        useMessage().error(error.msg || '关联视频失败');
    } finally {
        loading.value = false;
    }
};

// 暴露组件方法
defineExpose({
    open,
});
</script>

<style scoped>
.task-info {
    margin-bottom: 20px;
}

.search-container {
    display: flex;
    gap: 16px;
    margin-bottom: 20px;
}

.search-input {
    width: 300px;
}

.video-select {
    width: 400px;
}

.video-option {
    display: flex;
    justify-content: space-between;
    width: 100%;
}

.video-teacher {
    color: #909399;
    font-size: 12px;
}

.video-preview {
    margin-bottom: 20px;
}

.video-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.video-title h3 {
    margin: 0 0 8px 0;
}

.video-book {
    margin-right: 16px;
}

.video-container {
    width: 100%;
    display: flex;
    justify-content: center;
}

.full-width-video {
    width: 100%;
    max-height: 500px;
}

.mt20 {
    margin-top: 20px;
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 16px;
}
</style>
