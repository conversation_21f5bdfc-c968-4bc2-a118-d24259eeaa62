<template>
    <div class="layout-padding">
        <div class="layout-padding-auto layout-padding-view">
            <div class="mb8">
                <el-form :inline="true" @submit.prevent>
                    <el-form-item label="阶段">
                        <el-select v-model="queryForm.stageId" placeholder="阶段" class="w-[220px]" clearable>
                            <el-option
                                v-for="item in stage_list"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="任务状态">
                        <el-select v-model="queryForm.taskStatus" placeholder="任务状态" class="w-[220px]" clearable>
                            <el-option
                                v-for="item in statusOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="主讲师姓名">
                        <fuzzy-search
                            v-model="queryForm.lectureId"
                            :requestUrl="() => fetchAllTeacher(1)"
                            placeholder="请输入主讲师姓名"
                            data-key="userId"
                            data-label="name"
                            :get-label="(row: any) => (row.nickname ? `${row.name}(${row.nickname})` : row.name)"
                            :remote="false"
                            class="w-[220px]"
                        />
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="getDataList">
                            <el-icon>
                                <ele-Search />
                            </el-icon>
                            查询
                        </el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-form>
            </div>
            <el-row class="mb12">
                <el-button type="primary" :loading="generateLoading" @click="handleGenerateTask">
                    生成录课任务
                </el-button>
            </el-row>

            <el-table
                ref="dataTableRef"
                v-loading="loading"
                :data="dataList"
                style="width: 100%"
                :cell-style="tableStyle.cellStyle"
                :header-cell-style="tableStyle.headerCellStyle"
            >
                <el-table-column label="阶段" prop="stageId">
                    <template #default="scope">
                        {{ getStageLabel(scope.row.stageId) }}
                    </template>
                </el-table-column>
                <el-table-column label="直播间计划名称" prop="livePlanName" width="150" show-overflow-tooltip />
                <el-table-column label="课节名称" prop="lessonName" width="150" show-overflow-tooltip />
                <el-table-column label="关联书籍" prop="bookName" width="150" show-overflow-tooltip />
                <el-table-column label="日期时段" prop="dateName" width="220" />
                <el-table-column label="主讲师" prop="lectureName" width="130" />
                <el-table-column label="状态" prop="taskStatus">
                    <template #default="scope">
                        <el-tag :type="scope.row.taskStatus === 1 ? 'success' : 'warning'">
                            {{ scope.row.taskStatus === 1 ? '已完成' : scope.row.taskStatus === 0 ? '未完成' : '--' }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="操作" fixed="right" width="120">
                    <template #default="scope">
                        <el-button
                            v-if="scope.row.taskStatus === 0"
                            size="small"
                            type="primary"
                            link
                            @click="handleAssociate(scope.row)"
                        >
                            关联
                        </el-button>
                        <el-button
                            v-if="scope.row.taskStatus === 1"
                            size="small"
                            link
                            type="warning"
                            @click="handleReassociate(scope.row)"
                        >
                            重新关联
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>

            <!-- 分页组件 -->
            <el-pagination
                v-model:current-page="pagination.current"
                v-model:page-size="pagination.size"
                :total="pagination.total"
                :page-sizes="pagination.pageSizes"
                :layout="pagination.layout"
                @size-change="sizeChangeHandle"
                @current-change="currentChangeHandle"
            />

            <!-- 关联视频弹窗 -->
            <AssociateForm ref="formRef" @success="handleAssociateSuccess" />
        </div>
    </div>
</template>

<script lang="ts" setup>
import { reactive, ref, onMounted } from 'vue';
import { fetchList, generateTask } from '/@/api/eduConnect/recordTask';
import { useStageOptions, useStageDisplay } from '/@/hooks/stage';
import { useMessage } from '/@/hooks/message';
import AssociateForm from './form.vue';
import { fetchAllTeacher } from '/@/api/eduConnect/teacher';
import fuzzySearch from '/@/components/EduComponents/fuzzySearch.vue';

// 查询表单
const queryForm = reactive({
    stageId: '',
    taskStatus: '',
    lectureId: '',
});

// 生成录课任务加载状态
const generateLoading = ref(false);

// 阶段选项
const { options: stage_list } = useStageOptions();
const { getStageName } = useStageDisplay();

// 获取阶段标签
const getStageLabel = (value: string): string => {
    return getStageName(value) || value;
};

// 状态选项
const statusOptions = [
    { value: 1, label: '已完成' },
    { value: 0, label: '未完成' },
];

// 表格样式
const tableStyle = {
    cellStyle: { textAlign: 'center' },
    headerCellStyle: {
        textAlign: 'center',
        background: 'var(--el-table-row-hover-bg-color)',
        color: 'var(--el-text-color-primary)',
    },
};

// 数据列表和加载状态
const dataList = ref([]);
const loading = ref(false);
const dataTableRef = ref();
const formRef = ref(); // 表单弹窗引用

// 分页设置
const pagination = reactive({
    current: 1,
    size: 10,
    total: 0,
    pageSizes: [10, 20, 50, 100],
    layout: 'total, sizes, prev, pager, next, jumper',
});

// 获取数据列表
const getDataList = async (refresh = true) => {
    try {
        loading.value = true;
        if (refresh) {
            pagination.current = 1;
        }

        // 构建查询参数
        const params: any = {
            stageId: queryForm.stageId,
            taskStatus: queryForm.taskStatus,
            current: pagination.current,
            size: pagination.size,
        };

        // 如果选择了讲师，则添加teacherId参数
        if (queryForm.lectureId) {
            params.lectureId = queryForm.lectureId;
        }

        const res = await fetchList(params);
        if (res.data) {
            dataList.value = res.data.records || [];
            pagination.total = res.data.total || 0;
        }
    } catch (error: any) {
        useMessage().error(error);
    } finally {
        loading.value = false;
    }
};

// 分页大小改变事件处理函数
const sizeChangeHandle = (val: number) => {
    pagination.size = val;
    getDataList(false);
};

// 当前页码改变事件处理函数
const currentChangeHandle = (val: number) => {
    pagination.current = val;
    getDataList(false);
};

// 处理关联
const handleAssociate = (row: any) => {
    formRef.value.open(row, stage_list.value, 'associate');
};

// 处理重新关联
const handleReassociate = (row: any) => {
    formRef.value.open(row, stage_list.value, 'reassociate');
};

// 处理关联成功
const handleAssociateSuccess = () => {
    getDataList(false);
};

// 重置查询
const resetQuery = () => {
    queryForm.stageId = '';
    queryForm.taskStatus = '';
    queryForm.lectureId = '';
    getDataList();
};

// 生成录课任务
const handleGenerateTask = async () => {
    try {
        generateLoading.value = true;
        const res = await generateTask();
        if (res.code === 0) {
            useMessage().success('生成录课任务成功');
        }
    } catch (error: any) {
        useMessage().error(error);
    } finally {
        generateLoading.value = false;
    }
};

// 初始加载数据
onMounted(() => {
    getDataList();
});
</script>
