<template>
    <div class="layout-padding">
        <div class="layout-padding-auto layout-padding-view">
            <el-row class="ml10" v-show="showSearch">
                <el-form :inline="true" :model="state.queryForm" ref="queryRef">
                    <!-- 查询直播间字段 -->
                    <el-form-item label="所属阶段" prop="stage" style="width: 250px">
                        <el-select v-model="state.queryForm.stage" placeholder="请选择" style="width: 200px" clearable>
                            <el-option
                                v-for="item in grade"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            />
                        </el-select>
                    </el-form-item>

                    <el-form-item label="课程名称" prop="courseId">
                        <fuzzy-search
                            placeholder="请输入课程"
                            data-key="id"
                            v-model="state.queryForm.courseId"
                            :requestUrl="getlistByCourseName"
                            request-name="courseName"
                            data-label="courseName"
                            clearable
                            @change="changeCourseName"
                        />
                    </el-form-item>

                    <el-form-item label="课节名称" prop="lessonOrder">
                        <el-select
                            v-model="state.queryForm.lessonOrder"
                            clearable
                            filterable
                            remote
                            reserve-keyword
                            placeholder="请输入课节"
                            remote-show-suffix
                            :remote-method="pubListByNameAndId"
                            style="width: 240px"
                        >
                            <el-option
                                v-for="item in options"
                                :key="item.id"
                                :label="item.lessonName"
                                :value="item.lessonOrder"
                            />
                        </el-select>
                    </el-form-item>

                    <el-form-item>
                        <el-button @click="getDataList" formDialogRef icon="search" type="primary">
                            {{ $t('common.queryBtn') }}
                        </el-button>
                        <el-button @click="resetQuery" formDialogRef icon="Refresh">
                            {{ $t('common.resetBtn') }}
                        </el-button>
                    </el-form-item>
                </el-form>
            </el-row>
            <el-table
                :data="state.dataList"
                v-loading="state.loading"
                border
                :cell-style="tableStyle.cellStyle"
                :header-cell-style="tableStyle.headerCellStyle"
                @sort-change="sortChangeHandle"
            >
                <el-table-column type="index" label="序号" width="60" />
                <el-table-column prop="lessonName" label="课节名称" show-overflow-tooltip />
                <el-table-column prop="stage" label="阶段" show-overflow-tooltip>
                    <template #default="{ row }">
                        <dict-tag :options="grade" type="primary" :value="row.stage"></dict-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="courseName" label="课程名称" show-overflow-tooltip />
                <el-table-column prop="courseVodVideoCount" label="关联录课视频数量" show-overflow-tooltip />

                <el-table-column prop="updateTime" label="提交时间" show-overflow-tooltip>
                    <template #default="{ row }">
                        <span v-if="row.updateTime">{{ row.updateTime }}</span>
                        <span v-else>/</span>
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="150">
                    <template #default="{ row }">
                        <el-link
                            type="primary"
                            :underline="false"
                            @click="linkToDemand(row.lessonOrder, row.courseId, row.lessonName)"
                        >
                            查看
                        </el-link>
                    </template>
                </el-table-column>
            </el-table>
        </div>

        <!-- 编辑、新增  -->
        <!-- <form-dialog ref="formDialogRef" @refresh="getDataList(false)" /> -->

        <!-- <el-dialog width="600px" v-model="sucessDel" :show-close="false">
            <span style="font-size: 20px;color: #101010;">删除成功</span>
        </el-dialog> -->
    </div>
</template>

<script setup lang="ts" name="systemLiveRoomSchedule">
import { BasicTableProps, useTable } from '/@/hooks/table';
import { useMessageBox } from '/@/hooks/message';
import { useDict } from '/@/hooks/dict';
import fuzzySearch from '/@/components/EduComponents/fuzzySearch.vue';
import { fetchList, getlistByCourseName, getpubListByNameAndId } from '/@/api/eduConnect/demand';
const $router = useRouter();
// 定义查询字典
const { grade } = useDict('grade');
// 引入组件
// const FormDialog = defineAsyncComponent(() => import('./form.vue'));
// 搜索变量
const queryRef = ref();
const showSearch = ref(true);
const options = ref();

const state: BasicTableProps = reactive<BasicTableProps>({
    queryForm: {},
    isPage: false,
    pageList: fetchList,
});
const changeCourseName = () => {
    state.queryForm.lessonOrder = '';
    options.value = '';
};

//  table hook
const { getDataList, sortChangeHandle, tableStyle } = useTable(state);
const pubListByNameAndId = async (query: string) => {
    const form = {
        courseId: state.queryForm.courseId,
        lessonName: query,
    };
    console.log(form);

    if (form.courseId) {
        try {
            let res = await getpubListByNameAndId(form);
            console.log(res.data);

            options.value = res.data;
            console.log(options.value);
        } catch (err: any) {
            useMessageBox().error(err.msg);
        }
    }
};

// linkToDemand
const linkToDemand = (id: number, courseId: number, name: string) => {
    $router.push({
        path: '/eduConnect/teach/demand/demandDetail/index',
        query: {
            id: id,
            courseId: courseId,
            name: name,
            from: encodeURIComponent($router.currentRoute.value.fullPath),
        },
    });
};

// 清空搜索条件
const resetQuery = () => {
    // 清空搜索条件
    queryRef.value?.resetFields();
    // 清空多选
    // selectObjs.value = []
    getDataList();
};
</script>

<style scoped lang="scss">
.el-dropdown {
    display: flex;
    align-items: center;
    justify-content: center;

    :deep(.el-dropdown-link) {
        cursor: pointer;
        color: var(--el-color-primary);
        display: flex;
        align-items: center;
    }
}

:deep(.el-overlay .el-overlay-dialog .el-dialog .el-dialog__body) {
    text-align: center;
}

:deep(.el-dialog__header) {
    padding: 0;
}
</style>
