<template>
    <div class="layout-padding">
        <div class="layout-padding-auto layout-padding-view">
            <navi-bar
                class="mb20"
                :title="$route.query.title as string"
                :sub-title="$route.query.author as string"
                :from="decodeURIComponent($route.query.from as string)"
            />
            <el-row class="ml10" v-show="showSearch">
                <span style="color: #6c6c6c; font-size: 20px; display: inline-block; margin-top: 5px"
                    >课节名称：{{ $route.query.name }}</span
                >
                <div
                    style="
                        width: 1526px;
                        height: 1px;
                        background-color: rgba(255, 255, 255, 1);
                        border: 1px solid rgba(187, 187, 187, 1);
                        margin: 10px 0;
                    "
                ></div>
            </el-row>

            <el-table
                :data="state.dataList"
                v-loading="state.loading"
                border
                :cell-style="tableStyle.cellStyle"
                :header-cell-style="tableStyle.headerCellStyle"
                @sort-change="sortChangeHandle"
            >
                <el-table-column type="index" label="序号" width="60" fixed="left" />
                <el-table-column label="点播课" width="140">
                    <template #default="scope">
                        <div class="videoCard">
                            <video-placeholder
                                v-if="scope?.row?.aliyunPlayUrl"
                                @click.native="previewVideo(scope.row)"
                            />
                        </div>
                    </template>
                </el-table-column>
                <el-table-column prop="coursewareName" label="使用课件版本" show-overflow-tooltip />
                <el-table-column prop="coursewareCreateTime" label="课件版本发布时间" show-overflow-tooltip />
                <el-table-column prop="name" label="主讲老师" show-overflow-tooltip />
                <el-table-column prop="updateTime" label="提交时间" show-overflow-tooltip />
                <el-table-column prop="disable" label="状态" show-overflow-tooltip>
                    <template #default="{ row }">
                        <dict-tag
                            :options="deactivated"
                            :type="setTagType(row.disable, { '1': 'info', '0': 'success' })"
                            :value="row.disable"
                        />
                    </template>
                </el-table-column>
                <el-table-column label="操作">
                    <template #default="{ row }">
                        <el-link type="primary" :underline="false" @click="stop(row)" :disabled="row.disable">
                            停用
                        </el-link>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <player-dialog ref="playerDialogRef"></player-dialog>
    </div>
</template>

<script setup lang="ts" name="systemSsRecording">
import VideoPlaceholder from '/src/components/EduComponents/player/videoPlaceholder.vue';
import { BasicTableProps, useTable } from '/@/hooks/table';
import { detailDemand, delObjs } from '/@/api/eduConnect/demand';
import { useMessage, useMessageBox } from '/@/hooks/message';
import { useDict } from '/@/hooks/dict';
import PlayerDialog from '/@/components/EduComponents/player/playerDialog.vue';
import NaviBar from '/@/components/NaviBar/index.vue';

const $route = useRoute();
// 定义查询字典
const { deactivated } = useDict('deactivated');
// 视频播放器弹窗组件
const playerDialogRef = ref();

// 定义变量内容 ref

// 搜索变量
const showSearch = ref(true);

const state: BasicTableProps = reactive<BasicTableProps>({
    queryForm: {
        lessonOrder: $route.query.id,
        courseId: $route.query.courseId,
    },
    isPage: false,
    pageList: detailDemand,
});
console.log(state.queryForm);

//  table hook
const { getDataList, sortChangeHandle, tableStyle } = useTable(state);
const setTagType = (key: any, obj: any) => {
    return obj[key];
};

// 视频预览
const previewVideo = (row: any) => {
    if (row?.aliyunPlayUrl) {
        playerDialogRef.value?.openDialog(row.aliyunPlayUrl);
    } else {
        useMessage().warning('无法预览,视频未上传');
    }
};
// 暂停使用
const stop = async (row: any) => {
    try {
        await useMessageBox().confirm('您确定要停止使用本点播课视频吗？');
    } catch {
        return;
    }
    // 暂停
    try {
        await delObjs({ id: row.id });
        getDataList();
        useMessage().success('停用成功');
    } catch (err: any) {
        useMessage().error(err.msg);
    }
};
</script>

<style scoped lang="scss">
.el-dropdown-link {
    cursor: pointer;
    color: var(--el-color-primary);
    display: flex;
    align-items: center;
}
</style>
