<template>
    <el-dialog title="编辑排期" v-model="visible" :close-on-click-modal="false" width="600" @close="onClose">
        <el-form
            ref="dataFormRef"
            :model="form"
            :rules="dataRules"
            formDialogRef
            label-width="120px"
            v-loading="loading"
        >
            <el-row :gutter="24">
                <el-col :span="24" class="mb20">
                    <el-form-item label="课节名称" prop="lessonName">
                        <el-input
                            v-model.trim="form.lessonName"
                            placeholder="请输入"
                            disabled
                            maxlength="50"
                            show-word-limit
                        />
                    </el-form-item>
                </el-col>
                <el-col :span="24" class="mb20">
                    <el-form-item label="关联书籍" prop="bookName">
                        <el-input
                            v-model.trim="form.bookName"
                            placeholder="请输入"
                            disabled
                            maxlength="50"
                            show-word-limit
                        />
                    </el-form-item>
                </el-col>
                <el-col :span="24" class="mb20">
                    <el-form-item label="上课时段" prop="slotId">
                        <el-input
                            :value="form.classDate + ' ' + form.classWeek + ' ' + classTimeArray.find((item: any) => item.id == form.slotId)?.name"
                            placeholder="请输入"
                            disabled
                            maxlength="50"
                            show-word-limit
                        />
                    </el-form-item>
                </el-col>
                <el-col :span="24" class="mb20">
                    <el-form-item label="主讲老师" prop="lectureId">
                        <fuzzy-search
                            v-model="form.lectureId"
                            :requestUrl="fetchAllTeacher"
                            placeholder="请输入主讲老师名称"
                            data-key="userId"
                            data-label="name"
                            :get-label="(row) => `${row.name}${row.nickname ? '(' + row.nickname + ')' : ''}`"
                            :remote="false"
                            @change="handleLectureChange"
                        />
                    </el-form-item>
                </el-col>
                <el-col :span="24" class="mb20">
                    <el-form-item label="">
                        <el-checkbox v-model="form.useAll" label="应用到所有课节" :true-value="1" :false-value="0" />
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="visible = false">取消</el-button>
                <el-button type="primary" :disabled="loading" @click="onSubmit">确认</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script setup lang="ts" name="EditTeachPlanDetailDialog">
import fuzzySearch from '/@/components/EduComponents/fuzzySearch.vue';
import { useMessage } from '/@/hooks/message';
import { editTeachPlanPerioded } from '/@/api/eduConnect/teachPlan';
import { fetchAllTeacher } from '/@/api/eduConnect/teacher';
import { fetchList } from '/@/api/eduConnect/timePeriod';

const emit = defineEmits(['refresh']);
const dataFormRef = ref();
const visible = ref(false);
const loading = ref(false);
const classTimeArray = ref<any[]>([]);
const form = reactive({
    id: '',
    planId: '',
    lectureId: '',
    lectureName: '',
    lessonName: '',
    bookName: '',
    classDate: '',
    classWeek: '',
    slotId: '',
    useAll: 0,
});
const dataRules = ref({
    lessonName: [{ required: true, message: '课节名称不能为空', trigger: 'blur' }],
    bookName: [{ required: true, message: '关联书籍不能为空', trigger: 'blur' }],
    slotId: [{ required: true, message: '上课时段不能为空', trigger: 'blur' }],
    lectureId: [{ required: true, message: '主讲老师不能为空', trigger: 'blur' }],
});

onMounted(() => {
    fetchList()
        .then((res) => {
            classTimeArray.value = res.data || [];
        })
        .catch((err) => {
            useMessage().error(err.msg);
        });
});

// 打开弹窗
const openDialog = (item: any) => {
    form.planId = '';
    visible.value = true;
    if (item) {
        Object.assign(form, item);
    }
};

const handleLectureChange = (item: any | undefined) => {
    form.lectureId = item?.userId ?? '';
    form.lectureName = (item?.name ?? '') + (item?.nickname ? `(${item.nickname})` : '');
};

const onSubmit = async () => {
    const valid = await dataFormRef.value.validate().catch(() => {});
    if (!valid) return false;
    try {
        loading.value = true;
        await editTeachPlanPerioded({ ...form, useAll: form.useAll ? 1 : 0 });
        useMessage().success('编辑成功');
        visible.value = false;
        emit('refresh');
    } catch (err: any) {
        useMessage().error(err.msg);
    } finally {
        loading.value = false;
    }
};

const onClose = () => {
    dataFormRef.value?.resetFields();
    form.useAll = 0;
};

// 暴露变量
defineExpose({
    open: openDialog,
});
</script>
