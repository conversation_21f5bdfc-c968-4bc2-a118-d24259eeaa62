<template>
    <el-dialog
        :title="form.id ? '编辑教学计划' : '添加教学计划'"
        v-model="visible"
        :close-on-click-modal="false"
        width="600"
        destroy-on-close
        @close="onClose"
    >
        <el-form
            ref="dataFormRef"
            :model="form"
            :rules="dataRules"
            formDialogRef
            label-width="100px"
            v-loading="loading"
        >
            <el-row :gutter="24">
                <el-col :span="24" class="mb20">
                    <el-form-item label="直播间计划" prop="liveRoomPlanId">
                        <fuzzy-search
                            v-model="form.liveRoomPlanId"
                            :requestUrl="fetchPublishLiveRoomPlan"
                            placeholder="请输入直播间计划名称"
                            data-key="planId"
                            data-label="planName"
                            :remote="false"
                            :echoContent="form.planName"
                        />
                    </el-form-item>
                </el-col>
                <el-col :span="24" class="mb20">
                    <el-form-item label="课程" prop="courseId">
                        <fuzzy-search
                            v-model="form.courseId"
                            :requestUrl="fetchAllPublishCourse"
                            placeholder="请输入课程名称"
                            data-key="id"
                            data-label="courseName"
                            :remote="false"
                            @change="handleCourseChange"
                        />
                    </el-form-item>
                </el-col>
                <el-col :span="24" class="mb20">
                    <el-form-item label="主讲老师" prop="lectureId">
                        <fuzzy-search
                            v-model="form.lectureId"
                            :requestUrl="fetchAllTeacher"
                            placeholder="请输入主讲老师名称"
                            data-key="userId"
                            data-label="name"
                            :get-label="(row) => (row.nickname ? `${row.name}(${row.nickname})` : row.name)"
                            :remote="false"
                            @change="handleLectureChange"
                        />
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="visible = false">取消</el-button>
                <el-button type="primary" :disabled="loading" @click="onSubmit">确认</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script setup lang="ts" name="AddTeachPlanDialog">
import fuzzySearch from '/@/components/EduComponents/fuzzySearch.vue';
import { useMessage } from '/@/hooks/message';
import { fetchPublishLiveRoomPlan, addTeachPlan, editTeachPlan } from '/@/api/eduConnect/teachPlan';
import { fetchAllPublishCourse } from '/@/api/integraEdu/courseZone';
import { fetchAllTeacher } from '/@/api/eduConnect/teacher';

const emit = defineEmits(['refresh']);
const dataFormRef = ref();
const visible = ref(false);
const loading = ref(false);
const form = reactive({
    id: '',
    liveRoomPlanId: '',
    planName: '',
    courseId: '',
    courseName: '',
    lectureId: '',
    lectureName: '',
});
const dataRules = ref({
    liveRoomPlanId: [{ required: true, message: '直播间计划不能为空', trigger: 'blur' }],
    courseId: [{ required: true, message: '课程不能为空', trigger: 'blur' }],
    lectureId: [{ required: true, message: '主讲老师不能为空', trigger: 'blur' }],
});

// 打开弹窗
const openDialog = (item: any) => {
    form.id = '';
    form.planName = '';
    visible.value = true;
    nextTick(async () => {
        await dataFormRef.value?.resetFields();
        if (item) {
            Object.assign(form, {
                ...item,
                liveRoomPlanId: parseInt(item.liveRoomPlanId),
                courseId: parseInt(item.courseId),
            });
        }
    });
};

const handleCourseChange = (item: any | undefined) => {
    form.courseId = item?.id ?? '';
    form.courseName = item?.courseName ?? '';
};
const handleLectureChange = (item: any | undefined) => {
    form.lectureId = item?.userId ?? '';
    form.lectureName = (item?.name ?? '') + (item?.nickname ? `(${item.nickname})` : '');
};

// 提交
const onSubmit = async () => {
    const valid = await dataFormRef.value.validate().catch(() => {});
    if (!valid) return false;
    const api = form.id ? editTeachPlan : addTeachPlan;
    try {
        loading.value = true;
        await api(form);
        useMessage().success(form.id ? '编辑成功' : '添加成功');
        visible.value = false;
        emit('refresh');
    } catch (err: any) {
        useMessage().error(err.msg);
    } finally {
        loading.value = false;
    }
};

const onClose = () => {
    dataFormRef.value?.resetFields();
};

// 暴露变量
defineExpose({
    open: openDialog,
});
</script>
