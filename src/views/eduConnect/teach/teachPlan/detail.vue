<template>
    <div class="layout-padding">
        <div class="layout-padding-auto layout-padding-view">
            <navi-bar
                ref="naviBarRef"
                class="mb28"
                :title="`直播间计划名称: ${teachPlanData.planName || '-'}`"
                :sub-title="`课程名称: ${teachPlanData.courseName || '-'}`"
                :from="decodeURIComponent(route.query.from as string)"
            >
                <template #extra>
                    <el-button
                        v-auth="'edusystem_TeachingPlanDraft_publish'"
                        icon="promotion"
                        type="primary"
                        :loading="publishLoading"
                        :disabled="!teachPlanData.teachDetails?.length"
                        @click="handlePublish"
                    >
                        发布
                    </el-button>
                </template>
            </navi-bar>
            <el-table
                :data="teachPlanData.teachDetails || []"
                v-loading="state.loading"
                border
                :cell-style="tableStyle.cellStyle"
                :header-cell-style="tableStyle.headerCellStyle"
            >
                <el-table-column type="index" label="序号" width="60" />
                <el-table-column prop="lessonName" label="课节名称" show-overflow-tooltip>
                    <template #default="scope">{{ scope.row.lessonName || '/' }} </template>
                </el-table-column>
                <el-table-column prop="bookName" label="关联书籍" show-overflow-tooltip>
                    <template #default="scope">{{ scope.row.bookName || '/' }} </template>
                </el-table-column>
                <el-table-column prop="courseName" label="上课时段" show-overflow-tooltip>
                    <template #default="scope">
                        <p v-if="scope.row.slotId">
                            {{
                                scope.row.classDate +
                                ' ' +
                                week_type.find((item: any) => item.value == scope.row.classWeek)?.label +
                                ' ' +
                                classTimeArray.find((item: any) => item.id == scope.row.slotId)?.name
                            }}
                        </p>
                        <p v-else>/</p>
                    </template>
                </el-table-column>
                <el-table-column prop="lectureId" label="主讲老师" show-overflow-tooltip>
                    <template #default="scope">
                        <p v-if="scope.row.lectureId">{{ getTeacherName(scope.row.lectureId) }}</p>
                        <p v-else>/</p>
                    </template>
                </el-table-column>
                <el-table-column
                    v-auth="'edusystem_TeachingPlanDetailDraft_edit'"
                    label="操作"
                    width="120"
                    fixed="right"
                >
                    <template #default="scope">
                        <el-button
                            icon="edit"
                            text
                            type="primary"
                            :disabled="!scope.row.classDate || !scope.row.lessonName || isDisabled(scope.row)"
                            @click="handleEdit(scope.row)"
                        >
                            编辑
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>

        <form-dialog ref="formDialogRef" @refresh="loadData" />
    </div>
</template>

<script setup lang="ts">
import NaviBar from '/@/components/NaviBar/index.vue';
import { BasicTableProps, useTable } from '/@/hooks/table';
import { useMessage, useMessageBox } from '/@/hooks/message';
import { fetchTeachPlanInfo, publishTeachPlan } from '/@/api/eduConnect/teachPlan';
import { fetchAllTeacher } from '/@/api/eduConnect/teacher';
import { fetchList } from '/@/api/eduConnect/timePeriod';
import moment from 'moment';
import { useDict } from '/@/hooks/dict';

const FormDialog = defineAsyncComponent(() => import('./components/editDetailForm.vue'));
const formDialogRef = ref();
const route = useRoute();
// const router = useRouter();
const teachPlanData = reactive<any>({});
const teacherArray = ref<any[]>([]);
const classTimeArray = ref<any[]>([]);
const state: BasicTableProps = reactive<BasicTableProps>({
    isPage: false,
    pagination: {},
});
const { tableStyle } = useTable(state);
const publishLoading = ref(false);
const naviBarRef = ref();
const { week_type } = useDict('week_type');

onMounted(() => {
    loadData();
    fetchAllTeacher(1)
        .then((res) => {
            teacherArray.value = res.data || [];
        })
        .catch((err) => {
            useMessage().error(err.msg);
        });
    fetchList()
        .then((res) => {
            classTimeArray.value = res.data || [];
        })
        .catch((err) => {
            useMessage().error(err.msg);
        });
});

const getTeacherName = (id: number) => {
    const teacher = teacherArray.value.find((item: any) => item.userId == id);
    if (teacher) {
        return teacher.name + (teacher.nickname ? `(${teacher.nickname})` : '');
    } else {
        return '/';
    }
};

const isDisabled = (row: any) => {
    const classTimeRange: string = classTimeArray.value.find((item: any) => item.id == row.slotId)?.correspondingTime;
    if (!classTimeRange) return false;
    const arr = classTimeRange.split('-');
    const day = row.classDate;
    if (!day || !arr.length) return false;
    const date = moment(`${day} ${arr[1]}`); // 结束时间
    return date.isBefore(moment());
};

const loadData = async () => {
    if (!route.query.id) {
        useMessage().error('缺少必要的ID参数');
        return;
    }
    try {
        state.loading = true;
        const { data } = await fetchTeachPlanInfo(route.query.id as string);
        Object.assign(teachPlanData, data);
    } catch (error: any) {
        useMessage().error(error?.msg || '加载数据失败');
    } finally {
        state.loading = false;
    }
};

const handlePublish = async () => {
    try {
        await useMessageBox().confirm(
            `当前课节数量【${teachPlanData.lessonCount}】, 上课时段数量【${teachPlanData.planCount}】</br>发布后将无法更换直播间计划及课程包，请谨慎操作!</br>确定继续发布教学计划?`,
            true
        );
    } catch {
        return;
    }
    publishLoading.value = true;
    publishTeachPlan(route.query.id as string, true)
        .then(() => {
            useMessage().success('发布成功');
            naviBarRef.value.back();
        })
        .catch((err) => {
            useMessage().error(err.msg);
        })
        .finally(() => {
            publishLoading.value = false;
        });
};

const handleEdit = (item: any) => {
    formDialogRef.value.open({ ...item, lectureName: getTeacherName(item.lectureId) });
};
</script>

<style lang="scss" scoped></style>
