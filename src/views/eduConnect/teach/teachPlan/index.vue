<template>
    <div class="layout-padding">
        <div class="layout-padding-auto layout-padding-view">
            <el-row v-show="showSearch">
                <el-form :model="state.queryForm" ref="queryRef" :inline="true" @keyup.enter="getDataList">
                    <el-form-item label="直播间计划" prop="liveRoomPlanId">
                        <fuzzy-search
                            v-model="state.queryForm.liveRoomPlanId"
                            :requestUrl="getLivingRoomData"
                            placeholder="请输入直播间计划名称"
                            data-key="id"
                            data-label="planName"
                            requestName="planName"
                            :remote="true"
                        />
                    </el-form-item>
                    <el-form-item label="课程名称" prop="courseName">
                        <el-input
                            v-model="state.queryForm.courseName"
                            placeholder="请输入课程名称"
                            clearable
                            style="width: 200px"
                        />
                    </el-form-item>
                    <el-form-item label="直播间" prop="liveRoomId">
                        <el-select
                            v-model="state.queryForm.liveRoomId"
                            placeholder="请选择直播间"
                            clearable
                            style="width: 200px"
                        >
                            <el-option
                                v-for="item in live_room"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="阶段" prop="stage">
                        <el-select
                            v-model="state.queryForm.stage"
                            placeholder="请选择阶段"
                            clearable
                            style="width: 200px"
                        >
                            <el-option
                                v-for="item in stage_list"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="主讲老师" prop="lectureId">
                        <fuzzy-search
                            v-model="state.queryForm.lectureId"
                            :requestUrl="() => fetchAllTeacher(1)"
                            placeholder="请输入主讲老师名称"
                            data-key="userId"
                            data-label="name"
                            :get-label="(row) => (row.nickname ? `${row.name}(${row.nickname})` : row.name)"
                            :remote="false"
                        />
                    </el-form-item>
                    <el-form-item>
                        <el-button icon="search" type="primary" @click="getDataList"> 查询 </el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-row>
            <el-row>
                <div class="mb8" style="width: 100%">
                    <el-button icon="folder-add" type="primary" @click="formDialogRef.open()"> 添加教学计划 </el-button>
                    <right-toolbar
                        class="ml10 mr20"
                        v-model:showSearch="showSearch"
                        :export="false"
                        style="float: right"
                        @queryTable="getDataList"
                    />
                </div>
            </el-row>
            <el-table
                :data="state.dataList"
                v-loading="state.loading"
                border
                :cell-style="tableStyle.cellStyle"
                :header-cell-style="tableStyle.headerCellStyle"
            >
                <el-table-column type="index" label="序号" width="60" />
                <el-table-column prop="planName" label="直播间计划" width="200" />
                <el-table-column prop="liveRoomId" label="直播间" show-overflow-tooltip>
                    <template #default="scope">
                        {{ live_room.find((item: any) => item.value == scope.row.liveRoomId)?.label || '/' }}
                    </template>
                </el-table-column>
                <el-table-column prop="courseName" label="课程" width="200" />
                <el-table-column prop="stage" label="阶段" show-overflow-tooltip width="100">
                    <template #default="scope">
                        <el-tag type="primary">{{ getStageName(scope.row.stage) }}</el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="teachCount" label="课节数量" show-overflow-tooltip width="100" />
                <el-table-column prop="lectureName" label="主讲老师" show-overflow-tooltip width="130" />
                <el-table-column prop="planStatus" label="发布状态" show-overflow-tooltip width="100">
                    <template #default="scope">
                        <el-tag v-if="scope.row.planStatus == 0" type="warning">未发布</el-tag>
                        <el-tag v-else-if="scope.row.planStatus == 1" type="success">已发布</el-tag>
                        <el-tag v-else type="info">已关闭</el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="updateBy" label="发布人" show-overflow-tooltip>
                    <template #default="scope"> {{ scope.row.publishBy || '/' }} </template>
                </el-table-column>
                <el-table-column prop="updateTime" label="发布时间" min-width="180">
                    <template #default="scope"> {{ scope.row.publishTime || '/' }} </template>
                </el-table-column>
                <el-table-column label="操作" width="200" fixed="right">
                    <template #default="scope">
                        <el-button icon="InfoFilled" text type="primary" @click="handleDetail(scope.row)">
                            详情
                        </el-button>
                        <el-button
                            icon="edit"
                            text
                            type="primary"
                            :disabled="scope.row.planStatus == 1"
                            @click="handleEdit(scope.row)"
                        >
                            编辑
                        </el-button>
                        <el-button icon="delete" text type="primary" @click="handleDelete(scope.row)"> 删除 </el-button>
                    </template>
                </el-table-column>
            </el-table>
            <pagination
                @size-change="sizeChangeHandle"
                @current-change="currentChangeHandle"
                v-bind="state.pagination"
            />
        </div>

        <!-- 编辑、添加  -->
        <form-dialog ref="formDialogRef" @refresh="getDataList(false)" />
    </div>
</template>

<script setup lang="ts" name="TeachPlan">
import fuzzySearch from '/@/components/EduComponents/fuzzySearch.vue';
import { BasicTableProps, useTable } from '/@/hooks/table';
import { fetchAllTeacher } from '/@/api/eduConnect/teacher';
import { fetchTeachPlanList, deleteTeachPlan, getPublishLiveRoomPlan } from '/@/api/eduConnect/teachPlan';
import { useMessage, useMessageBox } from '/@/hooks/message';
import { useDict } from '/@/hooks/dict';
import { useStageDisplay } from '/@/hooks/stage';

const { getStageName } = useStageDisplay();
const FormDialog = defineAsyncComponent(() => import('./components/addForm.vue'));
const formDialogRef = ref();
const router = useRouter();
const queryRef = ref();
const showSearch = ref(true);
const { live_room, stage_list } = useDict('live_room', 'stage_list');
const state: BasicTableProps = reactive<BasicTableProps>({
    queryForm: {
        liveRoomPlanId: '',
        courseName: '',
        liveRoomId: '',
        stage: '',
        lectureId: '',
    },
    pageList: fetchTeachPlanList,
});
const { getDataList, currentChangeHandle, sizeChangeHandle, tableStyle } = useTable(state);

const resetQuery = () => {
    queryRef.value?.resetFields();
    state.queryForm = {
        liveRoomPlanId: '',
        courseName: '',
        liveRoomId: '',
        stage: '',
        lectureId: '',
    };
    getDataList();
};

// 编辑
const handleEdit = (item: any) => {
    formDialogRef.value.open({ ...item });
};
// 详情
const handleDetail = (item: any) => {
    router.push({
        path: '/eduConnect/teach/teachPlan/detail',
        query: { id: item.id, from: encodeURIComponent(router.currentRoute.value.fullPath) },
    });
};
// 删除
const handleDelete = async (item: any) => {
    try {
        await useMessageBox().confirm(
            `删除后不可恢复，请谨慎操作！<br/>确定删除【${item.planName}】教学计划吗？`,
            true
        );
    } catch {
        return;
    }
    deleteTeachPlan(item.id)
        .then(() => {
            useMessage().success('删除成功');
            getDataList();
        })
        .catch((err) => {
            useMessage().error(err.msg);
        });
};

const getLivingRoomData = async (params: any) => {
    const res = await getPublishLiveRoomPlan(params);

    const data = res.data.records.map((item: any) => ({
        id: item.id,
        planName: item.planName,
    }));
    return {
        data,
    };
};
</script>
