<template>
    <el-dialog
        :title="form.id ? '编辑书名' : '添加书名'"
        v-model="visible"
        :close-on-click-modal="false"
        width="600"
        @close="handleDismiss"
    >
        <el-form
            ref="dataFormRef"
            :model="form"
            :rules="dataRules"
            formDialogRef
            label-width="90px"
            v-loading="loading"
        >
            <el-row :gutter="24">
                <el-col :span="24" class="mb20">
                    <el-form-item label="书名" prop="title">
                        <el-input
                            v-model.trim="form.title"
                            placeholder="请输入书名"
                            :disabled="!!form.id"
                            maxlength="50"
                            show-word-limit
                        />
                        <div class="text-gray-400">请务必按照书籍封面上的准确名称,填写书籍名称</div>
                    </el-form-item>
                </el-col>

                <el-col :span="24" class="mb20">
                    <el-form-item label="作者" prop="author">
                        <el-input v-model.trim="form.author" placeholder="请输入作者" maxlength="50" show-word-limit />
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="visible = false">取消</el-button>
                <el-button type="primary" @click="onSubmit" :disabled="loading">确认</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script setup lang="ts" name="BookNameDialog">
import { useMessage, useMessageBox } from '/@/hooks/message';
import { fetchBookNameList, addBookName, editBookName } from '/@/api/integraEdu/bookTrack';
// import { rule } from '/@/utils/validate';

const emit = defineEmits(['refresh', 'version', 'dismiss']);
const dataFormRef = ref();
const visible = ref(false);
const loading = ref(false);

// 提交表单数据
const form = reactive({
    id: '',
    title: '',
    author: '',
    stageId: 0,
});

// 定义校验规则
const dataRules = ref({
    title: [{ required: true, message: '书名不能为空', trigger: 'blur' }],
    author: [{ required: true, message: '作者不能为空', trigger: 'blur' }],
});

// 打开弹窗
const openDialog = (id: string, stageId: number) => {
    visible.value = true;
    form.id = '';
    form.stageId = stageId;
    nextTick(() => {
        dataFormRef.value?.resetFields();
    });
    if (id) {
        form.id = id;
        getBookNameData(id, stageId);
    }
};

// 初始化表单数据
const getBookNameData = (bookId: string, stageId: number) => {
    loading.value = true;
    fetchBookNameList({ bookId, stageId })
        .then((res: any) => {
            const data = res.data.records || [];
            if (data.length > 0) {
                Object.assign(form, data[0]);
            }
        })
        .catch((err) => {
            useMessage().error(err.msg);
        })
        .finally(() => {
            loading.value = false;
        });
};

// 提交
const onSubmit = async () => {
    const valid = await dataFormRef.value.validate().catch(() => {});
    if (!valid) return false;
    try {
        loading.value = true;
        const res = form.id ? await editBookName(form) : await addBookName(form);
        if (res.data && res.data.id) {
            try {
                await useMessageBox().confirm('书名已存在, 是否去管理书籍版本');
            } catch (error) {
                return;
            }
            emit('version', res.data);
        } else {
            useMessage().success(form.id ? '编辑成功' : '添加成功');
            visible.value = false;
            emit('refresh');
        }
    } catch (err: any) {
        useMessage().error(err.msg);
    } finally {
        loading.value = false;
    }
};

const handleDismiss = () => {
    emit('dismiss');
};

// 暴露变量
defineExpose({
    openDialog,
});
</script>
