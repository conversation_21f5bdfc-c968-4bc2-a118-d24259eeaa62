<template>
    <div class="layout-padding">
        <div class="layout-padding-auto layout-padding-view">
            <navi-bar
                class="mb20"
                :title="form.bookVersionId ? '编辑版本' : '添加版本'"
                :sub-title="route.query.bookName as string"
                destroy
                :from="decodeURIComponent(route.query.from as string)"
            />
            <div class="overflow-auto">
                <el-form
                    ref="dataFormRef"
                    class="w-[600px]"
                    :model="form"
                    :rules="dataRules"
                    formDialogRef
                    label-width="90px"
                    v-loading="loading"
                >
                    <el-row :gutter="24">
                        <el-col :span="24" class="mb20">
                            <el-form-item label="ISBN" prop="isbn">
                                <el-input
                                    v-model.trim="form.isbn"
                                    placeholder="请输入ISBN"
                                    maxlength="50"
                                    :disabled="!!form.bookVersionId"
                                />
                            </el-form-item>
                        </el-col>

                        <el-col :span="24" class="mb20">
                            <el-form-item label="出版社" prop="press">
                                <el-input
                                    v-model.trim="form.press"
                                    placeholder="请输入出版社"
                                    maxlength="50"
                                    show-word-limit
                                />
                            </el-form-item>
                        </el-col>

                        <el-col :span="24" class="mb20">
                            <el-form-item label="译者" prop="translator">
                                <el-input
                                    v-model.trim="form.translator"
                                    placeholder="请输入译者"
                                    maxlength="50"
                                    show-word-limit
                                />
                            </el-form-item>
                        </el-col>

                        <el-col :span="24" class="mb20">
                            <el-form-item label="适读阶段" prop="stageId">
                                <el-checkbox-group v-model="form.stageId">
                                    <el-checkbox
                                        class="mb10"
                                        v-for="(item, index) in stageArray"
                                        :key="index"
                                        :label="item.id"
                                        :value="item.id"
                                        border
                                    >
                                        {{ item.stageName }}
                                    </el-checkbox>
                                </el-checkbox-group>
                            </el-form-item>
                        </el-col>

                        <el-col :span="24" class="mb20">
                            <el-form-item label="封面" prop="cover">
                                <image-oss
                                    v-model:file-path="form.cover"
                                    service-name="teaching"
                                    module-name="bookVersion"
                                    :file-type="['jpg', 'png', 'jpeg']"
                                >
                                    <template #tip>
                                        <div>建议比例为：2480×3366像素或1654×2480像素</div>
                                    </template>
                                </image-oss>
                            </el-form-item>
                        </el-col>

                        <el-col :span="24" class="mb20">
                            <el-form-item label="来源" prop="source">
                                <el-radio-group v-model="form.source">
                                    <el-radio :label="1" border>外采</el-radio>
                                    <el-radio :label="2" border>定制</el-radio>
                                </el-radio-group>
                            </el-form-item>
                        </el-col>

                        <el-col :span="24" class="mb20 mt20">
                            <el-form-item>
                                <el-button @click="goBack">取消</el-button>
                                <el-button type="primary" :disabled="loading" @click="onSubmit">确认</el-button>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts" name="BookVersionDialog">
import { useMessage, useMessageBox } from '/@/hooks/message';
import { bookVersionDetail, addBookVersion, editBookVersion } from '/@/api/integraEdu/bookTrack';
import { useStageList } from '/@/hooks/stage';
import NaviBar from '/@/components/NaviBar/index.vue';
import mittBus from '/@/utils/mitt';
import ImageOss from '/@/components/Upload/ImageOss.vue';

const emit = defineEmits(['refresh']);
const router = useRouter();
const route = useRoute();

// 使用stage hooks
const { stages } = useStageList();
const stageArray = computed(() => stages.value);
const dataFormRef = ref();
const loading = ref(false);
const form = reactive({
    bookVersionId: route.query.bookVersionId as string,
    bookId: route.query.bookId as string,
    isbn: '',
    press: '',
    stageId: [parseInt(route.query.stageId as string)],
    translator: '',
    source: 1,
    cover: {
        relativePath: '',
        fullPath: '',
    },
});

const dataRules = ref({
    isbn: [{ required: true, message: 'ISBN不能为空', trigger: 'blur' }],
    press: [{ required: true, message: '出版社不能为空', trigger: 'blur' }],
    stageId: [{ required: true, message: '适读阶段不能为空', trigger: 'change' }],
    source: [{ required: true, message: '来源不能为空', trigger: 'change' }],
    cover: [
        {
            required: true,
            validator: (rule: any, value: typeof form.cover, callback: any) => {
                if (value.relativePath == '') {
                    callback(new Error('封面图片不能为空'));
                } else {
                    callback();
                }
            },
            trigger: 'change',
        },
    ],
});

onMounted(() => {
    // Stage数据通过hooks自动加载，无需手动调用
    if (form.bookVersionId) {
        loading.value = true;
        bookVersionDetail(form.bookVersionId)
            .then((res) => {
                Object.assign(form, res.data);
                form.stageId = res.data.stageId || [];
                form.cover = {
                    relativePath: res.data.cover,
                    fullPath: res.data.coverUrl,
                };
            })
            .catch((err) => {
                useMessage().error(err.msg);
            })
            .finally(() => {
                loading.value = false;
            });
    }
});

// 提交
const onSubmit = async () => {
    const valid = await dataFormRef.value.validate().catch(() => {});
    if (!valid) return false;
    if (!form.bookVersionId) {
        try {
            await useMessageBox().confirm('请再次校验【ISBN】, 如保存后将无法修改！');
        } catch {
            return;
        }
    }
    try {
        loading.value = true;
        const dict: any = { ...form };
        dict.cover = form.cover.relativePath;
        const res = form.bookVersionId ? await editBookVersion(dict) : await addBookVersion(dict);
        if (res.data == true) {
            useMessage().success(form.bookVersionId ? '编辑成功' : '添加成功');
            emit('refresh');
            goBack();
        } else {
            useMessage().error(res.msg);
        }
    } catch (err: any) {
        useMessage().error(err.msg);
    } finally {
        loading.value = false;
    }
};

const goBack = () => {
    router.back();
    mittBus.emit('onCurrentContextmenuClick', { contextMenuClickId: 1, ...route });
};
</script>
