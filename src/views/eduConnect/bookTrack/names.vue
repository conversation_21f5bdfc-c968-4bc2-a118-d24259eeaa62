<template>
    <div class="layout-padding">
        <div class="layout-padding-auto layout-padding-view">
            <navi-bar
                class="mb20"
                :title="`${route.query.stageName as string || ''}书单`"
                :from="decodeURIComponent(route.query.from as string)"
            />
            <el-row v-show="showSearch">
                <el-form :model="state.queryForm" ref="queryRef" :inline="true" @keyup.enter="getDataList">
                    <el-form-item label="书名" prop="title">
                        <el-input placeholder="请输入书名" v-model="state.queryForm.title" />
                    </el-form-item>
                    <el-form-item>
                        <el-button icon="search" type="primary" @click="getDataList"> 查询 </el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-row>
            <el-row>
                <div class="mb8" style="width: 100%">
                    <el-button
                        icon="folder-add"
                        type="primary"
                        class="ml10"
                        @click="formDialogRef.openDialog(0, route.query.id)"
                    >
                        添加书名
                    </el-button>
                    <right-toolbar
                        v-model:showSearch="showSearch"
                        class="ml10 mr20"
                        style="float: right"
                        :export="false"
                        @queryTable="getDataList"
                    />
                </div>
            </el-row>
            <el-table
                :data="state.dataList"
                v-loading="state.loading"
                border
                :cell-style="tableStyle.cellStyle"
                :header-cell-style="tableStyle.headerCellStyle"
            >
                <el-table-column type="index" label="序号" width="60" />
                <el-table-column prop="title" label="书名" show-overflow-tooltip />
                <el-table-column prop="author" label="作者" show-overflow-tooltip />
                <el-table-column prop="versionCount" label="书籍版本数" show-overflow-tooltip />
                <el-table-column prop="updateTime" label="最近更新日期" show-overflow-tooltip />
                <el-table-column label="操作" width="250" fixed="right">
                    <template #default="scope">
                        <el-button icon="box" text type="primary" @click="handleVersion(scope.row)">
                            版本管理
                        </el-button>
                        <el-button
                            icon="edit-pen"
                            text
                            type="primary"
                            @click="formDialogRef.openDialog(scope.row.id, route.query.id)"
                        >
                            编辑
                        </el-button>
                        <el-tooltip
                            class="box-item"
                            content="已有版本时无法删除"
                            placement="top"
                            :disabled="scope.row.versionCount == 0"
                        >
                            <el-button
                                icon="delete"
                                text
                                type="primary"
                                :disabled="scope.row.versionCount > 0"
                                @click="handleDelete(scope.row.id)"
                            >
                                删除
                            </el-button>
                        </el-tooltip>
                    </template>
                </el-table-column>
            </el-table>
            <pagination
                @size-change="sizeChangeHandle"
                @current-change="currentChangeHandle"
                v-bind="state.pagination"
            />
        </div>

        <!-- 编辑、添加  -->
        <form-dialog
            ref="formDialogRef"
            @refresh="getDataList(false)"
            @version="handleVersion"
            @dismiss="dialogDismiss"
        />
    </div>
</template>

<script setup lang="ts">
import { BasicTableProps, useTable } from '/@/hooks/table';
import { fetchBookNameList, deleteBookName } from '/@/api/integraEdu/bookTrack';
import { useMessage, useMessageBox } from '/@/hooks/message';
import NaviBar from '/@/components/NaviBar/index.vue';
import FormDialog from './components/nameForm.vue';
// import mittBus from '/@/utils/mitt';

const route = useRoute();
const router = useRouter();
const formDialogRef = ref();
const queryRef = ref();
const showSearch = ref(true);
const state: BasicTableProps = reactive<BasicTableProps>({
    queryForm: { stageId: route.query.id },
    pageList: fetchBookNameList,
});
const { getDataList, currentChangeHandle, sizeChangeHandle, tableStyle } = useTable(state);

onMounted(async () => {
    if (route.query.isAdd == '1') {
        formDialogRef.value.openDialog(0, route.query.id);
    }
});

onActivated(() => {
    getDataList();
});

// 清空搜索条件
const resetQuery = () => {
    queryRef.value?.resetFields();
    getDataList();
};

// 版本管理
const handleVersion = (row: any) => {
    router.push({
        path: '/eduConnect/bookTrack/versions',
        query: {
            bookId: row.id,
            stageId: route.query.id,
            bookName: row.title,
            from: encodeURIComponent(router.currentRoute.value.fullPath),
        },
    });
};

// 删除
const handleDelete = async (id: string) => {
    try {
        await useMessageBox().confirm('您确定要删除此书名吗? <br/>删除后将无法恢复，且无法再使用该书名！', true);
    } catch {
        return;
    }
    try {
        await deleteBookName(id, route.query.id as string);
        getDataList(true);
        useMessage().success('删除成功');
    } catch (err: any) {
        useMessage().error(err.msg);
    }
};
const dialogDismiss = () => {
    if (route.query.isAdd == '1') {
        const { ...restQuery } = route.query;
        router.replace({
            path: route.path,
            query: { ...restQuery, isAdd: 0 }, // 或者直接不带isAdd参数
        });
    }
};
</script>
