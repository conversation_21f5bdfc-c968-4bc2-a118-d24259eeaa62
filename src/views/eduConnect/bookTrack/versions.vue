<template>
    <div class="layout-padding">
        <div class="layout-padding-auto layout-padding-view">
            <navi-bar
                class="mb20"
                :title="route.query.bookName as string"
                :from="decodeURIComponent(route.query.from as string)"
            />
            <el-row>
                <div class="mb8" style="width: 100%">
                    <el-button icon="folder-add" type="primary" class="ml10" @click="handleEdit()">
                        添加版本
                    </el-button>
                    <right-toolbar
                        class="ml10 mr20"
                        style="float: right"
                        :search="false"
                        :export="false"
                        @queryTable="getDataList"
                    />
                </div>
            </el-row>
            <el-table
                :data="state.dataList"
                v-loading="state.loading"
                border
                :cell-style="tableStyle.cellStyle"
                :header-cell-style="tableStyle.headerCellStyle"
            >
                <el-table-column type="index" label="序号" width="60" />
                <el-table-column prop="isbn" label="ISBN" show-overflow-tooltip />
                <el-table-column prop="press" label="出版社" show-overflow-tooltip />
                <el-table-column prop="translator" label="译者" show-overflow-tooltip>
                    <template #default="scope">
                        {{ scope.row.translator || '/' }}
                    </template>
                </el-table-column>
                <el-table-column prop="stageId" label="适读阶段" show-overflow-tooltip>
                    <template #default="scope">
                        <el-tag
                            class="mr5"
                            v-for="(item, index) in (scope.row.stageId ?? []).sort()"
                            :key="index"
                            type="primary"
                        >
                            {{ bookTrackArray.find((i) => i.id == item)?.stageName }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="source" label="来源" show-overflow-tooltip width="120">
                    <template #default="scope">
                        <el-tag v-if="scope.row.source == 1" type="primary">外采</el-tag>
                        <el-tag v-else type="warning">定制</el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="updateBy" label="最近操作人" show-overflow-tooltip />
                <el-table-column prop="updateTime" label="最近更新日期" show-overflow-tooltip />
                <el-table-column label="操作" width="160" fixed="right">
                    <template #default="scope">
                        <el-button icon="edit-pen" text type="primary" @click="handleEdit(scope.row.id)">
                            编辑
                        </el-button>
                        <el-button icon="delete" text type="primary" @click="handleDelete(scope.row.id)"
                            >删除</el-button
                        >
                    </template>
                </el-table-column>
            </el-table>
            <pagination
                @size-change="sizeChangeHandle"
                @current-change="currentChangeHandle"
                v-bind="state.pagination"
            />
        </div>
    </div>
</template>

<script setup lang="ts" name="systemBookVersion">
import { BasicTableProps, useTable } from '/@/hooks/table';
import { fetchBookVersionList, deleteBookVersion } from '/@/api/integraEdu/bookTrack';
import { useStageList } from '/@/hooks/stage';
import { useMessage, useMessageBox } from '/@/hooks/message';
import NaviBar from '/@/components/NaviBar/index.vue';

const route = useRoute();
const router = useRouter();
const state: BasicTableProps = reactive<BasicTableProps>({
    queryForm: {
        bookId: route.query.bookId as string,
    },
    pageList: fetchBookVersionList,
});
const { getDataList, currentChangeHandle, sizeChangeHandle, tableStyle } = useTable(state);

// 使用stage hooks
const { stages } = useStageList();
const bookTrackArray = computed(() => stages.value);

onMounted(() => {
    // Stage数据通过hooks自动加载，无需手动调用
});

const handleEdit = (id?: string) => {
    router.push({
        path: '/eduConnect/bookTrack/versionEdit',
        query: {
            bookId: state.queryForm.bookId,
            bookName: route.query.bookName,
            stageId: route.query.stageId,
            bookVersionId: id,
            from: encodeURIComponent(router.currentRoute.value.fullPath),
        },
    });
};
// 删除操作
const handleDelete = async (id: string) => {
    try {
        await useMessageBox().confirm('您确定要删除此版本吗？<br/>删除后将无法恢复，且无法再使用该版本！', true);
    } catch {
        return;
    }
    try {
        await deleteBookVersion(state.queryForm.bookId, id);
        getDataList();
        useMessage().success('删除成功');
    } catch (err: any) {
        useMessageBox().error(err.msg);
    }
};
</script>
