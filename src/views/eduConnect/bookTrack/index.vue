<template>
    <div class="layout-padding">
        <el-row :gutter="10" v-loading="loading">
            <el-col v-for="(item, index) in dataArray" :key="index" :xs="12" :sm="8" :md="8" :lg="6" :xl="6">
                <el-card class="cursor-pointer mb10" shadow="hover" @click="handleTrack(item)">
                    <div class="flex flex-col items-center py-[20px]">
                        <div
                            class="w-[60px] h-[60px] flex items-center justify-center text-[20px] font-bold bg-primary text-white rounded-full"
                        >
                            {{ item.stageName }}
                        </div>
                        <div class="mt20 text-gray-500">适用于{{ item.stageName }}阶段课程的书单集合</div>
                    </div>
                    <template #footer>
                        <div class="flex items-center">
                            <el-button class="flex-auto" text size="large" type="primary"> 查看 </el-button>
                            <el-divider direction="vertical" />
                            <el-button
                                class="flex-auto"
                                text
                                size="large"
                                type="primary"
                                @click.stop="handleTrack(item, true)"
                            >
                                添加
                            </el-button>
                        </div>
                    </template>
                </el-card>
            </el-col>
        </el-row>
    </div>
</template>

<script setup lang="ts">
import { useStageList } from '/@/hooks/stage';

const router = useRouter();
const loading = ref(false);

// 使用stage hooks
const { stages, loading: stageLoading } = useStageList();
const dataArray = computed(() => stages.value);

// 同步loading状态
watchEffect(() => {
    loading.value = stageLoading.value;
});

onMounted(() => {
    // Stage数据通过hooks自动加载，无需手动调用
});

const handleTrack = (item: object, isAdd = false) => {
    router.push({
        path: '/eduConnect/bookTrack/names',
        query: { ...item, isAdd: isAdd ? 1 : 0, from: encodeURIComponent(router.currentRoute.value.fullPath) },
    });
};
</script>

<style lang="scss" scoped>
.layout-padding {
    height: auto !important;
}
:deep(.el-card__footer) {
    padding: 0 !important;
}
</style>
