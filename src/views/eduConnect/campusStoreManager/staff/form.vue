<template>
    <el-dialog
        v-model="visible"
        :title="form.id ? '编辑门店用户' : '新增门店用户'"
        :close-on-click-modal="false"
        destroy-on-close
        width="600"
    >
        <el-form
            ref="dataFormRef"
            :model="form"
            :rules="dataRules"
            formDialogRef
            label-width="80px"
            v-loading="loading"
        >
            <el-row :gutter="24">
                <el-col :span="24" class="mb20">
                    <el-form-item label="姓名" prop="name">
                        <el-input v-model.trim="form.name" placeholder="请输入姓名" maxlength="20" />
                    </el-form-item>
                </el-col>
                <el-col :span="24" class="mb20">
                    <el-form-item label="性别" prop="sex">
                        <el-select v-model="form.sex" placeholder="请选择用户性别">
                            <el-option v-for="item in gender" :key="item.id" :value="item.value" :label="item.label" />
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="24" class="mb20">
                    <el-form-item label="手机号" prop="phone">
                        <el-input v-model.trim="form.phone" placeholder="请输入手机号" maxlength="11" />
                    </el-form-item>
                </el-col>
                <el-col :span="24" class="mb20">
                    <el-form-item label="岗位" prop="roleId">
                        <el-select v-model.trim="form.roleId" placeholder="请选择岗位" @change="handleChangeRole">
                            <el-option
                                v-for="item in store_role_all"
                                :key="item.id"
                                :value="item.value"
                                :label="item.label"
                            />
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="24" class="mb20">
                    <el-form-item
                        label="所属门店"
                        prop="storeId"
                        v-if="
                            form.roleId === '2' ||
                            form.roleId === '3' ||
                            form.roleId === '4' ||
                            form.roleId === '5' ||
                            form.roleId === '6'
                        "
                    >
                        <remote-select
                            v-model="form.storeId"
                            :fetch-options="fetchStoreOptions"
                            :fetch-detail="fetchStoreDetail"
                            value-key="id"
                            label-key="campusName"
                            placeholder="请输入门店名称"
                            @change="handleChangeStore"
                        />
                    </el-form-item>
                    <el-form-item label="所属校区" prop="schoolId" v-if="form.roleId === '1'">
                        <remote-select
                            v-model="form.schoolId"
                            :fetch-options="fetchSchoolOptions"
                            :fetch-detail="fetchSchoolDetail"
                            value-key="id"
                            label-key="schoolName"
                            placeholder="请输入校区名称"
                        />
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="visible = false">取消</el-button>
                <el-button type="primary" :loading="loading" @click="onSubmit">确认</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script setup lang="ts" name="StoreStaffDialog">
import { useMessage } from '/@/hooks/message';
import { useDict } from '/@/hooks/dict';
import {
    addStoreStaff,
    getSchoolList,
    getSchoolStoreList,
    updateStoreStaff,
    getSchoolDetail,
    getSchoolStoreDetail,
} from '/@/api/eduConnect/campusStore';
import RemoteSelect from '/@/components/RemoteSelect/index';

interface BaseOption {
    [key: string]: any;
}

const emit = defineEmits(['refresh']);
const { gender, store_role_all } = useDict('gender', 'store_role_all');
const dataFormRef = ref();
const visible = ref(false);
const loading = ref(false);
const form = reactive({
    id: '',
    name: '',
    roleId: '',
    sex: '1',
    storeId: '',
    storeName: '',
    schoolName: '',
    schoolId: '',
    phone: '',
});

const dataRules = ref({
    name: [{ required: true, message: '姓名不能为空', trigger: 'blur' }],
    sex: [{ required: true, message: '性别不能为空', trigger: 'change' }],
    roleId: [{ required: true, message: '岗位不能为空', trigger: 'change' }],
    phone: [
        { required: true, message: '手机号不能为空', trigger: 'change' },
        { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' },
    ],
    storeId: [{ required: true, message: '所属门店不能为空', trigger: 'blur' }],
    schoolId: [{ required: true, message: '所属校区不能为空', trigger: 'blur' }],
});

// 远程搜索方法
const fetchStoreOptions = async (query: string) => {
    const res = await getSchoolStoreList({ campusName: query });
    return res.data.records;
};

const fetchSchoolOptions = async (query: string) => {
    const res = await getSchoolList({ schoolName: query });
    return res.data.records;
};

const fetchStoreDetail = async (id: string) => {
    const res = await getSchoolStoreDetail(id);
    return res.data;
};

const fetchSchoolDetail = async (id: string) => {
    const res = await getSchoolDetail(id);
    return res.data;
};

// 打开弹窗
const openDialog = async (val: any | undefined = undefined) => {
    visible.value = true;
    await nextTick();

    if (dataFormRef.value) {
        dataFormRef.value.clearValidate();
        dataFormRef.value.resetFields();
    }

    if (val) {
        Object.assign(form, {
            ...val,
            sex: val.sex + '',
            schoolName: val.schoolName,
            storeName: val.campusName,
        });
    } else {
        Object.assign(form, {
            id: '',
            name: '',
            roleId: '',
            sex: '1',
            storeId: '',
            storeName: '',
            schoolName: '',
            schoolId: '',
            phone: '',
        });
    }
};

// 提交
const onSubmit = async () => {
    try {
        if (!dataFormRef.value) return false;
        const isValid = await dataFormRef.value.validate();
        if (!isValid) return false;

        const formData = {
            ...(form.id && { id: form.id }),
            name: form.name,
            roleId: form.roleId,
            sex: form.sex,
            storeId: form.storeId,
            schoolId: form.schoolId,
            phone: form.phone,
        };

        loading.value = true;
        const action = form.id ? updateStoreStaff : addStoreStaff;
        await action(formData);

        useMessage().success(form.id ? '更新成功' : '添加成功');
        visible.value = false;
        emit('refresh');
        return true;
    } catch (error: any) {
        const errorMessage = error.msg ?? '请输入完整的门店用户信息！';
        useMessage().error(errorMessage);
        return false;
    } finally {
        loading.value = false;
    }
};

// 选择角色
const handleChangeRole = () => {
    form.storeId = '';
    form.schoolId = '';
};

const handleChangeStore = (value: any, option: BaseOption) => {
    if (option && 'schoolId' in option) {
        form.schoolId = option.schoolId;
    }
};

defineExpose({
    openDialog,
});
</script>
