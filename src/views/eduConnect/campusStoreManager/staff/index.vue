<template>
    <div class="layout-padding">
        <div class="layout-padding-auto layout-padding-view">
            <el-row v-show="showSearch">
                <el-form :model="state.queryForm" ref="queryRef" :inline="true" @keyup.enter="getDataList">
                    <el-form-item label="门店名称" prop="storeId">
                        <fuzzy-search
                            class="w-96"
                            placeholder="请输入门店名称"
                            v-model="state.queryForm.storeId"
                            :requestUrl="getStoreSelectList"
                            request-name="campusName"
                            data-label="campusName"
                            clearable
                        ></fuzzy-search>
                    </el-form-item>
                    <el-form-item label="姓名" prop="name">
                        <el-input placeholder="请输入姓名" v-model.trim="state.queryForm.name" />
                    </el-form-item>
                    <el-form-item label="手机号" prop="phone">
                        <el-input placeholder="请输入手机号" v-model.trim="state.queryForm.phone" />
                    </el-form-item>
                    <el-form-item>
                        <el-button icon="search" type="primary" @click="getDataList">查询</el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-row>
            <el-row>
                <div class="mb8" style="width: 100%">
                    <el-button class="ml10" icon="folder-add" type="primary" @click="formDialogRef.openDialog()">
                        新增门店用户
                    </el-button>
                    <right-toolbar
                        class="ml10 mr20"
                        v-model:showSearch="showSearch"
                        :export="false"
                        style="float: right"
                        @queryTable="getDataList"
                    />
                </div>
            </el-row>
            <el-table
                :data="state.dataList"
                v-loading="state.loading"
                border
                :cell-style="tableStyle.cellStyle"
                :header-cell-style="tableStyle.headerCellStyle"
            >
                <el-table-column type="index" label="序号" width="60" />
                <el-table-column prop="name" label="姓名" show-overflow-tooltip min-width="90" />
                <el-table-column prop="sex" label="性别" show-overflow-tooltip width="80">
                    <template #default="scope">
                        <dict-tag :options="gender" :value="scope.row.sex" />
                    </template>
                </el-table-column>
                <el-table-column prop="phone" label="手机号" show-overflow-tooltip width="120" />
                <el-table-column prop="roleId" label="岗位" width="100">
                    <template #default="scope">
                        <dict-tag :options="store_role_all" :value="scope.row.roleId" />
                    </template>
                </el-table-column>
                <el-table-column prop="campusName" label="所属门店" show-overflow-tooltip min-width="120">
                    <template #default="scope">
                        <span v-if="scope.row.campusName">
                            {{ scope.row.campusName }}
                        </span>
                        <span v-else>--</span>
                    </template>
                </el-table-column>
                <el-table-column prop="schoolName" label="所属校区" show-overflow-tooltip min-width="120">
                    <template #default="scope">
                        <span v-if="scope.row.schoolName">
                            {{ scope.row.schoolName }}
                        </span>
                        <span v-else>--</span>
                    </template>
                </el-table-column>
                <el-table-column prop="regionId" label="所属大区" show-overflow-tooltip width="100">
                    <template #default="scope">
                        <dict-tag :options="region" :value="scope.row.regionId"></dict-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="disable" label="账号状态" show-overflow-tooltip width="100">
                    <template #default="scope">
                        <el-switch
                            :model-value="scope.row.isEnable"
                            class="ml-2"
                            inline-prompt
                            active-text="启用"
                            inactive-text="禁用"
                            :active-value="0"
                            :inactive-value="1"
                            @change="handleSwitchChange(scope.row)"
                        />
                    </template>
                </el-table-column>
                <el-table-column prop="createTime" label="创建日期" show-overflow-tooltip min-width="120" />
                <el-table-column prop="createBy" label="创建人" show-overflow-tooltip />
                <el-table-column label="操作" width="120" fixed="right">
                    <template #default="scope">
                        <el-button text type="primary" icon="edit" @click="formDialogRef.openDialog(scope.row)">
                            编辑
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
            <pagination
                @size-change="sizeChangeHandle"
                @current-change="currentChangeHandle"
                v-bind="state.pagination"
            />
        </div>

        <!-- 编辑、添加  -->
        <form-dialog ref="formDialogRef" @refresh="getDataList(false)" />
    </div>
</template>

<script setup lang="ts" name="staff">
import { BasicTableProps, useTable } from '/@/hooks/table';
import { getSchoolStoreList, getStoreStaffList, updateStoreStaffStatus } from '/@/api/eduConnect/campusStore';
import { useDict } from '/@/hooks/dict';
import { useMessage, useMessageBox } from '/@/hooks/message';

const FormDialog = defineAsyncComponent(() => import('./form.vue'));
const FuzzySearch = defineAsyncComponent(() => import('/@/components/EduComponents/fuzzySearch.vue'));

const formDialogRef = ref();
const queryRef = ref();
const showSearch = ref(true);
const state: BasicTableProps = reactive<BasicTableProps>({
    queryForm: {},
    pageList: getStoreStaffList,
    dataList: [],
});
const { getDataList, currentChangeHandle, sizeChangeHandle, tableStyle } = useTable(state);
const { gender, store_role_all, region } = useDict('gender', 'store_role_all', 'region');

// 清空搜索条件
const resetQuery = () => {
    queryRef.value?.resetFields();
    getDataList();
};
// 修改状态
const handleSwitchChange = async (val: any) => {
    try {
        const flag = val.isEnable === 1;
        await useMessageBox().confirm(
            `该账号目前为${flag ? '禁用' : '启用'}状态，是否${flag ? '启用' : '禁用'}该账号？`
        );
        const params = {
            id: val.id,
            isEnable: flag ? 0 : 1,
        };
        await updateStoreStaffStatus(params);
        useMessage().success(`账号${flag ? '启用' : '禁用'}成功`);
        await getDataList();
    } catch (err: any) {
        err.msg && useMessage().error(err.msg);
        return;
    }
};

const getStoreSelectList = async (params: {}) => {
    const res = await getSchoolStoreList(params);
    return { data: res.data.records };
};
</script>
