<template>
    <div class="layout-padding">
        <div class="layout-padding-auto layout-padding-view">
            <el-row v-show="showSearch">
                <el-form :model="state.queryForm" ref="queryRef" :inline="true" @keyup.enter="getDataList">
                    <el-form-item label="门店名称" prop="storeId">
                        <fuzzy-search
                            class="w-96"
                            placeholder="请输入门店名称"
                            v-model="state.queryForm.storeId"
                            :requestUrl="getStoreSelectList"
                            request-name="campusName"
                            data-label="campusName"
                            clearable
                        ></fuzzy-search>
                    </el-form-item>
                    <el-form-item label="姓名" prop="name">
                        <el-input placeholder="请输入姓名" v-model.trim="state.queryForm.name" />
                    </el-form-item>
                    <el-form-item label="手机号" prop="phone">
                        <el-input placeholder="请输入手机号" v-model.trim="state.queryForm.phone" />
                    </el-form-item>
                    <el-form-item>
                        <el-button icon="search" type="primary" @click="getDataList"> 查询 </el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-row>
            <el-row>
                <div class="mb8" style="width: 100%">
                    <right-toolbar
                        class="ml10 mr20"
                        v-model:showSearch="showSearch"
                        :export="false"
                        style="float: right"
                        @queryTable="getDataList"
                    />
                </div>
            </el-row>
            <el-table
                :data="state.dataList"
                v-loading="state.loading"
                border
                :cell-style="tableStyle.cellStyle"
                :header-cell-style="tableStyle.headerCellStyle"
            >
                <el-table-column type="index" label="序号" width="60" />
                <el-table-column prop="name" label="姓名" show-overflow-tooltip min-width="100" />
                <el-table-column prop="sex" label="性别" show-overflow-tooltip width="80">
                    <template #default="scope">
                        <dict-tag :options="gender" :value="scope.row.sex" />
                    </template>
                </el-table-column>
                <!-- <el-table-column prop="courseName" label="家长姓名" show-overflow-tooltip /> -->
                <el-table-column prop="phone" label="手机号" show-overflow-tooltip min-width="130" />
                <el-table-column prop="stageId" label="阶段" show-overflow-tooltip width="110">
                    <template #default="scope">
                        <dict-tag :options="stage_list" :value="scope.row.stageId"></dict-tag>
                    </template>
                </el-table-column>
                <!-- <el-table-column prop="courseName" label="学号" show-overflow-tooltip /> -->
                <el-table-column prop="storeName" label="所属门店" show-overflow-tooltip width="180" />
                <el-table-column prop="schoolName" label="所属校区" show-overflow-tooltip min-width="100" />
                <el-table-column prop="regionName" label="所属大区" show-overflow-tooltip width="100">
                    <template #default="scope">
                        <dict-tag :options="region" :value="scope.row.regionId"></dict-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="courseHours" label="剩余课时" show-overflow-tooltip width="90" />
                <el-table-column prop="isRegularStudents" label="类型" show-overflow-tooltip width="80">
                    <template #default="scope">
                        <span>{{ memberStatusMap[scope.row.isRegularStudents] }}</span>
                    </template>
                </el-table-column>

                <el-table-column prop="status" label="状态" show-overflow-tooltip width="100">
                    <template #default="scope">
                        <dict-tag :options="student_status" :value="scope.row.status"></dict-tag>
                    </template>
                </el-table-column>
                <!-- <el-table-column prop="disable" label="账号状态" show-overflow-tooltip width="100">
                    <template #default="scope">
                        <el-switch
                            :value="scope.row.disable"
                            class="ml-2"
                            inline-prompt
                            active-text="启用"
                            inactive-text="禁用"
                            :active-value="0"
                            :inactive-value="1"
                            @change="handleSwitchChange(scope.row)"
                        />
                    </template>
                </el-table-column> -->
                <el-table-column prop="createTime" label="创建日期" show-overflow-tooltip min-width="120" />
                <el-table-column prop="createBy" label="创建人" show-overflow-tooltip min-width="90" />
            </el-table>
            <pagination
                @size-change="sizeChangeHandle"
                @current-change="currentChangeHandle"
                v-bind="state.pagination"
            />
        </div>
    </div>
</template>

<script setup lang="ts" name="member">
import { BasicTableProps, useTable } from '/@/hooks/table';
import { getSchoolStoreList, getStudentList } from '/@/api/eduConnect/campusStore';
// import { useMessageBox } from '/@/hooks/message';
import { useDict } from '/@/hooks/dict';
const FuzzySearch = defineAsyncComponent(() => import('/@/components/EduComponents/fuzzySearch.vue'));
const memberStatusMap: Record<number, string> = {
    0: '试听',
    1: '正式',
    2: '意向',
};

const queryRef = ref();
const showSearch = ref(true);
const state: BasicTableProps = reactive<BasicTableProps>({
    queryForm: {},
    pageList: getStudentList,
    dataList: [],
});
const { getDataList, currentChangeHandle, sizeChangeHandle, tableStyle } = useTable(state);
const { gender, stage_list, student_status, region } = useDict('gender', 'stage_list', 'student_status', 'region');

// 清空搜索条件
const resetQuery = () => {
    queryRef.value?.resetFields();
    getDataList();
};

const getStoreSelectList = async (params: {}) => {
    const res = await getSchoolStoreList(params);
    return { data: res.data.records };
};
// 修改状态
// const handleSwitchChange = async (val: any) => {
//     try {
//         await useMessageBox().confirm(
//             `该账号目前为${val.disable ? '禁用' : '启用'}状态，是否${val.disable ? '启用' : '禁用'}该账号？`
//         );
//     } catch {
//         return;
//     }
//     // TODO: 修改状态
// };
</script>
