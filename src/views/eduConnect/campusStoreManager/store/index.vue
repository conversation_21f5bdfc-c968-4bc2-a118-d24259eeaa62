<template>
    <div class="layout-padding">
        <div class="layout-padding-auto layout-padding-view">
            <el-row>
                <el-form
                    :inline="true"
                    :model="state.queryForm"
                    ref="queryRef"
                    class="demo-form-inline"
                    v-show="showSearch"
                    @keyup.enter="getDataList"
                >
                    <el-form-item label="所属大区">
                        <el-select v-model="state.queryForm.regionId" placeholder="请选择所属大区" clearable>
                            <el-option
                                v-for="item in region"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="门店名称">
                        <el-input v-model="state.queryForm.campusName" placeholder="请输入门店名称" clearable />
                    </el-form-item>
                    <el-form-item>
                        <el-button icon="search" type="primary" @click="getDataList">
                            {{ $t('common.queryBtn') }}
                        </el-button>
                        <el-button icon="Refresh" @click="resetQuery">{{ $t('common.resetBtn') }}</el-button>
                    </el-form-item>
                </el-form>
                <div class="mb8" style="width: 100%"></div>
            </el-row>
            <el-row type="flex" justify="space-between" class="mb8">
                <el-button icon="folder-add" type="primary" @click="openAddPage">新增</el-button>
                <right-toolbar
                    v-model:showSearch="showSearch"
                    :export="false"
                    style="float: right"
                    @queryTable="getDataList"
                />
            </el-row>
            <el-table
                :data="state.dataList"
                v-loading="state.loading"
                border
                :cell-style="tableStyle.cellStyle"
                :header-cell-style="tableStyle.headerCellStyle"
                @sort-change="sortChangeHandle"
            >
                <el-table-column type="index" label="序号" width="60" />
                <el-table-column prop="campusName" label="门店名称" show-overflow-tooltip width="160" />
                <el-table-column prop="campusNo" label="门店编号" show-overflow-tooltip width="160" />
                <el-table-column prop="regionId" label="所属大区" show-overflow-tooltip width="100">
                    <template #default="scope">
                        <dict-tag :options="region" :value="scope.row.regionId"></dict-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="schoolName" label="所属校区" show-overflow-tooltip width="160" />
                <el-table-column prop="area" label="地区" show-overflow-tooltip width="200">
                    <template #default="scope">
                        <span v-if="scope.row.provinceName || scope.row.cityName || scope.row.districtName">
                            {{ scope.row.provinceName }}
                            {{ scope.row.cityName ? '/' + scope.row.cityName : '' }}
                            {{ scope.row.districtName ? '/' + scope.row.districtName : '' }}
                        </span>
                        <span v-else>--</span>
                    </template>
                </el-table-column>
                <el-table-column prop="campusState" label="店长姓名" show-overflow-tooltip width="120">
                    <template #default="scope">
                        <div v-if="scope.row.principal && scope.row.principal.length > 0">
                            <div v-for="item in scope.row.principal" :key="item.userId" class="cell-split">
                                {{ item.manageName }}
                            </div>
                        </div>
                        <span v-else>--</span>
                    </template>
                </el-table-column>
                <el-table-column prop="campusType" label="店长手机号" show-overflow-tooltip width="150">
                    <template #default="scope">
                        <div v-if="scope.row.principal && scope.row.principal.length > 0">
                            <div v-for="item in scope.row.principal" :key="item.userId" class="cell-split">
                                {{ item.managePhone || '--' }}
                            </div>
                        </div>
                        <span v-else>--</span>
                    </template>
                </el-table-column>
                <el-table-column prop="trialMember" label="试听学员" show-overflow-tooltip width="90" />
                <el-table-column prop="formalMember" label="正式学员" show-overflow-tooltip width="90" />
                <el-table-column prop="staffCount" label="员工数量" show-overflow-tooltip width="90" />
                <el-table-column prop="servicePeriodStart" label="合作协议运营服务期" show-overflow-tooltip width="200">
                    <template #default="scope">
                        <span v-if="scope.row.servicePeriodStart && scope.row.servicePeriodEnd">
                            {{ `${scope.row.servicePeriodStart} 至 ${scope.row.servicePeriodEnd}` }}
                        </span>
                        <span v-else>--</span>
                    </template>
                </el-table-column>
                <el-table-column prop="createTime" label="创建时间" show-overflow-tooltip width="150" />
                <el-table-column prop="updateBy" label="创建人" show-overflow-tooltip width="100" />
                <el-table-column label="操作" width="200" fixed="right">
                    <template #default="scope">
                        <el-button icon="edit-pen" text type="primary" @click="openEditPage(scope.row.id)">
                            编辑
                        </el-button>
                        <el-button
                            icon="edit"
                            text
                            type="primary"
                            @click="changeStoreFee(scope.row.id)"
                            v-loading.fullscreen.lock="fullscreenLoading"
                        >
                            修改门店收费标准
                        </el-button>
                        <el-button icon="link" text type="primary" @click="openClassFeePage(scope.row.id)">
                            课时费明细
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
            <pagination
                @size-change="sizeChangeHandle"
                @current-change="currentChangeHandle"
                v-bind="state.pagination"
            />
        </div>
        <class-fee-form
            ref="classFeeFormRef"
            title="修改门店收费标准"
            warning-text="修改后的收费标准会在课时费明细中更新"
            @submit="editSubmit"
        ></class-fee-form>
    </div>
</template>

<script setup lang="ts" name="systemCampus">
import { BasicTableProps, useTable } from '/@/hooks/table';
import { getSchoolStoreList } from '/@/api/eduConnect/campusStore';
// import { useMessage, useMessageBox } from '/@/hooks/message';
import { useDict } from '/@/hooks/dict';
import { useRouter } from 'vue-router';
import classFeeForm from './components/classFeeForm.vue';
import { getCurrentCourseFeeByStoreApi, updateCourseFeeByStoreIdApi } from '/@/api/eduConnect/classType';
import { useMessage } from '/@/hooks/message';
import { formatToTwoDecimals } from '/@/utils/classFee';

const router = useRouter();
const classFeeFormRef = ref();
// 定义查询字典
const { region } = useDict('region');

// 搜索变量
const queryRef = ref();
const showSearch = ref(true);
const fullscreenLoading = ref(false);

const state: BasicTableProps = reactive<BasicTableProps>({
    queryForm: {
        regionId: '',
        campusName: '',
    },
    pageList: getSchoolStoreList,
});
interface OpenData {
    id?: string;
    courseId?: string;
    standardPrice?: number;
    effectiveDate?: string;
}
const changeStoreFee = async (id: string) => {
    try {
        fullscreenLoading.value = true;
        let res = await getCurrentCourseFeeByStoreApi({ id });
        classFeeFormRef.value.openDialog({
            id,
            standardPrice: formatToTwoDecimals(res.data.standardPrice),
            effectiveDate: res.data.effectiveDate,
        });
    } catch (error: any) {
        useMessage().error(error?.msg || '请稍后重试');
    } finally {
        fullscreenLoading.value = false;
    }
};
//  table hook
const { getDataList, currentChangeHandle, sizeChangeHandle, sortChangeHandle, tableStyle } = useTable(state);

// 清空搜索条件
const resetQuery = () => {
    // 清空搜索条件
    queryRef.value?.resetFields();
    state.queryForm = {
        regionId: '',
        campusName: '',
    };
    getDataList();
};
const editSubmit = (form: OpenData) => {
    updateCourseFeeByStoreIdApi(form)
        .then(() => {
            useMessage().success('修改成功');
            classFeeFormRef.value.closeDialog();
        })
        .catch((err) => {
            useMessage().error(err.msg);
        });
};
const openAddPage = () => {
    router.push('/eduConnect/campusStoreManager/store/add');
};

const openEditPage = (id: string) => {
    router.push(`/eduConnect/campusStoreManager/store/edit?id=${id}`);
};
const openClassFeePage = (id: string) => {
    router.push(`/eduConnect/campusStoreManager/store/classFee?id=${id}`);
};
</script>

<style lang="scss" scoped>
.cell-split {
    border-bottom: 1px solid #ccc;
    &:last-child {
        border-bottom: none;
    }
}
</style>
