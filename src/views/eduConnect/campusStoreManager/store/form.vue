<!-- form.vue -->
<template>
    <el-form ref="formRef" :model="formData" :rules="rules" label-position="top" class="basic-form">
        <el-row :gutter="20">
            <el-col :span="12" class="form-item">
                <el-form-item label="门店名称" prop="campusName">
                    <el-input
                        show-word-limit
                        maxlength="20"
                        v-model="formData.campusName"
                        placeholder="请输入门店名称"
                    />
                </el-form-item>
            </el-col>
            <el-col :span="12" class="form-item">
                <el-form-item label="所属校区" prop="schoolId">
                    <fuzzy-search
                        v-if="mode === 'add'"
                        class="w-96"
                        placeholder="请输入校区名称"
                        v-model="formData.schoolId"
                        :requestUrl="getSchoolSelectList"
                        request-name="schoolName"
                        data-label="schoolName"
                        clearable
                        @change="handleChangeSchool"
                    ></fuzzy-search>
                    <el-input v-else v-model="formData.schoolName" disabled></el-input>
                </el-form-item>
            </el-col>
            <el-col :span="12" class="form-item">
                <el-form-item label="所属大区" prop="regionId">
                    <el-select v-model="formData.regionId" disabled>
                        <el-option
                            placeholder="请选择校区"
                            v-for="item in region"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        />
                    </el-select>
                </el-form-item>
            </el-col>
            <el-col :span="12" class="form-item">
                <el-form-item label="地区" prop="area">
                    <china-area
                        style="width: 100%"
                        :type="3"
                        placeholder="请选择地区"
                        v-model="formData.area"
                        :plus="true"
                        @change-with-detail="handleAreaChange"
                    />
                </el-form-item>
            </el-col>
            <el-col :span="12" class="form-item">
                <el-form-item label="门店类型" prop="campusMode">
                    <el-select v-model="formData.campusMode" placeholder="请选择签约主体类型">
                        <el-option
                            v-for="item in campus_mode"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        />
                    </el-select>
                </el-form-item>
            </el-col>
            <el-col :span="12" class="form-item">
                <el-form-item label="签约主体类型" prop="signingType">
                    <el-select v-model="formData.signingType" placeholder="请选择签约主体类型">
                        <el-option
                            v-for="item in contracting_entity_type"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        />
                    </el-select>
                </el-form-item>
            </el-col>
            <el-col :span="12" class="form-item">
                <el-form-item label="签约主体" prop="signingSubject">
                    <el-input
                        show-word-limit
                        maxlength="100"
                        v-model="formData.signingSubject"
                        placeholder="请输入签约主体"
                    />
                </el-form-item>
            </el-col>
            <el-col :span="12" class="form-item">
                <el-form-item label="签约主体手机号" prop="signingSubjectPhone">
                    <el-input
                        v-model="formData.signingSubjectPhone"
                        maxlength="11"
                        placeholder="请输入签约主体手机号"
                    />
                </el-form-item>
            </el-col>
            <el-col :span="12" class="form-item">
                <el-form-item label="合作协议运营服务期" prop="serviceTime">
                    <el-date-picker
                        v-model="formData.serviceTime"
                        type="daterange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                    />
                </el-form-item>
            </el-col>
            <el-col :span="12" class="form-item">
                <el-form-item label="课时费标准" prop="studentPrice">
                    <div class="flex items-center">
                        <custom-input-number
                            v-model="formData.studentPrice"
                            :disabled="isEdit"
                            placeholder="请输入课时费标准"
                        ></custom-input-number>
                        <span class="flex-shrink-0 ml-5">元/人次</span>
                    </div>
                </el-form-item>
            </el-col>
        </el-row>
    </el-form>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import type { FormInstance } from 'element-plus';
import { useDict } from '/@/hooks/dict';
import { getSchoolList } from '/@/api/eduConnect/campusStore';
import customInputNumber from '/@/components/custom-input-number/index.vue';

const { region, contracting_entity_type, campus_mode } = useDict('region', 'contracting_entity_type', 'campus_mode');

const ChinaArea = defineAsyncComponent(() => import('/@/components/ChinaArea/index.vue'));
const FuzzySearch = defineAsyncComponent(() => import('/@/components/EduComponents/fuzzySearch.vue'));

// 定义表单数据接口
interface FormData {
    campusName: string;
    schoolId: string;
    schoolName: string;
    regionId: string;
    area: string;
    signingType: string;
    signingSubject: string;
    signingSubjectPhone: string;
    provinceCode: string;
    cityCode: string;
    districtCode: string;
    serviceTime: [Date, Date] | null;
    campusMode: string;
    studentPrice: number;
}

const props = defineProps({
    initialData: {
        type: Object,
        default: () => ({}),
    },
    mode: {
        type: String,
        default: 'add',
    },
    isEdit: {
        type: Boolean,
        default: false,
    },
});
// const emit = defineEmits(['validate']);

const formRef = ref<FormInstance>();
const formData = ref<FormData>({
    campusName: '',
    schoolId: '',
    schoolName: '',
    regionId: '',
    area: '',
    signingType: '',
    signingSubject: '',
    signingSubjectPhone: '',
    provinceCode: '',
    cityCode: '',
    districtCode: '',
    serviceTime: null,
    campusMode: '',
    studentPrice: 0,
});

watch(
    () => props.initialData,
    (newVal) => {
        if (newVal) {
            // 使用Object.assign来合并数据，保持响应性
            Object.assign(formData.value, newVal);
        }
    },
    { immediate: true, deep: true }
);

// 自定义验证函数
const validateServiceTime = (rule: any, value: [Date, Date] | null, callback: any) => {
    if (!value) {
        callback(new Error('请选择服务期限'));
    } else {
        const endDate = value[1];
        const now = new Date();
        if (endDate < now) {
            callback(new Error('结束时间不能早于当前时间'));
        } else {
            callback();
        }
    }
};

// 表单验证规则
const rules = {
    campusName: [{ required: true, message: '请输入门店名称', trigger: 'blur' }],
    schoolId: [{ required: true, message: '请输入所属校区', trigger: 'blur' }],
    regionId: [{ required: true, message: '请选择所属大区', trigger: 'change' }],
    area: [{ required: true, message: '请选择地区', trigger: 'change' }],
    signingType: [{ required: true, message: '请选择签约主体类型', trigger: 'change' }],
    campusMode: [{ required: true, message: '请选择门店类型', trigger: 'change' }],
    signingSubject: [{ required: true, message: '请输入签约主体', trigger: 'blur' }],
    signingSubjectPhone: [
        { required: true, message: '请输入签约主体手机号', trigger: 'blur' },
        { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' },
    ],
    serviceTime: [
        { required: true, message: '请选择服务期限', trigger: 'change' },
        { validator: validateServiceTime, trigger: 'change' },
    ],
    studentPrice: [{ required: true, message: '请输入课时费标准', trigger: 'change' }],
};

const handleAreaChange = (areaInfo = { provinceCode: '', cityCode: '', districtCode: '' }) => {
    const { provinceCode, cityCode, districtCode } = areaInfo;
    formData.value.provinceCode = provinceCode;
    formData.value.cityCode = cityCode;
    formData.value.districtCode = districtCode;
    const validCodes = [provinceCode, cityCode, districtCode].filter((code) => code);
    formData.value.area = validCodes.join(',');
};

const getSchoolSelectList = async (params: {}) => {
    const res = await getSchoolList(params);
    return { data: res.data.records };
};

const handleChangeSchool = (schoolData: Record<string, any>) => {
    if (!schoolData) {
        formData.value.regionId = '';
        formData.value.area = '';
        formData.value.provinceCode = '';
        formData.value.cityCode = '';
        formData.value.districtCode = '';
        return;
    }
    const { provinceCode, cityCode, districtCode, regionId } = schoolData;
    formData.value.regionId = regionId + '';
    const validCodes = [provinceCode, cityCode, districtCode].filter((code) => code);
    formData.value.area = validCodes.join(',');
    formData.value.provinceCode = provinceCode;
    formData.value.cityCode = cityCode;
    formData.value.districtCode = districtCode;
};
// 暴露方法给父组件
defineExpose({
    formRef,
    formData,
    rules,
});
</script>

<style lang="scss" scoped>
.basic-form {
    max-width: 600px;
    .form-item {
        margin-bottom: 20px;
    }
}
</style>
