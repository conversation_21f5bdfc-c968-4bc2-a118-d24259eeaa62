<template>
    <el-dialog v-model="visible" title="课时费生效记录" :close-on-click-modal="true" style="width: 800px">
        <el-descriptions>
            <el-descriptions-item label="课程名称">{{ showData.courseName }}</el-descriptions-item>
            <el-descriptions-item label="课程类型">{{ showData.courseTypeName }}</el-descriptions-item>
        </el-descriptions>
        <el-table
            :data="state.dataList"
            v-loading="state.loading"
            border
            :cell-style="tableStyle.cellStyle"
            :header-cell-style="tableStyle.headerCellStyle"
            @sort-change="sortChangeHandle"
        >
            <el-table-column type="index" label="序号" width="60" />
            <el-table-column prop="standardPrice" label="课时费标准" show-overflow-tooltip>
                <template #default="scope">
                    {{ setCourseFee(scope.row.standardPrice, scope.row.chargeMethod == ChargeMethodEnum.shop) }}
                </template>
            </el-table-column>
            <el-table-column prop="effectiveDate" label="生效日期" show-overflow-tooltip />
            <el-table-column prop="updateTime" label="操作时间" show-overflow-tooltip />
            <el-table-column prop="updateBy" label="操作人" show-overflow-tooltip />
        </el-table>
    </el-dialog>
</template>

<script lang="ts" setup>
import { getCourseFeeListByStoreApi } from '/@/api/eduConnect/classType';
import { BasicTableProps, useTable } from '/@/hooks/table';
import { ChargeMethodEnum, setCourseFee } from '/@/utils/classFee';
const state: BasicTableProps = reactive<BasicTableProps>({
    queryForm: {
        courseId: '',
        id: '',
    },
    isPage: false,
    createdIsNeed: false,
    pageList: getCourseFeeListByStoreApi,
});
const showData = ref<{ courseName: string; courseTypeName: string }>({
    courseName: '',
    courseTypeName: '',
});
const visible = ref(false);
const { getDataList, sortChangeHandle, tableStyle } = useTable(state);

const openDialog = (data?: { courseId: string; id: string; courseName: string; courseTypeName: string }) => {
    visible.value = true;
    state.queryForm.courseId = data?.courseId;
    state.queryForm.id = data?.id;
    showData.value.courseName = data?.courseName || '';
    showData.value.courseTypeName = data?.courseTypeName || '';
    getDataList();
};
defineExpose({
    openDialog,
});
</script>

<style lang="scss" scoped></style>
