<template>
    <el-dialog v-model="visible" :title="title" :close-on-click-modal="false" style="width: 550px">
        <el-form
            ref="dataFormRef"
            :inline="true"
            :model="form"
            :rules="dataRules"
            label-width="120px"
            label-position="left"
        >
            <el-row :gutter="24">
                <el-col :span="24" class="mb20">
                    <el-form-item label="课时费标准：" prop="standardPrice">
                        <div class="flex items-center">
                            <custom-input-number
                                v-model="form.standardPrice"
                                placeholder="请输入课时费标准"
                            ></custom-input-number>
                            <span class="flex-shrink-0 ml-5">元/人次</span>
                        </div>
                    </el-form-item>
                </el-col>
                <el-col :span="24" class="mb20">
                    <el-form-item label="生效日期：" prop="effectiveDate" class="w-full">
                        <el-date-picker
                            v-model="form.effectiveDate"
                            format="YYYY-MM-DD"
                            value-format="YYYY-MM-DD"
                            type="date"
                            placeholder="请选择"
                        />
                    </el-form-item>
                </el-col>
                <el-col :span="24">
                    <p><span class="text-[#f56c6c]">*</span> {{ warningText }}</p>
                </el-col>
            </el-row>
        </el-form>
        <template #footer>
            <div class="dialog-footer">
                <el-button @click="visible = false">取消</el-button>
                <el-button type="primary" @click="handleSubmit()"> 确定 </el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script lang="ts" setup>
import { useMessage } from '/@/hooks/message';
import customInputNumber from '/@/components/custom-input-number/index.vue';
const emit = defineEmits(['submit']);
withDefaults(
    defineProps<{
        title: string;
        warningText: string;
    }>(),
    {
        title: '标题',
        warningText: '警告',
    }
);
interface FormData {
    id?: string;
    standardPrice: number;
    effectiveDate: string;
    courseId?: string;
}
interface OpenData {
    id?: string;
    courseId?: string;
    standardPrice?: number;
    effectiveDate?: string;
}

// 定义校验规则
const dataRules = ref({
    standardPrice: [{ required: true, message: '请输入收费标准', trigger: 'change' }],
    effectiveDate: [{ required: true, message: '请选择有效期', trigger: 'change' }],
});
const visible = ref(false);
const dataFormRef = ref();
const isEdit = ref(false);
const form = reactive<FormData>({
    id: '',
    standardPrice: 0,
    effectiveDate: '',
});
const openDialog = (data?: OpenData) => {
    console.log(data);

    isEdit.value = !!(data && data.id);
    form.effectiveDate = data?.effectiveDate || '';
    form.standardPrice = data?.standardPrice || 0;
    form.courseId = data?.courseId || '';
    form.id = data?.id || '';
    visible.value = true;
};

const handleSubmit = async () => {
    const valid = await dataFormRef.value.validate().catch(() => {});
    if (!valid) return false;
    try {
        // await updateFeeByStoreIdApi(form);
        // useMessage().success('编辑成功');
        // visible.value = false;
        // dataFormRef.value?.resetFields();
        emit('submit', form);
    } catch (error: any) {
        useMessage().error(error);
    }
};
const closeDialog = () => {
    dataFormRef.value?.resetFields();
    form.courseId = '';
    form.id = '';
    visible.value = false;
};
defineExpose({ openDialog, closeDialog });
</script>

<style lang="scss" scoped></style>
