<!-- edit.vue -->
<template>
    <div class="edit-container">
        <div class="header">
            <h2>课时费明细</h2>
        </div>

        <div class="content">
            <div class="layout-padding-auto layout-padding-view">
                <el-row>
                    <el-form
                        :inline="true"
                        :model="state.queryForm"
                        ref="queryRef"
                        class="demo-form-inline"
                        @keyup.enter="getDataList"
                    >
                        <el-form-item label="课程名称">
                            <fuzzy-search
                                v-model="state.queryForm.courseId"
                                :requestUrl="getlistByCourseName"
                                placeholder="请输入课程名称"
                                data-key="id"
                                data-label="courseName"
                                :remote="false"
                            />
                        </el-form-item>
                        <el-form-item label="课程类型" prop="courseTypeId">
                            <el-select v-model="state.queryForm.courseTypeId" placeholder="请选择课程类型">
                                <el-option
                                    v-for="item in courseTypeList"
                                    :key="item.id"
                                    :value="item.id"
                                    :label="item.name"
                                />
                            </el-select>
                        </el-form-item>
                        <el-form-item>
                            <el-button icon="search" type="primary" @click="getDataList">
                                {{ $t('common.queryBtn') }}
                            </el-button>
                            <el-button icon="Refresh" @click="resetQuery">{{ $t('common.resetBtn') }}</el-button>
                        </el-form-item>
                    </el-form>
                    <div class="mb8" style="width: 100%"></div>
                </el-row>
                <el-table
                    :data="state.dataList"
                    v-loading="state.loading"
                    :cell-style="tableStyle.cellStyle"
                    :header-cell-style="tableStyle.headerCellStyle"
                    @sort-change="sortChangeHandle"
                >
                    <el-table-column type="index" label="序号" width="60" />
                    <el-table-column prop="courseName" label="课程名称" show-overflow-tooltip />
                    <el-table-column prop="courseTypeName" label="课程类型 " show-overflow-tooltip />
                    <el-table-column prop="schoolName" label="收费方式" show-overflow-tooltip>
                        <template #default="scope">
                            <span v-if="scope.row.chargeMethod == ChargeMethodEnum.shop" type="success">门店设置</span>
                            <span v-else-if="scope.row.chargeMethod == ChargeMethodEnum.custom" type="success"
                                >自定义</span
                            >
                            <span v-else>/</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="customizeFee" label="课时费标准" show-overflow-tooltip>
                        <template #default="scope">
                            {{ setCourseFee(scope.row.customizeFee, scope.row.chargeMethod == ChargeMethodEnum.shop) }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="effectiveDate" label="生效日期 " show-overflow-tooltip />
                    <el-table-column prop="createBy" label="操作人" show-overflow-tooltip />
                    <el-table-column prop="createTime" label="操作时间" show-overflow-tooltip />
                    <el-table-column label="操作" width="250" fixed="right">
                        <template #default="scope">
                            <el-button
                                icon="edit"
                                text
                                type="primary"
                                @click="edit(scope.row)"
                                v-if="scope.row.chargeMethod == ChargeMethodEnum.custom"
                            >
                                修改
                            </el-button>
                            <el-button
                                icon="Tickets"
                                text
                                type="primary"
                                @click="
                                    classFeeRecordRef.openDialog({
                                        id: state.queryForm.id,
                                        courseId: scope.row.courseId,
                                        courseName: scope.row.courseName,
                                        courseTypeName: scope.row.courseTypeName,
                                    })
                                "
                            >
                                课时费生效记录
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <pagination
                    @size-change="sizeChangeHandle"
                    @current-change="currentChangeHandle"
                    v-bind="state.pagination"
                />
            </div>
        </div>
        <class-fee-form
            ref="classFeeFormRef"
            title="修改自定义课时费标准"
            warning-text="生效日期不同会产生多条收费标准"
            @submit="editSubmit"
        ></class-fee-form>
        <class-fee-record ref="classFeeRecordRef"></class-fee-record>
    </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { BasicTableProps, useTable } from '/@/hooks/table';
import { allCourseTypeApi, updateCustomizeFeeByStoreIdApi } from '/@/api/eduConnect/classType';
import { courseFeeListApi } from '/@/api/eduConnect/campusStore';
import { ChargeMethodEnum, formatToTwoDecimals, setCourseFee } from '/@/utils/classFee';
import fuzzySearch from '/@/components/EduComponents/fuzzySearch.vue';
import { getlistByCourseName } from '/@/api/eduConnect/demand';
import classFeeForm from './components/classFeeForm.vue';
import classFeeRecord from './components/classFeeRecord.vue';
import { useMessage } from '/@/hooks/message';
const state: BasicTableProps = reactive<BasicTableProps>({
    createdIsNeed: false,
    queryForm: {
        courseId: '',
        courseTypeId: '',
        id: '',
    },
    pageList: courseFeeListApi,
});

const route = useRoute();
const courseTypeList = ref();
const getCourseType = () => {
    allCourseTypeApi().then((res) => {
        courseTypeList.value = res.data;
    });
};
getCourseType();
const { getDataList, currentChangeHandle, sizeChangeHandle, sortChangeHandle, tableStyle } = useTable(state);
const queryRef = ref();
const classFeeFormRef = ref();
const classFeeRecordRef = ref();
// 清空搜索条件
const resetQuery = () => {
    // 清空搜索条件
    queryRef.value?.resetFields();
    state.queryForm.courseTypeId = '';
    state.queryForm.courseId = '';
    getDataList();
};
const edit = (item: any) => {
    classFeeFormRef.value.openDialog({
        id: state.queryForm.id,
        standardPrice: formatToTwoDecimals(item.customizeFee),
        effectiveDate: item.effectiveDate,
        courseId: item.courseId,
    });
};
interface OpenData {
    id?: string;
    courseId?: string;
    standardPrice?: number;
    effectiveDate?: string;
}
const editSubmit = (form: OpenData) => {
    updateCustomizeFeeByStoreIdApi(form)
        .then(() => {
            useMessage().success('修改成功');
            classFeeFormRef.value.closeDialog();
            getDataList();
        })
        .catch((err) => {
            useMessage().error(err.msg);
        });
};
onMounted(() => {
    if (route.query.id) {
        state.queryForm.id = route.query.id || '';
        getDataList();
    }
});
</script>

<style lang="scss" scoped>
.edit-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    background-color: #fff;
}

.header {
    padding: 16px 24px;
    border-bottom: 1px solid #e4e7ed;
    h2 {
        font-size: 16px;
        font-weight: bold;
    }
}

.content {
    flex: 1;
    // padding: 24px;
    overflow-y: auto;
}

.footer {
    padding: 16px 24px;
    border-top: 1px solid #e4e7ed;
    border-bottom: 1px solid #e4e7ed;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}
</style>
