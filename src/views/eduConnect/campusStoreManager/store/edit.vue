<!-- edit.vue -->
<template>
    <div class="edit-container">
        <div class="header">
            <h2>编辑门店信息</h2>
        </div>

        <div class="content">
            <BasicForm ref="formRef" :initial-data="formData" mode="edit" :isEdit="!!route.query.id" />
        </div>

        <div class="footer">
            <el-button type="primary" @click="handleSubmit">确认</el-button>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import BasicForm from './form.vue';
import { getSchoolStoreDetail, updateSchoolStore } from '/@/api/eduConnect/campusStore';
import { getCurrentCourseFeeByStoreApi } from '/@/api/eduConnect/classType';

import moment from 'moment';
import { formatToTwoDecimals } from '/@/utils/classFee';

const route = useRoute();
const router = useRouter();
const formRef = ref();
const formData = ref<Record<string, any>>({});

// 获取详情数据
const getDetail = async (id: string) => {
    let studentPrice = 0;
    try {
        const { data } = await getCurrentCourseFeeByStoreApi({ id });
        studentPrice = data.standardPrice;
    } catch (error) {
        studentPrice = 0;
    }
    try {
        const { data } = await getSchoolStoreDetail(id);
        const {
            campusName,
            schoolId,
            schoolName,
            regionId,
            provinceCode,
            cityCode,
            districtCode,
            signingSubject,
            signingType,
            signingSubjectPhone,
            servicePeriodStart,
            servicePeriodEnd,
            campusMode,
        } = data;

        // 过滤掉为空的代码，再进行拼接
        const validCodes = [provinceCode, cityCode, districtCode].filter((code) => code);
        const area = validCodes.join(',');

        formData.value = {
            campusName,
            schoolId: schoolId + '',
            schoolName,
            regionId: regionId + '',
            provinceCode,
            cityCode,
            districtCode,
            area,
            signingSubject,
            signingSubjectPhone,
            signingType,
            serviceTime: [servicePeriodStart, servicePeriodEnd],
            campusMode: campusMode + '',
            studentPrice: formatToTwoDecimals(studentPrice),
        };
    } catch (error) {
        console.error(error);
        ElMessage.error('获取详情失败');
    }
};

// 处理表单提交
const handleSubmit = async () => {
    const form = formRef.value.formRef;
    if (!form) return;

    try {
        await form.validate();
        const formData = formRef.value.formData;
        const {
            campusName,
            schoolId,
            regionId,
            provinceCode,
            cityCode,
            districtCode,
            signingSubject,
            signingType,
            signingSubjectPhone,
            serviceTime,
            campusMode,
            studentPrice,
        } = formData;

        const params = {
            id: route.query.id,
            campusName,
            schoolId,
            regionId,
            provinceCode,
            cityCode,
            districtCode,
            signingSubject,
            signingType,
            signingSubjectPhone,
            servicePeriodStart: '',
            servicePeriodEnd: '',
            campusMode,
            studentPrice,
        };
        if (serviceTime.length > 0) {
            params.servicePeriodStart = moment(serviceTime[0]).format('YYYY-MM-DD');
            params.servicePeriodEnd = moment(serviceTime[1]).format('YYYY-MM-DD');
        }
        await updateSchoolStore(params);
        ElMessage.success('更新成功');
        router.push('/eduConnect/campusStoreManager/store/index');
    } catch (error: any) {
        if (error?.msg) {
            ElMessage.error(error.msg);
        } else {
            ElMessage.error('请输入完整的门店信息');
        }
    }
};

onMounted(() => {
    if (route.query.id) {
        getDetail(route.query.id as string);
    }
});
</script>

<style lang="scss" scoped>
.edit-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    background-color: #fff;
}

.header {
    padding: 16px 24px;
    border-bottom: 1px solid #e4e7ed;
    h2 {
        font-size: 16px;
        font-weight: bold;
    }
}

.content {
    flex: 1;
    padding: 24px;
    overflow-y: auto;
}

.footer {
    padding: 16px 24px;
    border-top: 1px solid #e4e7ed;
    border-bottom: 1px solid #e4e7ed;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}
</style>
