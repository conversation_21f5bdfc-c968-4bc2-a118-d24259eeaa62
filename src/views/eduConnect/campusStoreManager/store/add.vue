<!-- add.vue -->
<template>
    <div class="add-container">
        <div class="header">
            <h2>新增门店信息</h2>
        </div>

        <div class="content">
            <BasicForm ref="formRef" />
        </div>

        <div class="footer">
            <!-- <el-button @click="handleCancel">取消</el-button> -->
            <el-button type="primary" @click="handleSubmit">确认</el-button>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import BasicForm from './form.vue';
import moment from 'moment';
import { addSchoolStore } from '/@/api/eduConnect/campusStore';

const router = useRouter();
const formRef = ref();

// 处理表单提交
const handleSubmit = async () => {
    const form = formRef.value.formRef;
    if (!form) return;

    try {
        await form.validate();
        const formData = formRef.value.formData;
        const {
            campusName,
            schoolId,
            regionId,
            provinceCode,
            cityCode,
            districtCode,
            signingSubject,
            signingType,
            signingSubjectPhone,
            serviceTime,
            campusMode,
            studentPrice,
        } = formData;

        const params = {
            campusName,
            schoolId,
            regionId,
            provinceCode,
            cityCode,
            districtCode,
            signingSubject,
            signingType,
            signingSubjectPhone,
            servicePeriodStart: '',
            servicePeriodEnd: '',
            campusMode,
            studentPrice,
        };
        if (serviceTime.length > 0) {
            params.servicePeriodStart = moment(serviceTime[0]).format('YYYY-MM-DD');
            params.servicePeriodEnd = moment(serviceTime[1]).format('YYYY-MM-DD');
        }
        console.log(params);

        // 这里调用添加API
        await addSchoolStore(params);
        ElMessage.success('添加成功');
        router.push('/eduConnect/campusStoreManager/store/index');
    } catch (error: any) {
        if (error?.msg) {
            ElMessage.error(error.msg);
        } else {
            ElMessage.error('请输入完整的门店信息');
        }
    }
};

// // 处理取消
// const handleCancel = () => {
//     router.back();
// };
</script>

<style lang="scss" scoped>
.add-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    background-color: #fff;
}

.header {
    padding: 16px 24px;
    border-bottom: 1px solid #e4e7ed;
    h2 {
        font-size: 16px;
        font-weight: bold;
    }
}

.content {
    flex: 1;
    padding: 24px;
    overflow-y: auto;
}

.footer {
    padding: 16px 24px;
    border-top: 1px solid #e4e7ed;
    border-bottom: 1px solid #e4e7ed;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    background-color: #fff;
}
</style>
