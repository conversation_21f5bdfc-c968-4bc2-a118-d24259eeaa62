<template>
    <div class="layout-padding">
        <div class="layout-padding-auto layout-padding-view">
            <el-row>
                <el-form
                    :inline="true"
                    :model="state.queryForm"
                    ref="queryRef"
                    class="demo-form-inline"
                    v-show="showSearch"
                >
                    <el-form-item label="所属大区">
                        <el-select v-model="state.queryForm.regionId" placeholder="请选择大区" clearable>
                            <el-option
                                v-for="item in region"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="校区名称">
                        <el-input v-model="state.queryForm.schoolName" placeholder="请输入校区名称" clearable />
                    </el-form-item>

                    <el-form-item>
                        <el-button icon="search" type="primary" @click="getDataList">
                            {{ $t('common.queryBtn') }}
                        </el-button>
                        <el-button icon="Refresh" @click="resetQuery">{{ $t('common.resetBtn') }}</el-button>
                    </el-form-item>
                </el-form>
            </el-row>
            <el-row type="flex" justify="space-between" class="mb8">
                <el-button icon="folder-add" type="primary" @click="formDialogRef.openDialog()"> 新 增 </el-button>
                <right-toolbar
                    v-model:showSearch="showSearch"
                    :export="false"
                    style="float: right"
                    @queryTable="getDataList"
                />
            </el-row>
            <el-table
                :data="state.dataList"
                v-loading="state.loading"
                border
                :cell-style="tableStyle.cellStyle"
                :header-cell-style="tableStyle.headerCellStyle"
                @sort-change="sortChangeHandle"
            >
                <el-table-column type="index" label="序号" width="60" />
                <el-table-column prop="schoolName" label="校区名称" show-overflow-tooltip width="200" />
                <el-table-column prop="regionId" label="所属大区" show-overflow-tooltip width="100">
                    <template #default="scope">
                        <dict-tag :options="region" :value="scope.row.regionId"></dict-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="district" label="地区" show-overflow-tooltip>
                    <template #default="scope">
                        <span>
                            {{ scope.row.provinceName }}
                            {{ scope.row.cityName ? '/' + scope.row.cityName : '' }}
                            {{ scope.row.districtName ? '/' + scope.row.districtName : '' }}
                        </span>
                    </template>
                </el-table-column>
                <el-table-column prop="campusManager" show-overflow-tooltip label="校区负责人" width="120">
                    <template #default="scope">
                        <div v-if="scope.row.principal && scope.row.principal.length > 0">
                            <div v-for="item in scope.row.principal" :key="item.userId" class="cell-split">
                                {{ item.manageName }}
                            </div>
                        </div>
                        <span v-else>--</span>
                    </template>
                </el-table-column>
                <el-table-column prop="managerPhoneNumber" show-overflow-tooltip label="负责人手机号" width="140">
                    <template #default="scope">
                        <div v-if="scope.row.principal && scope.row.principal.length > 0">
                            <div v-for="item in scope.row.principal" :key="item.userId" class="cell-split">
                                {{ item.managePhone }}
                            </div>
                        </div>
                        <span v-else>--</span>
                    </template>
                </el-table-column>
                <el-table-column prop="storeCount" label="门店数量" width="100"> </el-table-column>
                <el-table-column prop="createTime" label="创建时间" show-overflow-tooltip width="160" />
                <el-table-column prop="createBy" label="创建者" show-overflow-tooltip width="100" />

                <el-table-column label="操作" width="80">
                    <template #default="scope">
                        <el-button icon="edit-pen" text type="primary" @click="formDialogRef.openDialog(scope.row)">
                            修改
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
            <pagination
                @size-change="sizeChangeHandle"
                @current-change="currentChangeHandle"
                v-bind="state.pagination"
            />
        </div>

        <!-- 编辑、新增  -->
        <form-dialog ref="formDialogRef" @refresh="getDataList(false)" :region-list="region" />
    </div>
</template>

<script setup lang="ts" name="systemCampus">
import { defineAsyncComponent, ref, reactive } from 'vue';
import { BasicTableProps, useTable } from '/@/hooks/table';
import { getSchoolList } from '/@/api/eduConnect/campusStore';
import { useDict } from '/@/hooks/dict';
// 引入组件
const FormDialog = defineAsyncComponent(() => import('./form.vue'));
// 定义查询字典
const { region } = useDict(['region']);
// 定义变量内容
const formDialogRef = ref();
// 搜索变量
const queryRef = ref();
const showSearch = ref(true);

const state: BasicTableProps = reactive<BasicTableProps>({
    queryForm: {
        schoolName: '',
        regionId: '',
    },
    pageList: getSchoolList,
});

//  table hook
const { getDataList, currentChangeHandle, sizeChangeHandle, sortChangeHandle, tableStyle } = useTable(state);

// 清空搜索条件
const resetQuery = () => {
    // 清空搜索条件
    queryRef.value?.resetFields();
    // 清空多选
    state.queryForm = [];
    getDataList();
};
</script>

<style lang="scss" scoped>
.cell-split {
    border-bottom: 1px solid #ccc;
    &:last-child {
        border-bottom: none;
    }
}
</style>
