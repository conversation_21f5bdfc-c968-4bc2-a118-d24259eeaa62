<template>
    <el-dialog
        :title="form.id ? '编辑校区' : '新增校区'"
        v-model="visible"
        :close-on-click-modal="false"
        @closed="resetFormData"
        width="400"
    >
        <el-form
            ref="dataFormRef"
            :model="form"
            :rules="dataRules"
            formDialogRef
            label-width="80px"
            v-loading="loading"
        >
            <el-row :gutter="24">
                <el-col :span="24" class="mb20">
                    <el-form-item label="校区名称" prop="schoolName">
                        <el-input
                            v-model="form.schoolName"
                            placeholder="请输入校区名称"
                            maxlength="20"
                            show-word-limit
                        />
                    </el-form-item>
                </el-col>
                <el-col :span="24" class="mb20">
                    <el-form-item label="所属大区" prop="regionId">
                        <el-select v-model="form.regionId" placeholder="请选择所属大区" clearable>
                            <el-option
                                v-for="item in region"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            />
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="24" class="mb20">
                    <el-form-item label="地区" prop="areaCode">
                        <china-area
                            style="width: 100%"
                            :type="3"
                            placeholder="请选择地区"
                            v-model="form.areaCode"
                            :plus="true"
                            @change-with-detail="handleAreaChange"
                        />
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="visible = false">取 消</el-button>
                <el-button type="primary" @click="onSubmit" :disabled="loading">确 认</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script setup lang="ts" name="CampusDialog">
import { useDict } from '/@/hooks/dict';
import { useMessage } from '/@/hooks/message';
import { addSchool, updateSchool } from '/@/api/eduConnect/campusStore';

const emit = defineEmits(['refresh']);

// 省市区查询组件
const ChinaArea = defineAsyncComponent(() => import('/@/components/ChinaArea/index.vue'));
const { region } = useDict(['region']);

// 定义变量内容
const dataFormRef = ref();
const visible = ref(false);
const loading = ref(false);
// 定义字典

// 提交表单数据
const form = reactive({
    id: '',
    areaCode: '',
    schoolName: '',
    regionId: '',
    provinceCode: '',
    cityCode: '',
    districtCode: '',
});

// 定义校验规则
const dataRules = ref({
    schoolName: [{ required: true, message: '校区名称不能为空', trigger: 'blur' }],
    regionId: [{ required: true, message: '所属大区不能为空', trigger: 'change' }],
    areaCode: [{ required: true, message: '地区不能为空', trigger: 'change' }],
});

// 打开弹窗
const openDialog = (row: Record<string, any>) => {
    visible.value = true;
    form.id = '';
    if (dataFormRef.value) {
        dataFormRef.value.clearValidate();
    }
    // 获取campus信息
    if (row) {
        const { id, schoolName, regionId, provinceCode, cityCode, districtCode } = row;
        form.id = id;
        form.schoolName = schoolName;
        form.regionId = regionId + '';
        const validCodes = [provinceCode, cityCode, districtCode].filter((code) => code);
        form.areaCode = validCodes.join(',');
        form.provinceCode = provinceCode;
        form.cityCode = cityCode;
        form.districtCode = districtCode;
    }
};

const resetFormData = async () => {
    form.id = '';
    form.schoolName = '';
    form.regionId = '';
    form.areaCode = '';
    form.provinceCode = '';
    form.cityCode = '';
    form.districtCode = '';
};

const handleAreaChange = (areaInfo = { provinceCode: '', cityCode: '', districtCode: '' }) => {
    const { provinceCode, cityCode, districtCode } = areaInfo;
    form.provinceCode = provinceCode;
    form.cityCode = cityCode;
    form.districtCode = districtCode;
    const validCodes = [provinceCode, cityCode, districtCode].filter((code) => code);
    form.areaCode = validCodes.join(',');
};
// 提交
const onSubmit = async () => {
    const valid = await dataFormRef.value.validate().catch(() => {});
    if (!valid) return false;

    try {
        loading.value = true;

        const { id, regionId, schoolName, provinceCode, cityCode, districtCode } = form;

        const params = {
            id,
            schoolName,
            regionId,
            provinceCode,
            cityCode,
            districtCode,
        };

        form.id ? await updateSchool(params) : await addSchool(params);
        useMessage().success(form.id ? '修改成功' : '添加成功');
        visible.value = false;
        resetFormData();
        emit('refresh');
    } catch (err: any) {
        useMessage().error(err.msg);
    } finally {
        loading.value = false;
    }
};

// 暴露变量
defineExpose({
    openDialog,
});
</script>
