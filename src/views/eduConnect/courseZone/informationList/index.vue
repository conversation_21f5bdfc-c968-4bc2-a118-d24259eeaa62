<template>
    <div class="layout-padding">
        <div class="layout-padding-auto layout-padding-view">
            <navi-bar
                class="mb20"
                title="资料列表"
                :sub-title="route.query.author as string"
                :from="decodeURIComponent(route.query.from as string)"
            />
            <el-row>
                <div class="mb8" style="width: 100%">
                    <el-button icon="folder-add" type="primary" class="ml10" @click="uploadModelRef.openDialog()">
                        上传资料
                    </el-button>
                </div>
            </el-row>
            <el-table
                :data="state.dataList"
                v-loading="state.loading"
                style="width: 100%; margin-bottom: 20px"
                row-key="id"
                border
                :cell-style="tableStyle.cellStyle"
                :header-cell-style="tableStyle.headerCellStyle"
            >
                <el-table-column type="index" label="序号" width="60" />
                <el-table-column prop="resourceName" label="资料名称" show-overflow-tooltip />
                <el-table-column prop="createBy" label="上传人" show-overflow-tooltip />
                <el-table-column prop="createTime" label="上传时间" align="left" />
                <el-table-column prop="contentsName" label="是否允许下载" align="left">
                    <template #default="scope">
                        <el-switch
                            v-model="scope.row.isDownload"
                            :active-value="isDownload.YES"
                            :inactive-value="isDownload.NO"
                            @change="handleChange(scope.row)"
                        />
                    </template>
                </el-table-column>
                <el-table-column label="操作" fixed="right">
                    <template #default="scope">
                        <el-button type="primary" link @click="handlePreview(scope.row)">预览</el-button>
                        <el-button type="primary" link @click="handleDelete(scope.row)">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <pagination
                @size-change="sizeChangeHandle"
                @current-change="currentChangeHandle"
                v-bind="state.pagination"
            />
        </div>
        <UploadModel ref="uploadModelRef" @refresh="getDataList(false)" />
    </div>
</template>

<script setup lang="ts">
import NaviBar from '/@/components/NaviBar/index.vue';
import { BasicTableProps, useTable } from '/@/hooks/table';
import {
    resourceInformationApi,
    editInformationTokenApi,
    delInformationTokenApi,
    informationInfoApi,
} from '/@/api/eduConnect/information';
import { ElMessageBox } from 'element-plus';
enum isDownload {
    YES = 1,
    NO = 0,
}
const UploadModel = defineAsyncComponent(() => import('./uploadModel.vue'));
const uploadModelRef = ref();
const route = useRoute();
const state: BasicTableProps = reactive<BasicTableProps>({
    queryForm: {
        id: route.query.id,
    },
    isPage: true,
    pagination: {},
    pageList: resourceInformationApi,
});
const { getDataList, tableStyle, sizeChangeHandle, currentChangeHandle } = useTable(state);

const handleChange = async (row: any) => {
    await editInformationTokenApi({
        id: row.id,
        isDownload: row.isDownload,
    });
    getDataList(false);
};
const handlePreview = (row: any) => {
    informationInfoApi(row.id).then((res) => {
        if (res.data.resourceUrl) {
            window.open(res.data.resourceUrl, '_blank');
        }
    });
};
const handleDelete = (row: any) => {
    ElMessageBox.confirm('确定删除该资料吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    }).then(() => {
        delInformationTokenApi(row.id).then(() => {
            getDataList(false);
        });
    });
};
</script>

<style scoped></style>
