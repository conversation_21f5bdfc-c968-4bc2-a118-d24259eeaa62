<template>
    <el-dialog title="上传资料" v-model="visible" :close-on-click-modal="false" width="800" @close="handleCloseDialog">
        <el-form
            ref="dataFormRef"
            :model="form"
            :rules="dataRules"
            formDialogRef
            label-width="120px"
            v-loading="loading"
        >
            <Upload :multiple="true" ref="uploadRef" :fileSize="3000" @success="uploadSuccess" @remove="uploadRemove" />
        </el-form>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="visible = false">取消</el-button>
                <el-button type="primary" :disabled="loading" @click="onSubmit">确认</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script setup lang="ts" name="CourseDialog">
import { useMessage } from '/@/hooks/message';
import { saveUploadInformationApi } from '/@/api/eduConnect/information';
import Upload from '/@/components/Upload/fileDragOss.vue';
import { useRoute } from 'vue-router';
const route = useRoute();
const emit = defineEmits(['refresh']);
const dataFormRef = ref();
const visible = ref(false);
const loading = ref(false);
const form = reactive({});
const dataRules = ref({});
const uploadSuccessList = ref<
    Array<{
        resourceName: string;
        resourceUrl: string;
    }>
>([]);
const uploadRef = ref();
// 打开弹窗
const openDialog = () => {
    visible.value = true;
};
// 提交
const onSubmit = async () => {
    const valid = await dataFormRef.value.validate().catch(() => {});
    if (!valid) return false;

    try {
        if (uploadSuccessList.value.length === 0) {
            useMessage().error('请先上传资料');
            return false;
        }
        loading.value = true;
        await saveUploadInformationApi(route.query.id as string, uploadSuccessList.value);
        useMessage().success('保存成功');
        visible.value = false;
        uploadSuccessList.value = [];
        uploadRef.value.clearUploadList();
        emit('refresh');
    } catch (err: any) {
        useMessage().error(err.msg);
    } finally {
        loading.value = false;
    }
};
const handleCloseDialog = () => {
    visible.value = false;
    uploadSuccessList.value = [];
    uploadRef.value.clearUploadList();
};
const uploadSuccess = (file: any) => {
    uploadSuccessList.value.push({
        resourceName: file.fileName,
        resourceUrl: file.relativePath,
    });
};
const uploadRemove = (file: any) => {
    uploadSuccessList.value = uploadSuccessList.value.filter(
        (item) => item.resourceName !== file.name && item.resourceUrl !== file.relativePath
    );
};

// 暴露变量
defineExpose({
    openDialog,
});
</script>
