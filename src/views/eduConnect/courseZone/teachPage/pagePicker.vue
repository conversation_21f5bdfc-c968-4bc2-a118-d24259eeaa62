<template>
    <el-dialog v-model="visible" :close-on-click-modal="false" :show-close="false" width="60%" max-height="80%">
        <template #header="{ close, titleId, titleClass }">
            <div>
                <div class="flex items-center justify-between gap-[16px] mb-[12px]">
                    <div class="flex items-center gap-[16px]">
                        <p :id="titleId" :class="titleClass">请选择要插入的教学页</p>
                        <el-select v-model="themeValue" placeholder="请选择" style="width: 120px" size="small">
                            <el-option label="简约风格" :value="1" />
                        </el-select>
                    </div>
                    <el-button icon="close" text @click="close" />
                </div>
                <div class="px-[20px]">
                    <el-tabs v-model="typeValue" @tab-change="handleTypeChange">
                        <el-tab-pane
                            v-for="item in teaching_page_type"
                            :key="item.value"
                            :label="item.label"
                            :name="item.value"
                        />
                    </el-tabs>
                    <el-tabs v-model="categoryValue" @tab-change="handleCategoryChange" v-if="typeValue == '1'">
                        <el-tab-pane
                            v-for="item in teaching_page_category"
                            :key="item.value"
                            :label="item.label"
                            :name="item.value"
                        />
                    </el-tabs>
                </div>
            </div>
        </template>
        <div v-loading="loading" class="content">
            <el-row v-if="dataArray.length > 0" :gutter="12">
                <el-col
                    class="mb-[12px] cursor-pointer"
                    :xs="12"
                    :sm="12"
                    :md="8"
                    :lg="8"
                    :xl="6"
                    v-for="(item, index) in dataArray"
                    :key="index"
                    @click="selectedIndex = index"
                >
                    <el-card :class="{ selected: selectedIndex == index }" body-class="p-0" shadow="hover">
                        <el-image class="image" :src="item.path" fit="cover">
                            <template #placeholder>
                                <div class="w-full h-full flex items-center justify-center bg-gray-100">Loading...</div>
                            </template>
                        </el-image>
                        <p class="line-clamp-1 px-[10px] my-[8px] text-center">
                            {{ item.templateName }}
                        </p>
                    </el-card>
                </el-col>
            </el-row>
            <el-empty v-else class="m-auto" description="没有找到数据哦~" />
        </div>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="visible = false">取消</el-button>
                <el-button type="primary" :disabled="loading || dataArray.length == 0" @click="onSubmit"
                    >确认</el-button
                >
            </span>
        </template>
    </el-dialog>
</template>

<script setup lang="ts" name="TeachPageTempPicker">
import { useMessage } from '/@/hooks/message';
import { fetchTeachPageTempList } from '/@/api/integraEdu/teachPage';
import { useDict } from '/@/hooks/dict';

const emit = defineEmits(['complete']);
const { teaching_page_type, teaching_page_category } = useDict('teaching_page_type', 'teaching_page_category');
const visible = ref(false);
const loading = ref(false);
const themeValue = ref(1);
const typeValue = ref('');
const categoryValue = ref('');
const selectedIndex = ref(-1);
const dataArray = ref<any[]>([]);
// const savePreCategoryValue = ref('3');

// 打开弹窗
const openDialog = () => {
    visible.value = true;
    typeValue.value = '';
    // categoryValue.value = '1';
    selectedIndex.value = -1;
    dataArray.value = [];
    // if (teaching_page_type.value.length > 0 && teaching_page_category.value.length > 0) {
    typeValue.value = teaching_page_type.value?.[0].value;
    categoryValue.value = teaching_page_category.value?.[0].value;
    loadData();
    // }
};

const loadData = async () => {
    loading.value = true;
    const params = {
        type: typeValue.value,
        category: '',
        enabled: 1,
    };
    if (typeValue.value === '1') {
        params.category = categoryValue.value;
    }
    try {
        const res = await fetchTeachPageTempList(params);
        dataArray.value = res.data || [];
    } catch (err: any) {
        useMessage().error(err.msg);
    } finally {
        loading.value = false;
    }
};

const handleTypeChange = () => {
    // if (typeValue.value == '2') {
    //     savePreCategoryValue.value = categoryValue.value;
    //     categoryValue.value = '1';
    // } else {
    //     categoryValue.value = savePreCategoryValue.value;
    // }
    categoryValue.value = '1';
    selectedIndex.value = -1;
    loadData();
};
const handleCategoryChange = () => {
    selectedIndex.value = -1;
    loadData();
};

const onSubmit = async () => {
    if (selectedIndex.value === -1) {
        return useMessage().warning('请选择教学页');
    }
    emit('complete', { ...dataArray.value[selectedIndex.value] });
    visible.value = false;
};

// 暴露变量
defineExpose({
    open: openDialog,
});
</script>

<style scoped lang="scss">
.content {
    max-height: 60vh;
    min-height: 30vh;
}
.image {
    width: 100%;
    aspect-ratio: 16/9;
}
.selected {
    border: 1px solid var(--el-color-primary);
    color: white;
    background-color: var(--el-color-primary);
    // color: var(--el-color-primary);
}
</style>
