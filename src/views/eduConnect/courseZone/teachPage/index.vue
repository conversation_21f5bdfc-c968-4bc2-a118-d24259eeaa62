<template>
    <div class="layout-padding">
        <div class="layout-padding-auto layout-padding-view">
            <el-row>
                <div class="mb8" style="width: 100%">
                    <el-button icon="folder-add" type="primary" class="ml10" @click="handleEdit">
                        添加教学页模版
                    </el-button>
                    <right-toolbar
                        class="ml10 mr20"
                        :search="false"
                        :export="false"
                        style="float: right"
                        @queryTable="getDataList"
                    />
                </div>
            </el-row>
            <el-table
                :data="state.dataList"
                v-loading="state.loading"
                border
                :cell-style="tableStyle.cellStyle"
                :header-cell-style="tableStyle.headerCellStyle"
            >
                <el-table-column type="index" label="序号" width="60" />
                <el-table-column prop="templateName" label="教学页模版名称" width="160" />
                <el-table-column prop="viewUrl" label="URL" />
                <el-table-column prop="remark" label="备注">
                    <template #default="{ row }">
                        {{ row.remark || '--' }}
                    </template>
                </el-table-column>
                <el-table-column prop="enabled" label="是否启用" width="120">
                    <template #default="{ row }">
                        <el-switch
                            v-model="row.enabled"
                            inline-prompt
                            active-text="启用"
                            inactive-text="不启用"
                            :active-value="1"
                            :inactive-value="0"
                            @change="onEdit(row)"
                        />
                    </template>
                </el-table-column>
                <el-table-column prop="operation" label="操作" width="120">
                    <template #default="{ row }">
                        <el-button icon="edit" text type="primary" @click="handleEdit(row)">编辑</el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>
    </div>
</template>

<script setup lang="ts">
import { BasicTableProps, useTable } from '/@/hooks/table';
import { fetchTeachPageTempList, editTeachPageTemp } from '/@/api/integraEdu/teachPage';
import { useMessage } from '/@/hooks/message';

const router = useRouter();
const state: BasicTableProps = reactive<BasicTableProps>({
    queryForm: {},
    isPage: false,
    pagination: {},
    pageList: fetchTeachPageTempList,
});
const { getDataList, tableStyle } = useTable(state);

// 编辑
const onEdit = (row: any) => {
    state.loading = true;
    editTeachPageTemp(row)
        .then(() => {})
        .catch((err) => {
            getDataList();
            useMessage().error(err.msg);
        })
        .finally(() => {
            state.loading = false;
        });
};

// 前往新增/编辑
const handleEdit = async (row: any | undefined) => {
    router.push({
        path: '/eduConnect/courseZone/teachPage/edit',
        query: { id: row.id, from: encodeURIComponent(router.currentRoute.value.fullPath) },
    });
};
</script>

<style scoped></style>
