<template>
    <div class="layout-padding">
        <div class="layout-padding-auto layout-padding-view">
            <navi-bar
                ref="naviBarRef"
                class="mb20"
                :title="form.id ? '编辑教学页模版' : '添加教学页模版'"
                destroy
                :from="decodeURIComponent(route.query.from as string)"
            />
            <el-scrollbar class="w-full">
                <el-form
                    ref="dataFormRef"
                    class="w-[600px]"
                    :model="form"
                    :rules="dataRules"
                    formDialogRef
                    label-width="110"
                    v-loading="loading"
                >
                    <el-row :gutter="24">
                        <el-col :span="24" class="mb20">
                            <el-form-item label="模版名称" prop="templateName">
                                <el-input
                                    v-model.trim="form.templateName"
                                    placeholder="请输入教学页模版名称"
                                    maxlength="50"
                                    show-word-limit
                                />
                            </el-form-item>
                        </el-col>
                        <el-col :span="24" class="mb20">
                            <el-form-item label="类型" prop="type">
                                <el-select v-model="form.type" placeholder="请选择">
                                    <el-option
                                        v-for="option in teaching_page_type"
                                        :key="option.value"
                                        :label="option.label"
                                        :value="option.value"
                                    />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24" class="mb20">
                            <el-form-item label="分类" prop="category">
                                <el-select v-model="form.category" placeholder="请选择">
                                    <el-option
                                        v-for="option in teaching_page_category"
                                        :key="option.value"
                                        :label="option.label"
                                        :value="option.value"
                                    />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24" class="mb20">
                            <el-form-item label="页面URL" prop="viewUrl">
                                <el-input
                                    v-model.trim="form.viewUrl"
                                    placeholder="请输入页面URL"
                                    maxlength="200"
                                    show-word-limit
                                />
                            </el-form-item>
                        </el-col>
                        <el-col :span="24" class="mb20">
                            <el-form-item label="页面截图" prop="image">
                                <ImageOss
                                    v-model:file-path="form.image"
                                    service-name="teaching"
                                    module-name="pageTemplate"
                                    :file-type="['jpg']"
                                />
                            </el-form-item>
                        </el-col>
                        <el-col :span="24" class="mb20">
                            <el-form-item label="配置信息" prop="attr">
                                <el-input
                                    v-model.trim="form.attr"
                                    type="textarea"
                                    :autosize="{ minRows: 4, maxRows: 4 }"
                                    placeholder="请输入配置信息"
                                />
                            </el-form-item>
                        </el-col>
                        <el-col :span="24" class="mb20">
                            <el-form-item label="备注">
                                <el-input
                                    v-model.trim="form.remark"
                                    type="textarea"
                                    :autosize="{ minRows: 1, maxRows: 4 }"
                                    placeholder="请输入备注"
                                    maxlength="200"
                                    show-word-limit
                                />
                            </el-form-item>
                        </el-col>
                        <el-col :span="24" class="mb20">
                            <el-form-item label="是否启用">
                                <el-switch
                                    v-model="form.enabled"
                                    inline-prompt
                                    active-text="启用"
                                    inactive-text="不启用"
                                    :active-value="1"
                                    :inactive-value="0"
                                />
                            </el-form-item>
                        </el-col>

                        <el-col :span="24" class="mb35 mt20">
                            <el-form-item>
                                <el-button @click="naviBarRef.back()">取消</el-button>
                                <el-button type="primary" :disabled="loading" @click="onSubmit">确认</el-button>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </el-scrollbar>
        </div>
    </div>
</template>

<script setup lang="ts">
import NaviBar from '/@/components/NaviBar/index.vue';
import ImageOss from '/@/components/Upload/ImageOss.vue';
import { useMessage } from '/@/hooks/message';
import { useDict } from '/@/hooks/dict';
import { teachPageTempDetail, editTeachPageTemp } from '/@/api/integraEdu/teachPage';

const route = useRoute();
const { teaching_page_type, teaching_page_category } = useDict('teaching_page_type', 'teaching_page_category');
const dataFormRef = ref();
const naviBarRef = ref();
const loading = ref(false);
const form = reactive({
    id: '',
    templateName: '',
    type: '',
    category: '',
    viewUrl: '',
    url: '',
    path: '',
    attr: '',
    remark: '',
    enabled: 1,
    image: {
        relativePath: '',
        fullPath: '',
    },
});
const dataRules = ref({
    templateName: [{ required: true, message: '教学页模版名称不能为空', trigger: 'blur' }],
    type: [{ required: true, message: '类型不能为空', trigger: 'change' }],
    category: [{ required: true, message: '分类不能为空', trigger: 'change' }],
    viewUrl: [{ required: true, message: '页面URL不能为空', trigger: 'blur' }],
    attr: [
        { required: true, message: '配置信息不能为空', trigger: 'blur' },
        {
            required: true,
            validator: (rule: any, value: string, callback: any) => {
                try {
                    if (Object.prototype.toString.call(JSON.parse(value)) === '[object Object]') {
                        callback();
                    } else {
                        callback(new Error('JSON格式不正确'));
                    }
                } catch (e) {
                    callback(new Error('JSON格式不正确'));
                }
            },
            trigger: 'blur',
        },
    ],
    image: [
        {
            required: true,
            validator: (rule: any, value: typeof form.image, callback: any) => {
                if (value.relativePath == '') {
                    callback(new Error('页面截图不能为空'));
                } else {
                    callback();
                }
            },
            trigger: 'change',
        },
    ],
});

onMounted(() => {
    const pageId = route.query.id as string;
    if (pageId) {
        form.id = pageId;
        loading.value = true;
        teachPageTempDetail(pageId)
            .then((res) => {
                res.data.type = res.data.type + '';
                res.data.category = res.data.category + '';
                Object.assign(form, res.data);
                form.image = {
                    relativePath: res.data.url,
                    fullPath: res.data.path,
                };
            })
            .catch((err) => {
                useMessage().error(err.msg);
            })
            .finally(() => {
                loading.value = false;
            });
    }
});

const onSubmit = async () => {
    const valid = await dataFormRef.value.validate().catch(() => {});
    if (!valid) return false;
    loading.value = true;
    editTeachPageTemp({ ...form, url: form.image.relativePath })
        .then(() => {
            useMessage().success(form.id ? '编辑成功' : '添加成功');
            naviBarRef.value.back();
        })
        .catch((err) => {
            useMessage().error(err.msg);
        })
        .finally(() => {
            loading.value = false;
        });
};
</script>

<style lang="scss" scoped></style>
