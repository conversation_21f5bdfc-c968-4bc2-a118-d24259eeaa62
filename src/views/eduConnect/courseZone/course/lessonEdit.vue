<template>
    <div class="layout-padding">
        <div class="layout-padding-auto layout-padding-view">
            <navi-bar
                ref="naviBarRef"
                class="mb20"
                :title="form.id ? '编辑课节' : '添加课节'"
                destroy
                :from="decodeURIComponent(route.query.from as string)"
            />
            <div class="overflow-auto mt20">
                <el-form
                    ref="dataFormRef"
                    class="w-[600px]"
                    :model="form"
                    :rules="dataRules"
                    formDialogRef
                    label-width="110px"
                    v-loading="loading"
                >
                    <el-row :gutter="24">
                        <el-col :span="24" class="mb35">
                            <el-form-item label="课节名称" prop="lessonName">
                                <el-input
                                    v-model.trim="form.lessonName"
                                    placeholder="请输入课节名称"
                                    maxlength="50"
                                    show-word-limit
                                />
                            </el-form-item>
                        </el-col>

                        <el-col :span="24" class="mb35">
                            <el-form-item label="关联书籍" prop="bookId">
                                <el-select
                                    v-model="form.bookId"
                                    filterable
                                    remote
                                    reserve-keyword
                                    clearable
                                    placeholder="请输入书名搜索"
                                    :remote-method="loadBookNames"
                                    :loading="bookNameLoading"
                                    @change="onBookChange"
                                >
                                    <el-option
                                        v-for="item in bookNameArray"
                                        :key="item.id"
                                        :label="`${item.title}-${item.author}`"
                                        :value="item.id"
                                    />
                                </el-select>
                            </el-form-item>
                        </el-col>

                        <el-col v-if="form.bookId" class="mb35" :span="24">
                            <el-form-item class="w-full" label="关联课件" prop="coursewareId">
                                <el-select
                                    v-model="form.coursewareId"
                                    placeholder="请选择课件"
                                    :loading="coursewareLoading"
                                >
                                    <el-option
                                        v-for="item in coursewareArray"
                                        :key="item.id"
                                        :label="`${item.coursewareName}-${item.press}`"
                                        :value="item.id"
                                        @click.native="onCoursewareChange(item)"
                                    />
                                </el-select>
                            </el-form-item>
                            <el-tooltip effect="dark" :content="$t('queryTree.refresh')" placement="top">
                                <el-button
                                    class="flushed"
                                    circle
                                    icon="Refresh"
                                    :disabled="coursewareLoading"
                                    @click="loadCoursewares()"
                                />
                            </el-tooltip>
                        </el-col>

                        <el-col :span="24" class="mb35">
                            <el-form-item label="课节类型" prop="type">
                                <el-radio-group v-model="form.type">
                                    <el-radio :label="0" border>正式课</el-radio>
                                    <el-radio :label="1" border>试听课</el-radio>
                                </el-radio-group>
                            </el-form-item>
                        </el-col>

                        <!-- <el-col :span="24" class="mb35">
							<el-form-item label="是否可发布" prop="canPublish">
								<el-switch
									v-model="form.canPublish"
									inline-prompt
									active-text="可发布"
									inactive-text="不可发布"
									:active-value="1"
									:inactive-value="0"
								/>
							</el-form-item>
						</el-col> -->

                        <el-col :span="24" class="mb35 mt20">
                            <el-form-item>
                                <el-tooltip
                                    v-if="form.id"
                                    class="box-item"
                                    content="已发布的课节无法删除"
                                    placement="top"
                                    :disabled="form.publishStatus != 1"
                                >
                                    <el-button
                                        type="danger"
                                        plain
                                        :disabled="form.publishStatus == 1"
                                        @click="handleDelete"
                                        >删除</el-button
                                    >
                                </el-tooltip>
                                <el-button @click="naviBarRef.back()">取消</el-button>
                                <el-button type="primary" :disabled="loading" @click="onSubmit">确认</el-button>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts" name="LessonDialog">
import { useMessage, useMessageBox } from '/@/hooks/message';
import { fetchBookNames, coursewareNames } from '/@/api/integraEdu/bookTrack';
import { lessonDetail, addLesson, editLesson, deleteLesson } from '/@/api/integraEdu/courseZone';
import NaviBar from '/@/components/NaviBar/index.vue';
import { debounce } from 'lodash-es';

const emit = defineEmits(['refresh']);
const naviBarRef = ref();
const route = useRoute();
const loading = ref(false);
const dataFormRef = ref();
const bookNameLoading = ref(false);
const bookNameArray = ref<any[]>([]);
const coursewareLoading = ref(false);
const coursewareArray = ref<any[]>([]);
const form = reactive({
    courseId: route.query.courseId as string,
    id: '',
    lessonName: '',
    bookId: '',
    type: 0,
    canPublish: 0,
    publishStatus: 0,
    coursewareId: '',
    coursewareName: '',
    press: '',
});

// watch(
// 	() => [form.lessonName, form.bookId, form.coursewareId, form.type],
// 	() => {
// 		if (!loading.value) {
// 			form.canPublish = 0;
// 		}
// 	}
// );

// 定义校验规则
const dataRules = ref({
    lessonName: [{ required: true, message: '课节名称不能为空', trigger: 'change' }],
    bookId: [{ required: true, message: '关联书籍不能为空', trigger: 'change' }],
    coursewareId: [{ required: true, message: '关联课件不能为空', trigger: 'change' }],
    type: [{ required: true, message: '课节类型不能为空', trigger: 'change' }],
    canPublish: [{ required: false, message: '是否可发布不能为空', trigger: 'change' }],
});

onMounted(() => {
    const lessonId = route.query.lessonId as string;
    if (lessonId) {
        form.id = lessonId;
        loading.value = true;
        lessonDetail(lessonId)
            .then((res) => {
                Object.assign(form, res.data);
                bookNameArray.value = [
                    {
                        id: res.data.bookId,
                        title: res.data.bookName,
                        author: res.data.author,
                    },
                ];
                coursewareArray.value = [
                    {
                        id: res.data.coursewareId,
                        coursewareName: res.data.coursewareName,
                        press: res.data.press,
                    },
                ];
                loadCoursewares();
            })
            .catch((err) => {
                useMessage().error(err.msg);
            })
            .finally(() => {
                loading.value = false;
            });
    }
});

// 加载关联书籍
const loadBookNames = debounce((query: string) => {
    if (query) {
        coursewareArray.value = [];
        bookNameLoading.value = true;
        fetchBookNames({ title: query })
            .then((res) => {
                bookNameArray.value = res.data || [];
            })
            .catch((err) => {
                useMessage().error(err.msg);
            })
            .finally(() => {
                bookNameLoading.value = false;
            });
    } else {
        bookNameArray.value = [];
    }
}, 500);

const onBookChange = () => {
    //清空课件数据
    form.coursewareId = '';
    form.coursewareName = '';
    form.press = '';
    coursewareArray.value = [];
    if (form.bookId) {
        loadCoursewares();
    }
};

// 加载关联课件
const loadCoursewares = debounce(() => {
    coursewareLoading.value = true;
    coursewareNames({ bookId: form.bookId })
        .then((res) => {
            coursewareArray.value = res.data || [];
        })
        .catch((err) => {
            useMessage().error(err.msg);
        })
        .finally(() => {
            coursewareLoading.value = false;
        });
}, 300);

const onCoursewareChange = (item: any) => {
    form.coursewareName = item.coursewareName;
    form.press = item.press;
};

// 提交
const onSubmit = async () => {
    const valid = await dataFormRef.value.validate().catch(() => {});
    if (!valid) return false;
    try {
        loading.value = true;
        form.id ? await editLesson(form) : await addLesson(form);
        useMessage().success(form.id ? '编辑成功' : '添加成功');
        emit('refresh');
        naviBarRef.value.back();
    } catch (err: any) {
        useMessage().error(err.msg);
    } finally {
        loading.value = false;
    }
};

// 删除课节
const handleDelete = async () => {
    try {
        await useMessageBox().confirm('您确定要删除此课节吗？<br/>删除后将无法恢复，且无法再使用该课节！', true);
    } catch {
        return;
    }
    try {
        await deleteLesson(route.query.courseId as string, form.id);
        useMessage().success('删除成功');
        naviBarRef.value.back();
    } catch (err: any) {
        useMessage().error(err.msg);
        return;
    }
};
</script>

<style>
.flushed {
    position: absolute;
    left: 100%;
    transform: translateY(-100%);
}
</style>
