<template>
    <el-dialog
        :title="isEdit ? '编辑课程' : '添加课程'"
        v-model="visible"
        :close-on-click-modal="false"
        width="1100"
        @close="handleCloseDialog"
    >
        <el-form
            ref="dataFormRef"
            :model="form"
            :rules="dataRules"
            formDialogRef
            label-width="120px"
            v-loading="loading"
        >
            <el-row :gutter="24">
                <el-col :span="24" class="mb20">
                    <el-form-item label="课程产品名称" prop="courseName">
                        <el-input
                            v-model.trim="form.courseName"
                            placeholder="请输入课程产品名称"
                            maxlength="50"
                            :disabled="isDisable"
                        />
                        <div class="text-gray-400">建议以年份+季节/主题+阶段进行命名</div>
                    </el-form-item>
                </el-col>

                <el-col :span="24" class="mb20">
                    <el-form-item label="课程类型" prop="courseTypeId">
                        <el-select v-model="form.courseTypeId" :disabled="isDisable" placeholder="请选择课程类型">
                            <el-option
                                v-for="item in courseTypeList"
                                :key="item.id"
                                :value="item.id"
                                :label="item.name"
                            />
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="24" class="mb20">
                    <el-form-item label="适学阶段" prop="stageId">
                        <el-select v-model="form.stageId" :disabled="isDisable" placeholder="请选择适学阶段">
                            <el-option
                                v-for="item in props.stageList"
                                :key="item.id"
                                :value="item.id"
                                :label="item.stageName"
                            />
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="24" class="mb20">
                    <el-form-item label="收费方式" prop="chargeMethod">
                        <el-radio-group v-model="form.chargeMethod" :disabled="isDisable" @change="changeChargeMethod">
                            <el-radio :label="ChargeMethodEnum.shop">门店设置</el-radio>
                            <el-radio :label="ChargeMethodEnum.custom">自定义</el-radio>
                        </el-radio-group>
                    </el-form-item>
                </el-col>
                <el-col :span="24" class="mb20" v-if="form.chargeMethod === ChargeMethodEnum.custom">
                    <el-form-item label="课时费标准" prop="customizeFee">
                        <div class="flex items-center">
                            <custom-input-number
                                :disabled="isDisable"
                                v-model="form.customizeFee"
                                placeholder="请输入课时费标准"
                            ></custom-input-number>
                            <span class="flex-shrink-0 ml-5">元/人次</span>
                        </div>
                    </el-form-item>
                </el-col>
                <el-col :span="24" class="mb20">
                    <el-form-item label="授权门店" prop="ssClassAuthInfoDTOList">
                        <YdTransfer
                            ref="transferRef"
                            v-model="selectedDevices"
                            :dataSource="transferOptions"
                            rowKey="key"
                            :titles="['未选中门店', '选中门店']"
                            filterable
                            :filterPlaceholder="['请输入要搜索的门店', '请输入要搜索的门店']"
                            @change="handleDeviceChange"
                        >
                            <template #default="{ item }">
                                <el-tooltip :content="item.label" placement="top" :show-after="500">
                                    <span>{{ item.label }}</span>
                                </el-tooltip>
                            </template>
                        </YdTransfer>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="visible = false">取消</el-button>
                <el-button type="primary" :disabled="loading" @click="onSubmit">确认</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script setup lang="ts" name="CourseDialog">
import { useMessage } from '/@/hooks/message';
import { addCourse, editCourse } from '/@/api/integraEdu/courseZone';
import { allOKCourseTypeApi, getAuthStoreApi, getCourseByIdApi } from '/@/api/eduConnect/classType';
import YdTransfer from '/@/components/YdTransfer/index.vue'; // 路径根据您的项目结构调整
import { ChargeMethodEnum } from '/@/utils/classFee';
import { ElInputNumber } from 'element-plus';
import customInputNumber from '/@/components/custom-input-number/index.vue';
import { PropType } from 'vue';
import { StageItem } from '/@/types/common';
interface Option {
    key: number;
    label: string;
}
// 定义接口类型
interface ClassRoomDevice {
    id: number;
    campusName: string;
    campusNo: string;
    isAuthorized: boolean;
}
const selectedDevices = ref<Option[]>([]);
const transferRef = ref();
const emit = defineEmits(['refresh']);
const dataFormRef = ref();
const visible = ref(false);
const loading = ref(false);
const classRoomDevice = ref<ClassRoomDevice[]>([]);
const defaultForm = {
    id: '',
    courseCode: '',
    courseName: '',
    stageId: '',
    courseTypeId: '',
    chargeMethod: 0,
    customizeFee: 0,
    authStoreIds: [],
};
const form = reactive<{
    courseName: string;
    stageId: string;
    courseTypeId: number | string;
    chargeMethod: number;
    customizeFee: number;
    authStoreIds: number[];
    id?: string;
    courseCode?: string;
}>({
    ...defaultForm,
});

const props = defineProps({
    stageList: {
        type: Array as PropType<StageItem[]>,
        default: () => [],
    },
});
const courseTypeList = ref<any>([]);
const transferOptions = ref<Option[]>([]);
const isEdit = ref(false);
const isDisable = ref(false);
const preventInvalidKeys = (event: any) => {
    const invalidKeys = ['e', 'E', '+', '-'];
    if (invalidKeys.includes(event.key)) {
        event.preventDefault();
    }
};
const changeChargeMethod = () => {
    form.customizeFee = 0;
    nextTick(() => {
        const inputNumber = inputNumberRef.value;
        if (inputNumber) {
            // 获取内部的原生 input 元素
            const inputEl = inputNumber.$el.querySelector('input');
            if (inputEl) {
                // 添加 keydown 事件监听器
                inputEl.addEventListener('keydown', preventInvalidKeys);
            }
        }
    });
};
const inputNumberRef = ref<InstanceType<typeof ElInputNumber> | null>();

const handleDeviceChange = (value: Option[]) => {
    form.authStoreIds = value.map((item: Option) => item.key);
};
const validatorCustomizeFee = (rule: any, value: any, callback: any) => {
    if (value === '') {
        callback(new Error('请输入有效数字'));
    } else if (value < 0) {
        callback(new Error('请输入有效值'));
    } else {
        callback();
    }
};

const dataRules = ref({
    courseName: [{ required: true, message: '课程产品名称不能为空', trigger: 'blur' }],
    stageId: [{ required: true, message: '适学阶段不能为空', trigger: 'change' }],
    courseTypeId: [{ required: true, message: '课程类型不能为空', trigger: 'change' }],
    customizeFee: [{ validator: validatorCustomizeFee, required: true, trigger: 'change' }],
    chargeMethod: [{ required: true, message: '收费方式不能为空', trigger: 'change' }],
});
const getCourseType = async () => {
    try {
        await allOKCourseTypeApi().then((res) => {
            courseTypeList.value = res.data;
        });
    } catch (error) {
        console.error(error, 'getCourseType接口出错！！！');
    }
};

const generateTransferOptions = () => {
    transferOptions.value = []; // 清空之前的数据，避免重复添加
    classRoomDevice.value.forEach((item: ClassRoomDevice) => {
        transferOptions.value.push({
            label: item.campusName,
            key: Number(item.id),
        });
        if (item.isAuthorized) {
            selectedDevices.value.push({
                label: item.campusName,
                key: Number(item.id),
            });
        }
    });
};
// 打开弹窗
const openDialog = async (id?: string) => {
    visible.value = true;
    loading.value = true;
    isEdit.value = !!id;
    await getCourseType();
    await nextTick(() => {
        dataFormRef.value?.resetFields();
    });
    const res = await getAuthStoreApi({
        courseId: id || '',
    });
    classRoomDevice.value = res.data || [];
    generateTransferOptions();
    if (id) {
        form.id = id;
        await getDetails(id);
        handleDeviceChange(selectedDevices.value);
    } else {
        form.id = '';
        isDisable.value = false;
    }
    loading.value = false;
};
const getDetails = async (id: string) => {
    try {
        await getCourseByIdApi({ id }).then((res) => {
            form.stageId = res.data.stageId;
            form.courseName = res.data.courseName;
            form.customizeFee = res.data.courseFee;
            form.courseTypeId = res.data.courseTypeId && Number(res.data.courseTypeId);
            form.chargeMethod = res.data.chargeMethod;
            form.courseCode = res.data.courseCode;
            isDisable.value = res.data.publishStatus != 0;
        });
    } catch (error) {
        console.error(error, 'getCourseByIdApi接口错误！！！');
    }
};
// 提交
const onSubmit = async () => {
    const valid = await dataFormRef.value.validate().catch(() => {});
    if (!valid) return false;

    try {
        loading.value = true;
        const url = form.id ? editCourse : addCourse;
        await url(form);
        useMessage().success(form.id ? '编辑成功' : '添加成功');
        visible.value = false;
        emit('refresh');
    } catch (err: any) {
        useMessage().error(err.msg);
    } finally {
        loading.value = false;
    }
};
const handleCloseDialog = () => {
    if (transferRef.value) {
        transferRef.value.clearSearch();
    }
    transferOptions.value = [];
    selectedDevices.value = [];
    form.authStoreIds = [];
    const activeElement = document.activeElement as HTMLElement;
    if (activeElement) {
        activeElement.blur();
    }
};

// 暴露变量
defineExpose({
    openDialog,
});
</script>
