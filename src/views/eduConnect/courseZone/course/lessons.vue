<template>
    <div class="layout-padding">
        <div class="layout-padding-auto layout-padding-view">
            <navi-bar
                class="mb20"
                :title="courseData.courseName"
                :from="decodeURIComponent(route.query.from as string)"
            />
            <el-descriptions :column="4">
                <el-descriptions-item label="课程产品名称:">
                    <template v-if="!courseNameEdit">
                        <span>{{ courseData.courseName }}</span>
                        <el-button
                            :type="status ? 'primary' : 'info'"
                            :disabled="!status"
                            text
                            icon="edit"
                            @click="handleEditCourseName"
                        />
                    </template>
                    <template v-else>
                        <el-input
                            class="w-[200px] mr10"
                            v-model.trim="courseData.courseNameTemp"
                            placeholder="请输入课程产品名称"
                            maxlength="50"
                            @keyup.enter="handleEditCourse"
                        />
                        <el-button type="danger" plain icon="close" @click="handleEditCourseCancel" />
                        <el-button
                            type="primary"
                            plain
                            icon="check"
                            :disabled="!courseData.courseNameTemp"
                            @click="handleEditCourse"
                        />
                    </template>
                </el-descriptions-item>
                <el-descriptions-item label="适学阶段:">
                    <template v-if="!courseStageEdit">
                        <el-tag size="small">{{ getStageName(courseData.stageId) }}</el-tag>
                        <el-button
                            v-if="false"
                            :type="status ? 'primary' : 'info'"
                            :disabled="!status"
                            text
                            icon="edit"
                            @click="handleEditCourseStage"
                        />
                    </template>
                    <template v-else>
                        <el-select class="w-[100px] mr10" v-model="courseData.stageIdTemp" placeholder="请选择">
                            <el-option
                                v-for="item in stageList"
                                :key="item.id"
                                :value="item.id"
                                :label="item.stageName"
                            />
                        </el-select>
                        <el-button type="danger" plain icon="close" @click="handleEditCourseCancel" />
                        <el-button type="primary" plain icon="check" @click="handleEditCourse" />
                    </template>
                </el-descriptions-item>
                <el-descriptions-item label="课节数量:">{{ courseData.lessonCount }}</el-descriptions-item>
                <el-descriptions-item label="最近发布时间:"> {{ courseData.publishTime ?? '/' }} </el-descriptions-item>
            </el-descriptions>
            <el-row class="mt10">
                <el-button
                    class="ml10"
                    icon="promotion"
                    :type="status ? 'primary' : 'info'"
                    :disabled="!status"
                    @click="handlePublishCourse"
                >
                    发布课程
                </el-button>
                <el-tooltip
                    v-if="courseData.id && !courseData.lessonCount && courseData.publishStatus == 0"
                    class="box-item"
                    content="已有课节时无法删除课程"
                    placement="top"
                    :disabled="!courseData.lessonCount"
                >
                    <el-button
                        plain
                        icon="Delete"
                        :type="status ? 'danger' : 'info'"
                        :disabled="!!courseData.lessonCount"
                        @click="handleDeleteCourse"
                    >
                        删除课程
                    </el-button>
                </el-tooltip>
                <el-switch
                    v-model="state.queryForm.disable"
                    class="ml-2"
                    width="60"
                    inline-prompt
                    active-text="未停用"
                    inactive-text="停用"
                    @change="chagneSwitch"
                />
            </el-row>
            <el-divider />

            <el-row>
                <div class="mb8" style="width: 100%">
                    <el-button
                        icon="folder-add"
                        :type="status ? 'primary' : 'info'"
                        :disabled="!status"
                        class="ml10"
                        @click="handleAddLesson"
                        >添加课节</el-button
                    >
                    <right-toolbar
                        class="ml10 mr20"
                        :search="false"
                        :export="false"
                        style="float: right"
                        @queryTable="getDataList"
                        @exportExcel="exportExcel"
                    />
                </div>
            </el-row>
            <el-table
                :data="state.dataList"
                v-loading="state.loading"
                border
                :cell-style="tableStyle.cellStyle"
                :header-cell-style="tableStyle.headerCellStyle"
            >
                <el-table-column label="" width="50">
                    <template #header>
                        <el-icon>
                            <el-tooltip content="拖动排序" placement="top">
                                <WarningFilled />
                            </el-tooltip>
                        </el-icon>
                    </template>
                    <template #default>
                        <div class="move" style="cursor: move">
                            <el-icon>
                                <Rank />
                            </el-icon>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column type="index" label="序号" width="60" />
                <el-table-column prop="lessonName" label="课节名称" show-overflow-tooltip />
                <el-table-column prop="bookName" label="关联书籍" show-overflow-tooltip>
                    <template #default="scope"> {{ scope.row.bookName ?? '/' }} </template>
                </el-table-column>
                <el-table-column prop="coursewareName" label="关联课件" show-overflow-tooltip>
                    <template #default="scope"> {{ scope.row.coursewareName ?? '/' }} </template>
                </el-table-column>
                <el-table-column prop="type" label="课节类型" show-overflow-tooltip>
                    <template #default="scope">
                        <el-tag v-if="scope.row.type == 0" type="primary">正式课</el-tag>
                        <el-tag v-else type="warning">试听课</el-tag>
                    </template>
                </el-table-column>
                <el-table-column v-if="0" prop="canPublish" label="是否可发布" show-overflow-tooltip>
                    <template #default="scope">
                        <el-tag v-if="scope.row.canPublish == 1" type="success">可发布</el-tag>
                        <el-tag v-else type="info">不可发布</el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="publishStatus" label="发布状态" show-overflow-tooltip>
                    <template #default="scope">
                        <el-tag v-if="scope.row.publishStatus == 1" type="success">已发布</el-tag>
                        <el-tag v-else type="info">未发布</el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="updateTime" label="最近更新日期" show-overflow-tooltip />
                <el-table-column label="操作" width="100" fixed="right">
                    <template #default="scope">
                        <el-button
                            icon="edit-pen"
                            text
                            :type="status ? 'primary' : 'info'"
                            :disabled="!status"
                            @click="handleEditLesson(scope.row.id)"
                        >
                            编辑
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>
    </div>
</template>

<script setup lang="ts" name="systemLesson">
import { BasicTableProps, useTable } from '/@/hooks/table';
import {
    fetchCourseDetail,
    editCourse,
    deleteCourse,
    publishCourse,
    fetchLessonList,
    sortLessons,
    changeDeviceState,
} from '/@/api/integraEdu/courseZone';
import { useMessage, useMessageBox } from '/@/hooks/message';
import mittBus from '/@/utils/mitt';
import NaviBar from '/@/components/NaviBar/index.vue';
import Sortable from 'sortablejs';
import { useStageList, useStageDisplay } from '/@/hooks/stage';

const route = useRoute();
const router = useRouter();
const courseData = reactive<any>({});
const { stages: stageList } = useStageList();
const { getStageName } = useStageDisplay();
const state: BasicTableProps = reactive<BasicTableProps>({
    queryForm: {
        courseId: route.query.courseId as string,
        id: '',
        disable: '',
    },
    isPage: false,
    pagination: {},
    pageList: fetchLessonList,
});
const { getDataList, downBlobFile, tableStyle } = useTable(state);
const courseNameEdit = ref(false);
const courseStageEdit = ref(false);
const status = ref(true);

onMounted(() => {
    rowDrop();
    loadCourseDetail();
});
const setDeviceState = async () => {
    await changeDeviceState({
        id: state.queryForm.courseId,
        disable: state.queryForm.disable == 1 ? 0 : 1,
    });
    getDataList(false);
};
const chagneSwitch = async () => {
    try {
        if (!state.queryForm.disable) {
            useMessageBox()
                .confirm('你确定要停止使用本课程吗？')
                .then(() => {
                    setDeviceState();
                    // state.queryForm.disable = false;
                    //所有按钮均要置灰
                    status.value = false;
                })
                .catch(() => {
                    state.queryForm.disable = true;
                    status.value = true;
                });
        } else {
            await setDeviceState();
            status.value = true;
        }
    } catch {
        return;
    }
};

const loadCourseDetail = () => {
    fetchCourseDetail(state.queryForm.courseId)
        .then((res) => {
            Object.assign(courseData, {
                ...res.data,
                courseNameTemp: res.data.courseName || '',
                stageIdTemp: res.data.stageId || 0,
            });
            state.queryForm.disable = courseData.disable == 0;
            if (state.queryForm.disable === true) {
                status.value = true;
            } else {
                status.value = false;
            }
        })
        .catch((err) => {
            useMessage().error(err.msg);
        });
};

const rowDrop = () => {
    const tbody = document.querySelector('.el-table__body-wrapper tbody') as HTMLElement;
    Sortable.create(tbody, {
        handle: '.move',
        animation: 150,
        ghostClass: 'ghost',
        fallbackOnBody: true,
        onEnd({ newIndex, oldIndex }) {
            if (newIndex == undefined || oldIndex == undefined || newIndex == oldIndex) {
                return;
            }
            const newDataList = [...(state.dataList || [])];
            const element = newDataList[oldIndex];
            const newArray = [...newDataList.slice(0, oldIndex), ...newDataList.slice(oldIndex + 1)];
            newArray.splice(newIndex, 0, element);
            newArray.forEach((item, index) => {
                item.lessonOrder = index + 1;
            });
            state.loading = true;
            sortLessons(state.queryForm.courseId, newArray)
                .then(() => {
                    state.dataList = [];
                    nextTick(() => {
                        state.dataList = newArray;
                    });
                })
                .catch((err) => {
                    useMessage().error(err.msg);
                    const temp = [...(state.dataList || [])];
                    state.dataList = [];
                    nextTick(() => {
                        state.dataList = temp;
                    });
                })
                .finally(() => {
                    state.loading = false;
                });
        },
    });
};

// 导出excel
const exportExcel = () => {
    downBlobFile('/admin/lesson/export', Object.assign(state.queryForm, { ids: [] }), 'lesson.xlsx');
};

const handleEditCourseName = () => {
    courseNameEdit.value = true;
    courseStageEdit.value = false;
    courseData.stageIdTemp = courseData.stageId;
};

const handleEditCourseStage = () => {
    courseNameEdit.value = false;
    courseStageEdit.value = true;
    courseData.courseNameTemp = courseData.courseName;
};
// 编辑课程
const handleEditCourse = () => {
    courseData.courseName = courseData.courseNameTemp;
    courseData.stageId = courseData.stageIdTemp;
    courseData.customizeFee = courseData.courseFee;
    courseNameEdit.value = false;
    courseStageEdit.value = false;
    editCourse(courseData)
        .then(() => {
            useMessage().success('修改成功');
        })
        .catch((err) => {
            useMessage().error(err.msg);
        });
};
// 取消编辑课程
const handleEditCourseCancel = () => {
    courseNameEdit.value = false;
    courseStageEdit.value = false;
    courseData.courseNameTemp = courseData.courseName;
    courseData.stageIdTemp = courseData.stageId;
};
// 发布课程
const handlePublishCourse = async () => {
    try {
        await useMessageBox().confirm('您确定要发布此课程吗？');
    } catch {
        return;
    }
    publishCourse({
        courseId: courseData.id,
        courseCode: courseData.courseCode,
        courseName: courseData.courseName,
        stageId: courseData.stageId,
    })
        .then(() => {
            useMessage().success('发布成功');
            loadCourseDetail();
            getDataList();
        })
        .catch((err) => {
            useMessage().error(err.msg);
        });
};
// 删除课程
const handleDeleteCourse = async () => {
    try {
        await useMessageBox().confirm('您确定要删除此课程产品吗？<br/>删除后将无法恢复，且无法再使用该课程产品', true);
    } catch {
        return;
    }

    try {
        await deleteCourse(state.queryForm.courseId);
        useMessage().success('删除成功');
        goBack();
    } catch (err: any) {
        useMessage().error(err.msg);
    }
};

// 添加课节
const handleAddLesson = () => {
    router.push({
        path: '/eduConnect/courseZone/course/lessonEdit',
        query: { courseId: state.queryForm.courseId, from: encodeURIComponent(router.currentRoute.value.fullPath) },
    });
};
// 编辑课节
const handleEditLesson = (id: string) => {
    router.push({
        path: '/eduConnect/courseZone/course/lessonEdit',
        query: {
            courseId: state.queryForm.courseId,
            lessonId: id,
            from: encodeURIComponent(router.currentRoute.value.fullPath),
        },
    });
};

const goBack = () => {
    router.back();
    mittBus.emit('onCurrentContextmenuClick', { contextMenuClickId: 1, ...route });
};
</script>
