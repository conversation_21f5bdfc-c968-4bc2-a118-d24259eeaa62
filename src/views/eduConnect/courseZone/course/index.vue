<template>
    <div class="layout-padding">
        <div class="layout-padding-auto layout-padding-view">
            <el-row v-show="showSearch">
                <el-form :model="state.queryForm" ref="queryRef" :inline="true" @keyup.enter="getDataList">
                    <el-form-item label="课程产品名称" prop="courseName">
                        <el-input placeholder="请输入课程产品名称" v-model="state.queryForm.courseName" />
                    </el-form-item>
                    <el-form-item label="适学阶段" prop="stageId">
                        <el-select v-model="state.queryForm.stageId" placeholder="请选择适学阶段">
                            <el-option
                                v-for="item in stageList"
                                :key="item.id"
                                :value="item.id"
                                :label="item.stageName"
                            />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="课程类型" prop="courseTypeId">
                        <el-select v-model="state.queryForm.courseTypeId" placeholder="请选择课程类型">
                            <el-option
                                v-for="item in courseTypeList"
                                :key="item.id"
                                :value="item.id"
                                :label="item.name"
                            />
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-button icon="search" type="primary" @click="getDataList"> 查询 </el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-row>
            <el-row>
                <div class="mb8" style="width: 100%">
                    <el-button icon="folder-add" type="primary" @click="formDialogRef.openDialog()">
                        添加课程
                    </el-button>
                    <right-toolbar
                        class="ml10 mr20"
                        v-model:showSearch="showSearch"
                        :export="false"
                        style="float: right"
                        @queryTable="getDataList"
                        @exportExcel="exportExcel"
                    />
                </div>
            </el-row>
            <el-table
                :data="state.dataList"
                v-loading="state.loading"
                border
                :cell-style="tableStyle.cellStyle"
                :header-cell-style="tableStyle.headerCellStyle"
            >
                <el-table-column type="index" label="序号" width="60" />
                <el-table-column prop="courseCode" label="课程编码" show-overflow-tooltip width="120" />
                <el-table-column prop="courseName" label="课程产品名称" show-overflow-tooltip width="180" />
                <el-table-column prop="courseTypeName" label="课程类型" show-overflow-tooltip width="110">
                    <template #default="scope"> {{ scope.row.courseTypeName ?? '/' }} </template>
                </el-table-column>
                <el-table-column prop="stageId" label="适学阶段" show-overflow-tooltip width="110">
                    <template #default="scope">
                        <el-tag type="primary">{{ getStageName(scope.row.stageId) }}</el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="chargeMethod" label="收费方式" show-overflow-tooltip width="120">
                    <template #default="scope">
                        <span v-if="scope.row.chargeMethod == ChargeMethodEnum.shop" type="success">门店设置</span>
                        <span v-else-if="scope.row.chargeMethod == ChargeMethodEnum.custom" type="success">自定义</span>
                        <span v-else>/</span>
                    </template>
                </el-table-column>
                <el-table-column prop="courseFee" label="课时费标准" show-overflow-tooltip width="100">
                    <template #default="scope">
                        {{ setCourseFee(scope.row.courseFee, scope.row.chargeMethod == ChargeMethodEnum.custom) }}
                    </template>
                </el-table-column>
                <el-table-column prop="authStoreCount" label="授权门店" show-overflow-tooltip width="100">
                    <template #default="{ row }">
                        <el-button circle @click="formDialogRef?.openDialog(row.id)">{{
                            row.authStoreCount
                        }}</el-button>
                    </template>
                </el-table-column>
                <el-table-column prop="lessonCount" label="课节数量" show-overflow-tooltip width="110" />
                <el-table-column prop="publishStatus" label="发布状态" show-overflow-tooltip width="120">
                    <template #default="scope">
                        <el-tag v-if="scope.row.publishStatus == 1" type="success">已发布</el-tag>
                        <el-tag v-else type="info">未发布</el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="publishTime" label="版本记录" show-overflow-tooltip width="180">
                    <template #default="scope"> {{ scope.row.publishTime ?? '/' }} </template>
                </el-table-column>

                <el-table-column prop="disable" label="课程状态" show-overflow-tooltip width="120">
                    <template #default="scope">
                        <el-link v-if="scope.row.disable == 1" type="danger" :underline="false">停用</el-link>
                        <el-link v-else type="info" :underline="false">未停用</el-link>
                    </template>
                </el-table-column>

                <el-table-column prop="updateTime" label="更新日期" show-overflow-tooltip width="180" />
                <el-table-column label="操作" width="180" fixed="right">
                    <template #default="scope">
                        <el-button text type="primary" icon="Edit" @click="formDialogRef?.openDialog(scope.row.id)">
                            编辑
                        </el-button>
                        <el-button text type="primary" icon="Tools" @click="handleLessonManage(scope.row.id)">
                            课节管理
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
            <pagination
                @size-change="sizeChangeHandle"
                @current-change="currentChangeHandle"
                v-bind="state.pagination"
            />
        </div>

        <!-- 编辑、添加  -->
        <form-dialog ref="formDialogRef" @refresh="getDataList(false)" :stageList="stageList" />
    </div>
</template>

<script setup lang="ts" name="systemCourse">
import { BasicTableProps, useTable } from '/@/hooks/table';
import { fetchCourseList } from '/@/api/integraEdu/courseZone';
import { allCourseTypeApi } from '/@/api/eduConnect/classType';
import { setCourseFee, ChargeMethodEnum } from '/@/utils/classFee';
import { useStageList, useStageDisplay } from '/@/hooks/stage';
const courseTypeList = ref();
const { stages: stageList } = useStageList();
const { getStageName } = useStageDisplay();

const getCourseType = () => {
    allCourseTypeApi().then((res) => {
        courseTypeList.value = res.data;
    });
};
getCourseType();
const FormDialog = defineAsyncComponent(() => import('./components/courseForm.vue'));
const formDialogRef = ref();
const router = useRouter();
const queryRef = ref();
const showSearch = ref(true);
const state: BasicTableProps = reactive<BasicTableProps>({
    queryForm: {},
    pageList: fetchCourseList,
});
const { getDataList, currentChangeHandle, sizeChangeHandle, downBlobFile, tableStyle } = useTable(state);

// 清空搜索条件
const resetQuery = () => {
    queryRef.value?.resetFields();
    getDataList();
};

// 导出excel
const exportExcel = () => {
    downBlobFile(
        '/teaching/course/page',
        Object.assign(state.queryForm, { ids: state.dataList?.map((item) => item.id) }),
        'course.xlsx'
    );
};

// 课节管理
const handleLessonManage = (courseId: string) => {
    router.push({
        path: '/eduConnect/courseZone/course/lessons',
        query: { courseId, from: encodeURIComponent(router.currentRoute.value.fullPath) },
    });
};
</script>
