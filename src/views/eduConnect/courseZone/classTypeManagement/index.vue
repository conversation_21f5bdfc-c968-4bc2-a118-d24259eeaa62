<template>
    <div class="layout-padding">
        <div class="layout-padding-auto layout-padding-view" v-if="isMask">
            <el-row class="top-3" v-show="showSearch">
                <el-form
                    :inline="true"
                    :model="state.queryForm"
                    ref="queryRef"
                    label-width="80px"
                    @keyup.enter="getDataList"
                >
                    <el-form-item label="课程类型" prop="name">
                        <el-input v-model="state.queryForm.name" style="width: 240px" placeholder="请输入课程类型" />
                    </el-form-item>
                    <el-form-item>
                        <el-button @click="getDataList" formDialogRef icon="search" type="primary"> 查询 </el-button>
                        <el-button @click="resetQuery" formDialogRef icon="Refresh">重置 </el-button>
                    </el-form-item>
                </el-form>
            </el-row>
            <el-row>
                <div class="mb8" style="width: 100%">
                    <el-button @click="visible = true" type="primary"> 新增课程类型 </el-button>
                    <right-toolbar
                        v-model:showSearch="showSearch"
                        class="ml10 mr20"
                        style="float: right"
                        @queryTable="getDataList"
                    ></right-toolbar>
                </div>
            </el-row>
            <el-table
                :data="state.dataList"
                v-loading="state.loading"
                border
                :cell-style="tableStyle.cellStyle"
                :header-cell-style="tableStyle.headerCellStyle"
                @sort-change="sortChangeHandle"
            >
                <el-table-column type="index" label="序号" width="60" />
                <el-table-column prop="name" label="课程类型" show-overflow-tooltip min-width="100px" />
                <el-table-column prop="courseCount" label="已开课程数" show-overflow-tooltip min-width="100px" />
                <!-- <el-table-column prop="storeName" label="招生人数" show-overflow-tooltip min-width="100px" />
                <el-table-column prop="storeName" label="续费人数" show-overflow-tooltip min-width="100px" />
                <el-table-column prop="storeName" label="续费率" show-overflow-tooltip min-width="200px" /> -->
                <el-table-column prop="status" label="状态" show-overflow-tooltip min-width="100px">
                    <template #default="scope">
                        <el-link v-if="scope.row.status == 2" type="danger" :underline="false">停用</el-link>
                        <el-link v-else type="info" :underline="false">未停用</el-link>
                    </template>
                </el-table-column>
                <el-table-column prop="updateBy" label="操作人" show-overflow-tooltip min-width="100px" />
                <el-table-column prop="updateTime" label="操作时间 " show-overflow-tooltip min-width="100px" />
                <el-table-column label="操作" width="200" fixed="right">
                    <template #default="scope">
                        <el-button text type="primary" @click="openEdit(scope.row.id)"> 编辑 </el-button>
                        <el-button text type="primary" @click="stopUse(scope.row.id, scope.row.status, scope.row)">
                            {{ EStatus.Normal === scope.row.status ? '停用' : '启用' }}
                        </el-button>
                        <!-- <el-button text type="primary" @click="delClassType(scope.row)"> 删除 </el-button> -->
                    </template>
                </el-table-column>
            </el-table>
            <pagination
                @size-change="sizeChangeHandle"
                @current-change="currentChangeHandle"
                v-bind="state.pagination"
            />
        </div>
        <el-dialog
            v-model="visible"
            :title="isEdit ? '编辑课程类型' : '新增课程类型'"
            :close-on-click-modal="false"
            style="width: 550px"
        >
            <el-form
                ref="dataFormRef"
                :inline="true"
                :model="form"
                :rules="dataRules"
                label-width="120px"
                label-position="right"
            >
                <el-row :gutter="24">
                    <el-col :span="24" class="mb20">
                        <el-form-item label="课程类型名称" prop="name" class="w-full">
                            <el-input v-model="form.name" placeholder="请输入课程类型名称" />
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="visible = false">取消</el-button>
                    <el-button type="primary" @click="handleSubmit()"> 确定 </el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup lang="ts" name="courseConsumption">
import { BasicTableProps, useTable } from '/@/hooks/table';
import { getCourseType, addCoureTypeApi, editCoureTypeApi, coureTypeDetailsApi } from '/@/api/eduConnect/classType';
import { useMessage } from '/@/hooks/message';
import { ElMessageBox } from 'element-plus';
enum EStatus {
    Stop = 2,
    Normal = 1,
}
const isMask = ref(true);
// 定义变量内容
const queryRef = ref();
const showSearch = ref(true);
const queryDate = ref();

const visible = ref(false);
const isEdit = ref(false);
const form = reactive({
    name: '',
    id: '',
});
const dataFormRef = ref<any>();
const state: BasicTableProps = reactive<BasicTableProps>({
    queryForm: {},
    isShowWarning: true,
    pageList: getCourseType,
});
watch(visible, (val: boolean) => {
    form.id = '';
    form.name = '';
    dataFormRef.value?.resetFields();
    if (!val) {
        isEdit.value = false;
    }
});
// 定义校验规则
const dataRules = ref({
    name: [{ required: true, message: '课程类型名称不能为空', trigger: 'change' }],
});
//  table hook
const { getDataList, currentChangeHandle, sizeChangeHandle, sortChangeHandle, tableStyle } = useTable(state);
// 清空搜索条件
const resetQuery = () => {
    queryRef.value?.resetFields();
    queryDate.value = undefined;
    state.queryForm = {};
    getDataList();
};
const openEdit = async (id: number) => {
    isEdit.value = true;
    visible.value = true;
    try {
        const res = await coureTypeDetailsApi({
            id: id,
        });
        form.name = res.data[0].name;
        form.id = res.data[0].id;
    } catch (error) {
        useMessage().error('详情获取失败');
    }
};
const stopUse = (id: number, status: EStatus, item: any) => {
    ElMessageBox.confirm(
        status == EStatus.Normal
            ? `停用后，该课程类型将无法关联课程，请谨慎操作！确定停用【${item.name}】？`
            : `确定启用【${item.name}】？`,
        '',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        }
    ).then(async () => {
        try {
            await editCoureTypeApi({
                id: id,
                status: status == EStatus.Normal ? EStatus.Stop : EStatus.Normal,
            });
            useMessage().success('操作成功');
            getDataList();
        } catch (error) {
            useMessage().error('操作失败');
        }
    });
};
const handleSubmit = async () => {
    if (!dataFormRef.value) return;
    await dataFormRef.value.validate((valid: boolean) => {
        if (valid) {
            const url = isEdit.value ? editCoureTypeApi : addCoureTypeApi;
            url({ ...form })
                .then(() => {
                    visible.value = false;
                    getDataList();
                })
                .catch((err) => {
                    useMessage().error(err.msg);
                });
        }
    });
};
</script>

<style scoped lang="scss"></style>
