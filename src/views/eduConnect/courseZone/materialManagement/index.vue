<template>
    <div class="layout-padding">
        <div class="layout-padding-auto layout-padding-view">
            <el-row>
                <div class="mb8" style="width: 100%">
                    <el-button
                        icon="folder-add"
                        type="primary"
                        class="ml10"
                        @click="(catalogueVisible = true), (isEdit = false)"
                    >
                        新增目录
                    </el-button>
                    <right-toolbar
                        class="ml10 mr20"
                        :search="false"
                        :export="false"
                        style="float: right"
                        @queryTable="getDataList(false)"
                    />
                </div>
            </el-row>
            <el-table
                :data="state.dataList?.map((item) => ({ ...item, hasChildren: item.hasChild > 0 }))"
                style="width: 100%; margin-bottom: 20px"
                v-loading="state.loading"
                ref="tableRef"
                row-key="id"
                border
                lazy
                :load="load"
                :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
            >
                <el-table-column prop="contentsName" label="目录名称" align="left" />
                <el-table-column label="操作" width="260" fixed="right">
                    <template #default="scope">
                        <el-button type="primary" link @click="handleEdit(scope.row)">编辑名称</el-button>
                        <el-button
                            type="primary"
                            link
                            @click="formDialogRef.openDialog(scope.row.id, scope.row.contentsName)"
                            v-if="!scope.row.pid"
                        >
                            授权门店({{ scope.row.authTotal || 0 }})
                        </el-button>
                        <el-button type="primary" link @click="handleView(scope.row)" v-if="scope.row.pid">
                            查看资料
                        </el-button>
                        <el-button type="primary" link @click="handleDel(scope.row)"> 删除 </el-button>
                    </template>
                </el-table-column>
            </el-table>
            <pagination
                @size-change="sizeChangeHandle"
                @current-change="currentChangeHandle"
                v-bind="state.pagination"
            />
        </div>
        <el-dialog v-model="catalogueVisible" :title="isEdit ? '编辑目录' : '新增目录'" width="600" @close="onCancel">
            <el-form ref="dataFormRef" :model="form" :rules="dataRules" formDialogRef label-width="120px">
                <el-row :gutter="24">
                    <el-col :span="24" class="mb20">
                        <el-form-item label="父级目录" prop="pid">
                            <el-select v-model="form.pid" placeholder="请选择父级目录" :disabled="isEdit">
                                <el-option label="一级目录（顶级）" :value="0" />
                                <el-option
                                    v-for="item in directoryList"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24" class="mb20">
                        <el-form-item label="新建目录名称" prop="contentsName">
                            <el-input
                                v-model="form.contentsName"
                                :maxlength="20"
                                show-word-limit
                                placeholder="请输入新建目录名称"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="onCancel">取 消</el-button>
                    <el-button type="primary" @click="onSubmit" :disabled="loading">确 认</el-button>
                </span>
            </template>
        </el-dialog>

        <!-- 授权门店 -->
        <FormDialog ref="formDialogRef" @refresh="getDataList(false)" />
    </div>
</template>

<script setup lang="ts">
import { BasicTableProps, useTable } from '/@/hooks/table';
import {
    informationListApi,
    saveInformationApi,
    childInformationApi,
    editInformationApi,
    delInformationApi,
    informationDirectoryApi,
} from '/@/api/eduConnect/information';
import { useMessage } from '/@/hooks/message';
import { ElMessageBox } from 'element-plus';
import router from '/@/router';
const FormDialog = defineAsyncComponent(() => import('./components/authorizationStore.vue'));
const formDialogRef = ref();
const state: BasicTableProps = reactive<BasicTableProps>({
    queryForm: {},
    isPage: true,
    pagination: {},
    pageList: informationListApi,
});
const { getDataList, sizeChangeHandle, currentChangeHandle } = useTable(state);
const catalogueVisible = ref(false);
const dataFormRef = ref();
const isEdit = ref(false);
const currentRow = ref();
const tableRef = ref();
const form = reactive({
    contentsName: '',
    pid: 0,
    id: '',
});
const maps = new Map();
const dataRules = ref({
    contentsName: [{ required: true, message: '请输入新建目录名称', trigger: 'blur' }],
    pid: [{ required: true, message: '请选择父级目录', trigger: 'change' }],
});
const loading = ref(false);
const directoryList = ref<any[]>([]);
const getDirectoryList = async () => {
    const res = await informationDirectoryApi({});
    directoryList.value = res.data.map((item: any) => ({
        label: item.contentsName,
        value: item.id,
    }));
};
onMounted(() => {
    getDirectoryList();
});
const load = async (row: any, treeNode: any, resolve: any) => {
    const res = await childInformationApi(row.id);
    if (res.data.length === 0) {
        tableRef.value.store.states.lazyTreeNodeMap.value[row.id] = [];
    } else {
        resolve(res.data);
    }
    maps.set(row.id, { row, treeNode, resolve });
};
const updateMap = async (pid: string) => {
    if (maps.get(pid)) {
        const { row, treeNode, resolve } = maps.get(pid);
        if (row) {
            await load(row, treeNode, resolve);
        }
    }
};
const onSubmit = async () => {
    const valid = await dataFormRef.value.validate().catch(() => {});
    if (!valid) return false;
    try {
        loading.value = true;
        const api = isEdit.value ? editInformationApi : saveInformationApi;
        await api({
            contentsName: form.contentsName,
            pid: form.pid || '',
            id: form.id || '',
        });
        useMessage().success(isEdit.value ? '修改成功' : '新增成功');
        getDataList(false);
        getDirectoryList();
        updateMap(form.pid + '');
        onCancel();
    } catch (err: any) {
        useMessage().error(err.msg);
    } finally {
        loading.value = false;
    }
};
const handleEdit = (row: any) => {
    currentRow.value = row;
    isEdit.value = true;
    form.contentsName = row.contentsName;
    form.pid = row.pid || 0;
    form.id = row.id;
    catalogueVisible.value = true;
};
const handleView = (row: any) => {
    router.push({
        path: '/eduConnect/courseZone/informationList/index?id=' + row.id,
        query: {
            id: row.id,
            from: encodeURIComponent(router.currentRoute.value.fullPath),
        },
    });
};
const handleDel = (row: any) => {
    ElMessageBox.confirm('确定删除该资料目录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    }).then(async () => {
        try {
            await delInformationApi(row.id);
            updateMap(row.pid + '');
            useMessage().success('删除成功');
            getDataList(false);
            getDirectoryList();
        } catch (err: any) {
            useMessage().error(err.msg);
        }
    });
};
const onCancel = () => {
    catalogueVisible.value = false;
    isEdit.value = false;
    form.contentsName = '';
    form.pid = 0;
    form.id = '';
};
</script>

<style scoped></style>
