<template>
    <el-dialog title="授权门店" v-model="visible" :close-on-click-modal="false" width="1100" @close="handleCloseDialog">
        <el-form
            ref="dataFormRef"
            :model="form"
            :rules="dataRules"
            formDialogRef
            label-width="120px"
            v-loading="loading"
        >
            <el-row :gutter="24">
                <el-col :span="24" class="mb20">
                    <el-form-item label="目录名称">{{ contentsName || '--' }} </el-form-item>
                </el-col>
                <el-col :span="24" class="mb20">
                    <el-form-item label="授权门店" prop="ssClassAuthInfoDTOList">
                        <YdTransfer
                            ref="transferRef"
                            v-model="selectedDevices"
                            :dataSource="transferOptions"
                            rowKey="key"
                            :titles="['未选中门店', '选中门店']"
                            filterable
                            :filterPlaceholder="['请输入要搜索的门店', '请输入要搜索的门店']"
                            @change="handleDeviceChange"
                        >
                            <template #default="{ item }">
                                <el-tooltip :content="item.label" placement="top" :show-after="500">
                                    <span>{{ item.label }}</span>
                                </el-tooltip>
                            </template>
                        </YdTransfer>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="visible = false">取消</el-button>
                <el-button type="primary" :disabled="loading" @click="onSubmit">确认</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script setup lang="ts" name="CourseDialog">
import { useMessage } from '/@/hooks/message';
import YdTransfer from '/@/components/YdTransfer/index.vue'; // 路径根据您的项目结构调整
import { authInformationApi, saveAuthInformationApi } from '/@/api/eduConnect/information';
interface Option {
    key: number;
    label: string;
}
// 定义接口类型
interface ClassRoomDevice {
    id: number;
    storeName: string;
    storeId: string;
    isAuthorized: boolean;
}
const selectedDevices = ref<Option[]>([]);
const transferRef = ref();
const emit = defineEmits(['refresh']);
const dataFormRef = ref();
const visible = ref(false);
const loading = ref(false);
const classRoomDevice = ref<ClassRoomDevice[]>([]);
const authStoreList = ref<ClassRoomDevice[]>([]);
const form = reactive({
    id: '',
    storeIds: [] as any,
});
const transferOptions = ref<Option[]>([]);
const isEdit = ref(false);
const contentsName = ref('');
const handleDeviceChange = (value: Option[]) => {
    form.storeIds = value.map((item: Option) => item.key);
};
const validatorCustomizeFee = (rule: any, value: any, callback: any) => {
    if (value === '') {
        callback(new Error('请输入有效数字'));
    } else if (value < 0) {
        callback(new Error('请输入有效值'));
    } else {
        callback();
    }
};

const dataRules = ref({
    courseName: [{ required: true, message: '课程产品名称不能为空', trigger: 'blur' }],
    stageId: [{ required: true, message: '适学阶段不能为空', trigger: 'change' }],
    courseTypeId: [{ required: true, message: '课程类型不能为空', trigger: 'change' }],
    customizeFee: [{ validator: validatorCustomizeFee, required: true, trigger: 'change' }],
    chargeMethod: [{ required: true, message: '收费方式不能为空', trigger: 'change' }],
});

const generateTransferOptions = () => {
    transferOptions.value = [
        ...classRoomDevice.value.map((item: ClassRoomDevice) => ({
            label: item.storeName,
            key: Number(item.storeId),
        })),
        ...authStoreList.value.map((item: any) => ({
            label: item.storeName,
            key: Number(item.storeId),
        })),
    ];
    selectedDevices.value = authStoreList.value.map((item: any) => ({
        label: item.storeName,
        key: Number(item.storeId),
    }));
    if (selectedDevices.value.length > 0) {
        form.storeIds = selectedDevices.value.map((item: Option) => item.key);
    }
};
// 打开弹窗
const openDialog = async (id: string, name?: string) => {
    form.id = id;
    visible.value = true;
    loading.value = true;
    isEdit.value = !!id;
    contentsName.value = name || '--';
    await nextTick(() => {
        dataFormRef.value?.resetFields();
    });
    const res = await authInformationApi(id);
    classRoomDevice.value = res.data.unAuthStoreList || [];
    authStoreList.value = res.data.authStoreList || [];
    generateTransferOptions();
    loading.value = false;
};
// 提交
const onSubmit = async () => {
    const valid = await dataFormRef.value.validate().catch(() => {});
    if (!valid) return false;

    try {
        // if (form.storeIds.length === 0) {
        //     useMessage().warning('请选择授权门店');
        //     return false;
        // }
        loading.value = true;
        await saveAuthInformationApi(form);
        useMessage().success('保存成功');
        visible.value = false;
        emit('refresh');
    } catch (err: any) {
        useMessage().error(err.msg);
    } finally {
        loading.value = false;
    }
};
const handleCloseDialog = () => {
    if (transferRef.value) {
        transferRef.value.clearSearch();
    }
    transferOptions.value = [];
    selectedDevices.value = [];
    form.storeIds = [];
};

// 暴露变量
defineExpose({
    openDialog,
});
</script>
