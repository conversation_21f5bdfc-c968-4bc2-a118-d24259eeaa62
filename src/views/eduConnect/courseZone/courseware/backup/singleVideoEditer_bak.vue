<template>
    <div class="h-full flex flex-col items-center justify-between">
        <el-scrollbar>
            <el-form ref="dataFormRef" :model="form" :rules="dataRules" formDialogRef label-width="60px">
                <el-row :gutter="24">
                    <el-col :span="24" class="mb20">
                        <el-form-item label="视频" prop="videoUrl">
                            <div v-if="form.videoUrl" class="w-full flex items-center gap-[3px] text-gray-600">
                                <el-icon><Document /></el-icon>
                                <p class="line-clamp-1 overflow-hidden">{{ form.videoUrl }}</p>
                                <el-icon class="cursor-pointer text-red-400" @click="form.videoUrl = ''"
                                    ><CircleClose
                                /></el-icon>
                            </div>
                            <file-oss
                                v-else
                                moduleName="courseware"
                                service-name="teaching"
                                :file-type="['mp4']"
                                :file-size="2000"
                                @success="form.videoUrl = $event.fullPath"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </el-scrollbar>
        <el-button class="flex-none mt12" icon="FolderAdd" type="primary" @click="handleSave"> 保存 </el-button>
    </div>
</template>

<script setup lang="ts">
import FileOss from '/@/components/Upload/FileOss.vue';

const props = defineProps({
    menuData: { type: Object, default: () => ({}) },
    data: { type: Object, default: () => ({}) },
});
watch(
    () => props.data,
    (newValue) => {
        form.videoUrl = newValue.animationInfo;
    },
    { deep: true }
);
const emit = defineEmits(['save']);

const dataFormRef = ref();
const form = reactive({
    videoUrl: props.data.animationInfo,
});
const dataRules = ref({
    videoUrl: [
        {
            required: true,
            message: '请上传视频',
            trigger: 'change',
        },
    ],
});

const handleSave = async () => {
    const valid = await dataFormRef.value.validate().catch(() => {});
    if (!valid) return;
    emit('save', {
        id: props.data.id,
        stepId: props.data.stepId, // 教学页id
        animationInfo: form.videoUrl, // 视频路径
        type: 0, // 0视频 1图片
    });
};
</script>

<style lang="scss" scoped></style>
