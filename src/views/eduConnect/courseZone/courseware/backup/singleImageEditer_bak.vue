<template>
    <div class="h-full flex flex-col items-center justify-between">
        <el-scrollbar>
            <el-form
                ref="dataFormRef"
                class="w-[600px]"
                :model="form"
                :rules="dataRules"
                formDialogRef
                label-width="60px"
            >
                <el-row :gutter="24">
                    <el-col :span="24" class="mb20">
                        <el-form-item label="图片" prop="image">
                            <image-oss
                                v-model:file-path="form.image"
                                service-name="teaching"
                                module-name="courseware"
                                :file-type="['jpg', 'gif']"
                                :file-size="50"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </el-scrollbar>
        <el-button class="flex-none mt12" icon="FolderAdd" type="primary" @click="handleSave"> 保存 </el-button>
    </div>
</template>

<script setup lang="ts">
import ImageOss from '/@/components/Upload/ImageOss.vue';

const props = defineProps({
    menuData: { type: Object, default: () => ({}) },
    data: { type: Object, default: () => ({}) },
});
watch(
    () => props.data,
    (newValue) => {
        form.image.relativePath = newValue.backImgShort;
        form.image.fullPath = newValue.backImgLong;
    },
    { deep: true }
);
const emit = defineEmits(['save']);

const dataFormRef = ref();
const form = reactive({
    image: {
        relativePath: props.data.backImgShort,
        fullPath: props.data.backImgLong,
    },
});
const dataRules = ref({
    image: [
        {
            required: true,
            validator: (rule: any, value: typeof form.image, callback: any) => {
                if (value.relativePath == '') {
                    callback(new Error('图片不能为空'));
                } else {
                    callback();
                }
            },
            trigger: 'change',
        },
    ],
});

const handleSave = async () => {
    const valid = await dataFormRef.value.validate().catch(() => {});
    if (!valid) return;
    emit('save', {
        id: props.data.id,
        stepId: props.data.stepId, // 教学页id
        backImg: form.image.relativePath, // 图片半路径
        type: 1, // 0视频 1图片
    });
};
</script>

<style lang="scss" scoped></style>
