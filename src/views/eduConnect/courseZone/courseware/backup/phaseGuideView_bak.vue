<template>
    <div class="flex flex-col items-center justify-between h-full" v-loading="loading">
        <el-input
            v-model.trim="teachingPlanInfo"
            :rows="5"
            type="textarea"
            placeholder="请输入目标、教学动作、参考话术等内容"
            maxlength="500"
            show-word-limit
        />
        <el-button class="flex-none mt12" icon="FolderAdd" type="primary" @click="handleSave"> 保存教案 </el-button>
    </div>
</template>

<script setup lang="ts">
import { fetchTeachPageDetail } from '/@/api/integraEdu/courseware';
const props = defineProps({
    data: { type: Object, default: () => ({}) },
    coursewareId: { type: String, default: '' },
    coursewareDataId: { type: String, default: '' },
    stepId: { type: String, default: '' },
});
const teachingPlanInfo = ref<string>('');
const loading = ref(false);
const getValue = () => {
    loading.value = true;
    fetchTeachPageDetail(props.coursewareId, props.coursewareDataId, props.stepId)
        .then((res) => {
            teachingPlanInfo.value = res.data.details?.teachingPlan;
        })
        .finally(() => {
            loading.value = false;
        });
};
getValue();

const emit = defineEmits(['save']);

const handleSave = () => {
    const details = JSON.stringify({
        teachingPlan: teachingPlanInfo.value,
    });
    emit('save', {
        id: props.data.id,
        stepId: props.data.stepId,
        details,
    });
};
</script>

<style lang="scss" scoped></style>
