<template>
    <div class="layout-padding">
        <div class="layout-padding-auto layout-padding-view">
            <navi-bar
                class="mb20"
                :title="route.query.title as string"
                :sub-title="route.query.author as string"
                :from="decodeURIComponent(route.query.from as string)"
            />
            <el-row>
                <div class="mb8" style="width: 100%">
                    <el-button
                        icon="folder-add"
                        type="primary"
                        class="ml10"
                        @click="formDialogRef.openDialog(route.query.bookId)"
                    >
                        添加课件
                    </el-button>
                    <right-toolbar
                        class="ml10 mr20"
                        :search="false"
                        style="float: right"
                        :export="false"
                        @queryTable="getDataList"
                    />
                </div>
            </el-row>
            <el-table
                :data="state.dataList"
                v-loading="state.loading"
                border
                :cell-style="tableStyle.cellStyle"
                :header-cell-style="tableStyle.headerCellStyle"
            >
                <el-table-column type="index" label="序号" width="60" />
                <el-table-column prop="coursewareName" label="课件名称" show-overflow-tooltip />
                <el-table-column prop="author" label="发布状态" show-overflow-tooltip width="120">
                    <template #default="scope">
                        <el-tag v-if="scope.row.publishStatus == 1" type="success">已发布</el-tag>
                        <el-tag v-else type="info">未发布</el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="press" label="书籍版本" show-overflow-tooltip />
                <el-table-column prop="createBy" label="创建人" show-overflow-tooltip />
                <el-table-column prop="publishTime" label="发布日期" show-overflow-tooltip>
                    <template #default="scope"> {{ scope.row.publishTime ?? '/' }} </template>
                </el-table-column>
                <el-table-column prop="updateTime" label="最近更新日期" show-overflow-tooltip>
                    <template #default="scope"> {{ scope.row.updateTime ?? '/' }} </template>
                </el-table-column>
                <el-table-column label="操作" width="120" fixed="right">
                    <template #default="scope">
                        <el-button icon="edit-pen" text type="primary" @click="handleEdit(scope.row)"> 编辑 </el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <form-dialog ref="formDialogRef" @refresh="getDataList(false)" />
    </div>
</template>

<script setup lang="ts">
import { BasicTableProps, useTable } from '/@/hooks/table';
import { fetchCoursewareList } from '/@/api/integraEdu/courseware';
import NaviBar from '/@/components/NaviBar/index.vue';
import FormDialog from './components/common/addForm.vue';

const route = useRoute();
const router = useRouter();
const formDialogRef = ref();
const state: BasicTableProps = reactive<BasicTableProps>({
    queryForm: { bookId: route.query.bookId as string },
    pageList: fetchCoursewareList,
    isPage: false,
    pagination: {},
});
const { getDataList, tableStyle } = useTable(state);

const handleEdit = (item: any) => {
    router.push({
        path: '/eduConnect/courseZone/courseware/materiels',
        query: {
            bookId: route.query.bookId,
            coursewareId: item.id,
            from: encodeURIComponent(router.currentRoute.value.fullPath),
        },
    });
};
</script>

<style lang="scss" scoped></style>
