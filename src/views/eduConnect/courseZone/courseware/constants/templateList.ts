interface TemplateItem {
    pageTemplateId: number; //对应接口模板id
    title: string; //产品名称
    component: () => Promise<any>; //对应的编辑组件
    path: string; //对应页面路径
    name: string;
}
export const templateList: TemplateItem[] = [
    {
        pageTemplateId: 0,
        title: '全屏图',
        name: 'fullScreenImage',
        path: '/fullScreenImage',
        component: () => import('../components/templates/contentTemplate/fullScreenImage.vue'),
    },
    {
        pageTemplateId: 1,
        title: '单图',
        name: 'titleAndImage',
        path: '/titleAndImage',
        component: () => import('../components/templates/contentTemplate/titleAndImage.vue'),
    },
    {
        pageTemplateId: 2,
        title: '纯文字',
        name: 'titleAndText',
        path: '/titleAndText',
        component: () => import('../components/templates/contentTemplate/titleAndText.vue'),
    },
    {
        pageTemplateId: 3,
        title: '双图',
        name: 'imageSplitLayout',
        path: '/imageSplitLayout',
        component: () => import('../components/templates/contentTemplate/imageSplitLayout.vue'),
    },
    {
        pageTemplateId: 4,
        title: '三图',
        name: 'imageRowLayout',
        path: '/imageRowLayout',
        component: () => import('../components/templates/contentTemplate/imageRowLayout.vue'),
    },
    {
        pageTemplateId: 5,
        title: '阅读时间',
        name: 'readTime',
        path: '/readTime',
        component: () => import('../components/templates/processTemplate/readTime.vue'),
    },
    {
        pageTemplateId: 6,
        title: '视频',
        name: 'videoPlayer',
        path: '/videoPlayer',
        component: () => import('../components/templates/contentTemplate/videoPlayer.vue'),
    },
    {
        pageTemplateId: 7,
        title: '开放性问题',
        name: 'opinionQuestion',
        path: '/opinionQuestion',
        component: () => import('../components/templates/contentTemplate/opinionQuestion.vue'),
    },
    {
        pageTemplateId: 8,
        title: '轮播图',
        name: 'threeImageSlider',
        path: '/threeImageSlider',
        component: () => import('../components/templates/contentTemplate/threeImageSlider.vue'),
    },
    {
        pageTemplateId: 9,
        title: '图片翻转',
        name: 'flipImageEffect',
        path: '/flipImageEffect',
        component: () => import('../components/templates/contentTemplate/flipImageEffect.vue'),
    },
    {
        pageTemplateId: 10,
        title: '笔记单',
        name: 'studyNoteList',
        path: '/studyNoteList',
        component: () => import('../components/templates/contentTemplate/studyNoteList.vue'),
    },
    {
        pageTemplateId: 11,
        title: '选择题',
        name: 'choiceTopic',
        path: '/choiceTopicOrVote',
        component: () => import('../components/templates/interactiveTemplate/choiceTopicOrVote.vue'),
    },
    {
        pageTemplateId: 12,
        title: '全屏图',
        name: 'fullScreenImage',
        path: '/fullScreenImage',
        component: () => import('../components/templates/contentTemplate/fullScreenImage.vue'),
    },
    {
        pageTemplateId: 13,
        title: '全屏视频',
        name: 'fullScreenVideo',
        path: '/fullScreenVideo',
        component: () => import('../components/templates/contentTemplate/fullScreenVideo.vue'),
    },
    {
        pageTemplateId: 14,
        title: '有声绘本',
        name: 'audioPictureBook',
        path: '/audioPictureBook',
        component: () => import('../components/templates/contentTemplate/audioPictureBook.vue'),
    },
    {
        pageTemplateId: 15,
        title: '投票',
        name: 'vote',
        path: '/choiceTopicOrVote',
        component: () => import('../components/templates/interactiveTemplate/choiceTopicOrVote.vue'),
    },
    {
        pageTemplateId: 1000,
        title: '开场五分钟',
        name: 'emptyTemplate',
        path: '/emptyTemplate',
        component: () => import('../components/templates/processTemplate/emptyTemplate.vue'),
    },
    {
        pageTemplateId: 1001,
        title: '开场一分钟',
        name: 'emptyTemplate',
        path: '/emptyTemplate',
        component: () => import('../components/templates/processTemplate/emptyTemplate.vue'),
    },
    {
        pageTemplateId: 1002,
        title: '课前回顾',
        name: 'emptyTemplate',
        path: '/emptyTemplate',
        component: () => import('../components/templates/processTemplate/emptyTemplate.vue'),
    },
    {
        pageTemplateId: 1003,
        title: '休息',
        name: 'emptyTemplate',
        path: '/emptyTemplate',
        component: () => import('../components/templates/processTemplate/emptyTemplate.vue'),
    },
    {
        pageTemplateId: 1004,
        title: '阅读时间',
        name: 'emptyTemplate',
        path: '/emptyTemplate',
        component: () => import('../components/templates/processTemplate/emptyTemplate.vue'),
    },
    {
        pageTemplateId: 1005,
        title: '开场视频',
        name: 'emptyTemplate',
        path: '/emptyTemplate',
        component: () => import('../components/templates/processTemplate/emptyTemplate.vue'),
    },
    {
        pageTemplateId: 1006,
        title: '表彰',
        name: 'emptyTemplate',
        path: '/emptyTemplate',
        component: () => import('../components/templates/processTemplate/emptyTemplate.vue'),
    },
];
