export interface TemplateConfigType {
    titleMaxLength: number; //标题最长字数
    backgroundUploadType: string[]; //背景图上传类型
    audioPictureBook: {
        // 有声绘本
        audioPictureBookUploadType: string[]; // 有声绘本上传类型
        audioPictureBookUploadSize: number; // 有声绘本上传大小限制
        roleUploadType: string[]; // 角色上传类型
        roleUploadSize: number; // 角色上传大小
        dialogueContentMaxLength: number; // 对话框角色content最大长度
        videoUploadType: string[]; // 音频上传格式
        videoUploadSize: number; // 音频上传最大
    };
    feifei: {
        // 飞飞辅助工具
        feifeiInputMaxLength: number;
        videoUploadType: string[];
        videoUploadSize: number;
    };
    imageUploadType: string[]; //图片上传类型
    imageUploadSize: number; //图片上传大小限制
    imageUploadOssName: string; //图片上传oss文件名
    imageUploadOssModuleName: string; //图片上传oss模块名
    twoPicturesContentMaxLength: number; //两张图片内容最长字数
    threePicturesContentMaxLength: number; //三张图片内容最长字数
    remarkMaxLength: number; //备注最长字数
}
export const config: TemplateConfigType = {
    titleMaxLength: 20,
    backgroundUploadType: ['jpg', 'png', 'jpeg'],
    imageUploadType: ['jpg', 'png', 'jpeg'],
    imageUploadSize: 10,
    imageUploadOssName: 'teaching',
    imageUploadOssModuleName: 'courseware',
    twoPicturesContentMaxLength: 60,
    threePicturesContentMaxLength: 30,
    audioPictureBook: {
        audioPictureBookUploadType: ['jpg', 'png', 'jpeg'],
        audioPictureBookUploadSize: 10,
        roleUploadType: ['jpg', 'png', 'jpeg'],
        roleUploadSize: 10,
        dialogueContentMaxLength: 40,
        videoUploadType: ['mp3'],
        videoUploadSize: 0,
    },
    feifei: {
        feifeiInputMaxLength: 50,
        videoUploadType: ['mp3'],
        videoUploadSize: 0,
    },
    remarkMaxLength: 500,
};
