<template>
    <div class="layout-padding">
        <div class="layout-padding-auto layout-padding-view">
            <el-row>
                <el-form ref="queryRef" :model="queryForm" :inline="true" @keyup.enter="queryData">
                    <el-form-item label="图书名称" prop="title">
                        <el-input placeholder="请输入图书名称" v-model.trim="queryForm.title" />
                    </el-form-item>
                    <el-form-item>
                        <el-button icon="search" type="primary" :disabled="!queryForm.title" @click="queryData">
                            查询
                        </el-button>
                        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-row>
            <template v-if="!searchResultVisible">
                <el-radio-group class="mt12" v-model="curTabId" @change="loadData(false)">
                    <el-radio-button v-for="(item, index) in bookTrackList" :key="index" :label="item.id">{{
                        item.stageName
                    }}</el-radio-button>
                </el-radio-group>
                <el-divider />
                <el-scrollbar v-loading="loading">
                    <template v-if="dataArray.length">
                        <book-cell
                            v-for="(item, index) in dataArray"
                            :key="index"
                            :data="item"
                            @click="handleWareList(item)"
                        />
                    </template>
                    <el-empty v-else class="m-[100px]" description="没有找到数据哦~" />
                </el-scrollbar>
            </template>
            <template v-else>
                <el-divider content-position="left">搜索结果</el-divider>
                <el-scrollbar v-loading="loading">
                    <template v-if="dataArray.length">
                        <book-cell
                            v-for="(item, index) in dataArray"
                            :key="index"
                            :data="item"
                            @click="handleWareList(item)"
                        />
                    </template>
                    <el-empty v-else class="m-[100px]" description="没有找到数据哦~" />
                </el-scrollbar>
            </template>
        </div>
    </div>
</template>

<script setup lang="ts">
import { useMessage } from '/@/hooks/message';
import { useStageList } from '/@/hooks/stage';
import { fetchWareBookNameList } from '/@/api/integraEdu/courseware';
import bookCell from './components/common/bookCell.vue';

const router = useRouter();
const queryRef = ref();
const queryForm = reactive({
    title: '',
});
const curTabId = ref(-1);
const loading = ref(false);
const searchResultVisible = ref(false);
const dataArray = ref<any[]>([]);

// 使用stage hooks
const { stages } = useStageList();
const bookTrackList = computed(() => stages.value);

onMounted(() => {
    // 等待stages数据加载完成
    watch(
        stages,
        (newStages) => {
            if (newStages.length > 0 && curTabId.value === -1) {
                curTabId.value = newStages[0].id;
                loadData(false);
            }
        },
        { immediate: true }
    );
});

const queryData = () => {
    searchResultVisible.value = true;
    loadData(true);
};
const resetQuery = () => {
    queryRef.value?.resetFields();
    searchResultVisible.value = false;
    loadData(false);
};
const loadData = (isQuery: boolean = false) => {
    if (!isQuery) {
        queryRef.value?.resetFields();
    }
    loading.value = true;
    fetchWareBookNameList({ ...queryForm, stageId: isQuery ? '' : curTabId.value })
        .then((res) => {
            dataArray.value = res.data || [];
        })
        .catch((err) => {
            useMessage().error(err.msg);
        })
        .finally(() => {
            loading.value = false;
        });
};

const handleWareList = (item: any) => {
    router.push({
        path: '/eduConnect/courseZone/courseware/list',
        query: {
            bookId: item.id,
            title: item.title,
            author: item.author,
            from: encodeURIComponent(router.currentRoute.value.fullPath),
        },
    });
};
</script>

<style lang="scss" scoped></style>
