<template>
    <div class="layout-padding">
        <div class="flex flex-col items-stretch layout-padding-view">
            <navi-bar
                ref="naviBarRef"
                class="p-3"
                :title="route.query.materielName as string || ''"
                :sub-title="route.query.coursewareName as string || ''"
                :from="decodeURIComponent(route.query.from as string)"
                destroy
            >
                <template #extra>
                    <div class="flex items-center space-x-4">
                        <el-button class="bg-gray-100 w-8 h-8 m-0 border-none" @click="menuShow = !menuShow">
                            <template #icon>
                                <span v-if="menuShow" class="iconfont-ss icon-menu_shrink" />
                                <span v-else class="iconfont-ss icon-menu_expand" />
                            </template>
                        </el-button>
                        <el-button class="bg-gray-100 w-8 h-8 m-0 border-none" @click="handleToggleFullScreen">
                            <template #icon>
                                <span v-if="isTagsViewCurrenFull" class="iconfont-ss icon-full_screen_exit" />
                                <span v-else class="iconfont-ss icon-full_screen" />
                            </template>
                        </el-button>
                        <el-button
                            class="bg-gray-100 h-8 m-0 border-none px-3 flex items-center"
                            @click="toggleFullscreen"
                        >
                            <el-icon class="mr-1"><View /></el-icon>
                            <span>预览</span>
                        </el-button>
                        <div class="w-px h-5 bg-gray-300 mx-3"></div>
                        <el-switch
                            v-model="canPublish"
                            class="publish-switch"
                            inline-prompt
                            active-text="可发布"
                            inactive-text="不可发布"
                        />
                        <el-button
                            icon="promotion"
                            type="primary"
                            :loading="submitLoading"
                            class="ml-4"
                            @click="handleSubmit"
                        >
                            提交
                        </el-button>
                    </div>
                </template>
            </navi-bar>
            <el-divider class="m-0" />
            <div class="flex items-stretch flex-auto" v-loading="pageLoading">
                <div v-show="menuShow" class="flex-none w-60 border-r border-gray-300">
                    <menu-view
                        :courseware-name="coursewareName"
                        :courseware-id="coursewareId"
                        :materiel-id="materielId"
                        :data-template-id="dataTemplateId"
                        @change="canPublish = false"
                        @click-cell="handleClickMenuCell"
                        @step-name-change="handleStepNameChange"
                    />
                </div>
                <div v-if="selectMenuData && pageData" class="flex flex-col items-stretch flex-auto">
                    <div class="flex-1 flex min-h-0">
                        <div class="flex-1 flex flex-col">
                            <div class="w-full p-3">
                                <div class="bg-black aspect-video max-w-full max-h-full" ref="iframeRef" />
                            </div>
                            <div class="p-3 flex-1 overflow-y-auto">
                                <el-form label-position="top">
                                    <el-form-item label="备注" class="mt-5">
                                        <el-input
                                            v-model="pageRemarkContent"
                                            :maxlength="config?.remarkMaxLength"
                                            show-word-limit
                                            placeholder="请输入内容"
                                            :rows="5"
                                            type="textarea"
                                        />
                                    </el-form-item>
                                </el-form>
                            </div>
                        </div>
                        <div class="flex-1 h-full flex flex-col">
                            <data-view
                                ref="dataViewRef"
                                v-show="dataShow"
                                class="flex-1 min-h-0 overflow-hidden"
                                :courseware-id="coursewareId"
                                :materiel-id="materielId"
                                :menuData="selectMenuData"
                                :phase-data="phaseData"
                                :data="pageData"
                                :stepId="stepId"
                                @change="onValueChange"
                            />
                        </div>
                    </div>
                    <div class="flex justify-center w-full border-t border-gray-300 p-2 bg-white">
                        <el-button type="primary" plain class="w-60" :loading="saveLoading" @click="handleSaveAll">
                            保存
                        </el-button>
                    </div>
                </div>
            </div>
        </div>
        <delay-confirm
            ref="delayConfirmRef"
            title="提交成功"
            message="5秒后自动返回上一级"
            cancel-text="留在此页面"
            confirm-text="返回上一级"
            @confirm="naviBarRef?.back()"
        />
        <fullscreen-preview
            ref="fullscreenPreviewRef"
            :coursewareId="coursewareId"
            :materielId="materielId"
            @exitFullscreen="exitFullscreen"
        ></fullscreen-preview>
    </div>
</template>

<script setup lang="ts">
import { useMessage } from '/@/hooks/message';
import NaviBar from '/@/components/NaviBar/index.vue';
import menuView from './components/editor/menuView.vue';
import dataView from './components/editor/dataView.vue';
import delayConfirm from './components/common/delayConfirm.vue';
import { editTeachPageDetail, fetchTeachPageDetail, modifyWareMaterielStatus } from '/@/api/integraEdu/courseware';
import { useTagsViewRoutes } from '/@/stores/tagsViewRoutes';
import { SlidePlayer } from 'courseware-player';
import { debounce } from 'lodash';
import { getPictureBookList } from '/@/api/eduConnect/audioPictureBook';
import fullscreenPreview from './components/editor/fullscreenPreview.vue';
import { config } from './constants/templateConfig';
const tagsViewRoutesStore = useTagsViewRoutes();
const isTagsViewCurrenFull = computed(() => tagsViewRoutesStore.isTagsViewCurrenFull);
const naviBarRef = ref();
const delayConfirmRef = ref();
const fullscreenPreviewRef = ref();

const route = useRoute();
const pageLoading = ref(false);
const saveLoading = ref(false);
const submitLoading = ref(false);
const canPublish = ref((parseInt(route.query.canPublish as string) || 0) == 1);
const selectMenuData = ref<any | undefined>(undefined);
const phaseData = ref<any | undefined>(undefined);
const pageData = ref<any | undefined>(undefined);
const menuShow = ref(true);
const dataShow = ref(true);

const pageRemarkContent = ref<string>('');

const coursewareName = computed(() => route.query.coursewareName as string);
const coursewareId = computed(() => route.query.coursewareId as string);
const materielId = computed(() => route.query.materielId as string);
const dataTemplateId = computed(() => route.query.dataTemplateId as string);
const stepId = ref();

const iframeRef = ref();
const player = ref();

const dataViewRef = ref();
const cacheSelectMenuData = ref();

const roleList: any = reactive({
    role: [],
});
const toggleFullscreen = () => {
    if (player.value) {
        player.value.destroy();
        cacheSelectMenuData.value = selectMenuData.value;
        handleClickMenuCell('reset');
    }
    fullscreenPreviewRef.value.toggleFullscreen();
};
const exitFullscreen = () => {
    selectMenuData.value = cacheSelectMenuData.value;
    handleClickMenuCell();
};
// 获取角色信息
const getRoleInfo = async () => {
    try {
        const res = await getPictureBookList({ bookId: route.query.bookId });
        roleList.role = res.data;
    } catch (error: any) {
        useMessage().error(error.msg);
    }
};
getRoleInfo();
// 预览
const handlePreview = debounce((pageData) => {
    const previewData = {
        details: {
            ...pageData.details,
        },
        tools: {
            ...pageData.tool,
        },
    };
    if (player.value) {
        player.value.updateCurrentPageConfig(previewData);
    }
}, 100);
// 切换全屏
const handleToggleFullScreen = () => {
    tagsViewRoutesStore.setCurrenFullscreen(!isTagsViewCurrenFull.value);
};

// 点击菜单
const handleClickMenuCell = (data?: any) => {
    if (!data) {
        // selectMenuData.value = undefined;
        phaseData.value = undefined;
        pageData.value = undefined;
        if (selectMenuData.value?.id) {
            loadTeachPageDetail();
        }
        return;
    }
    if (data === 'reset') {
        selectMenuData.value = undefined;
        phaseData.value = undefined;
        pageData.value = undefined;
        return;
    }
    const { id, stepParent } = data;
    stepId.value = data.stepParent;
    const { id: agoId, stepParent: agoStepParent } = selectMenuData.value || {};
    if (id == agoId) return;
    selectMenuData.value = data;
    if (stepParent === agoStepParent) {
        // loadTeachPhaseDetail();
        phaseData.value = { ...phaseData.value };
    }
    // else {
    // }
    loadTeachPageDetail();
    updateTeachPhaseDetail();
};

const handleStepNameChange = (data: any) => {
    if (data.id === selectMenuData.value?.id || data.id === selectMenuData.value?.stepParent) {
        loadTeachPageDetail();
    }
};

function updateTeachPhaseDetail() {
    nextTick(() => {
        dataViewRef.value && dataViewRef.value.setActiveIndex(1);
    });
}
// 获取教学页详情
const loadTeachPageDetail = async () => {
    pageLoading.value = true;
    try {
        const res = await fetchTeachPageDetail(
            route.query.coursewareId as string,
            route.query.materielId as string,
            selectMenuData.value?.id
        );
        pageRemarkContent.value = res.data.details.notes.content;
        pageData.value = res.data;
        await nextTick();
        createPlayer();
    } catch (err: any) {
        pageData.value = undefined;
        useMessage().error(err.msg);
    } finally {
        pageLoading.value = false;
    }
};
//创建播放器
const createPlayer = () => {
    try {
        const playlist = [
            {
                id: pageData.value.templateId,
                config: {
                    ...pageData.value.details,
                    stepName: pageData.value.stepName,
                    tools: pageData.value.tool,
                    role: roleList.role,
                },
                viewUrl: pageData.value.viewUrl,
            },
        ];
        if (player.value) {
            player.value.destroy();
            player.value = null;
        }
        player.value = new SlidePlayer({
            container: iframeRef.value,
            playlist,
            options: {
                hideToolbar: true,
            },
        });
    } catch (error) {
        console.error(error);
    }
};

// 内容变动
const onValueChange = (val: any) => {
    canPublish.value = false;
    // 每次数据变化都触发预览
    handlePreview(val);
};

// 提交
const handleSubmit = async () => {
    submitLoading.value = true;
    try {
        await modifyWareMaterielStatus({
            id: route.query.materielId as string,
            coursewareId: route.query.coursewareId as string,
            canPublish: canPublish.value ? 1 : 0,
        });
        delayConfirmRef.value.open(5);
    } catch (err: any) {
        useMessage().error(err.msg);
    } finally {
        submitLoading.value = false;
    }
};

// 保存处理函数
const handleSaveAll = async () => {
    if (!selectMenuData.value || !pageData.value) {
        useMessage().warning('请先选择要编辑的内容');
        return;
    }

    try {
        saveLoading.value = true;
        const dataViewData = await dataViewRef.value?.getAllData();

        if (!dataViewData) {
            const errors = dataViewRef.value?.validationErrors;
            if (errors) {
                const errorTabs = Object.entries(errors)
                    .filter(([, hasError]) => !hasError)
                    .map(([tab]) => {
                        switch (tab) {
                            case 'content':
                                return '内容编辑';
                            case 'assistant':
                                return '飞飞辅助工具';
                            case 'strokeOrder':
                                return '笔顺工具';
                            default:
                                return tab;
                        }
                    });

                if (errorTabs.length > 0) {
                    useMessage().warning(`请检查以下标签页的表单：${errorTabs.join('、')}`);
                }
            }
            return;
        }

        if (dataViewData.details) {
            dataViewData.details.notes = {
                type: 'text',
                content: pageRemarkContent.value,
            };
        }

        const pageData = {
            details: JSON.stringify(dataViewData.details || {}),
            tool: JSON.stringify(dataViewData.tool || {}),
        };

        await editTeachPageDetail({
            ...pageData,
            coursewareId: coursewareId.value,
            coursewareDataId: materielId.value,
            stepId: selectMenuData.value?.id,
        });

        loadTeachPageDetail();
        useMessage().success('保存成功');
    } catch (err: any) {
        useMessage().error(err.msg || '保存失败');
    } finally {
        saveLoading.value = false;
    }
};
</script>

<style lang="scss" scoped>
.publish-switch {
    :deep(.el-switch__core) {
        width: 72px;
    }
}
</style>
