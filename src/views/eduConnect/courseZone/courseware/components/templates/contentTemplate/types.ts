export interface PublicType {
    background: ImageType;
}
export interface TextareaType {
    type: 'textarea';
    content: string;
}
export interface ImageType {
    type: 'image';
    path: string;
    url: string;
}
export interface TextType {
    type: 'text';
    content: string;
}
export interface VideoType {
    type: 'video';
    path: string;
    url: string;
}
export interface dialogueType {
    type: 'audio';
    url: string;
    role: string;
    content: string;
    fileName: string;
}
export interface roleType {
    avatar: {
        fullPath: string;
        relativePath: string;
    };
    bookId: number;
    id: number;
    roleName: string;
    url: string;
}

export type SaveType = 'save' | 'preview';
