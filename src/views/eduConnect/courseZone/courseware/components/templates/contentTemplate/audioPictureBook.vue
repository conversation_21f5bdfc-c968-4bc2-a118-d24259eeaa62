<template>
    <div class="h-full flex flex-col">
        <el-form
            ref="dataFormRef"
            :model="form"
            :rules="rules"
            label-position="top"
            class="flex-1 h-full overflow-hidden"
            v-loading="!formLoading"
        >
            <div class="h-full overflow-y-auto">
                <el-collapse v-model="activeNames">
                    <!-- 基本内容面板 -->
                    <el-collapse-item title="基本内容" name="1">
                        <el-row :gutter="28">
                            <el-col :span="12" class="mb20">
                                <el-form-item label="标题">
                                    <el-input
                                        v-model="form.title.content"
                                        :maxlength="config?.titleMaxLength"
                                        show-word-limit
                                        placeholder="请输入标题"
                                        :autosize="{ minRows: 4 }"
                                        type="textarea"
                                    />
                                </el-form-item>
                            </el-col>
                            <el-col :span="12" class="mb20">
                                <el-form-item label="背景图片" prop="background">
                                    <image-oss
                                        v-model:relative-path="form.background.url"
                                        v-model:full-path="form.background.path"
                                        :service-name="config?.imageUploadOssName"
                                        :module-name="config?.imageUploadOssModuleName"
                                        :file-type="config?.audioPictureBook.audioPictureBookUploadType"
                                        :file-size="config?.audioPictureBook.audioPictureBookUploadSize"
                                    />
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-collapse-item>

                    <!-- 角色面板 -->
                    <el-collapse-item title="角色" name="2">
                        <div class="flex gap-3 pb-6 border-b border-gray-200">
                            <el-button type="primary" size="small" :icon="Plus" plain @click="addRole">
                                添加角色
                            </el-button>
                            <el-button type="primary" size="small" :icon="Checked" plain @click="saveRole">
                                保存角色
                            </el-button>
                        </div>

                        <div
                            v-for="(item, index) in form.role"
                            :key="index"
                            class="every py-4 border-b border-gray-100"
                        >
                            <el-row :gutter="20">
                                <el-col :span="10">
                                    <el-form-item
                                        :label="`角色${index + 1}昵称`"
                                        :prop="`role.${index}.roleName`"
                                        :rules="rules.roleName"
                                    >
                                        <el-input
                                            v-model="item.roleName"
                                            :maxlength="10"
                                            show-word-limit
                                            placeholder="请输入角色昵称"
                                            @change="roleHasChange"
                                        />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="10">
                                    <el-form-item label="头像" :prop="`role.${index}.avatar`" :rules="rules.avatar">
                                        <image-oss
                                            v-model:file-path="item.avatar"
                                            :service-name="config?.imageUploadOssName"
                                            :module-name="config?.imageUploadOssModuleName"
                                            :file-type="config?.audioPictureBook.roleUploadType"
                                            :file-size="config?.audioPictureBook.roleUploadSize"
                                            width="80px"
                                            height="80px"
                                            @change="roleHasChange"
                                        />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="4" class="flex items-center">
                                    <el-button
                                        type="danger"
                                        :icon="Delete"
                                        circle
                                        plain
                                        v-if="!item.id"
                                        @click="deleteRole(index)"
                                    />
                                </el-col>
                            </el-row>
                        </div>
                    </el-collapse-item>

                    <!-- 对话内容面板 -->
                    <el-collapse-item title="对话内容" name="3">
                        <div class="flex gap-3 pb-6 border-b border-gray-200">
                            <el-button type="primary" size="small" :icon="Plus" plain @click="addVideo">
                                添加对话
                            </el-button>
                        </div>
                        <div
                            v-for="(item, index) in form.dialogue"
                            :key="index"
                            class="mb-4 pb-4 border-b border-gray-100"
                        >
                            <div class="font-medium mb-2 py-1">对话{{ index + 1 }}</div>
                            <el-row :gutter="12">
                                <el-col :span="8">
                                    <el-form-item
                                        label="选择角色"
                                        :prop="`dialogue.${index}.role`"
                                        :rules="speakRules.speakRole"
                                    >
                                        <el-select v-model="item.role" placeholder="请选择角色" size="default">
                                            <el-option
                                                v-for="role in (form.role || []).filter((item: any) =>
                                                        item.hasOwnProperty('id')
                                                    )"
                                                :key="role.id"
                                                :label="role.roleName"
                                                :value="role.id"
                                            />
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="8">
                                    <el-form-item
                                        label="对话文字"
                                        :prop="`dialogue.${index}.content`"
                                        :rules="speakRules.speakContent"
                                    >
                                        <el-input
                                            v-model="item.content"
                                            :maxlength="config?.audioPictureBook.dialogueContentMaxLength"
                                            show-word-limit
                                            placeholder="请输入对话内容"
                                            :autosize="{ minRows: 2 }"
                                            type="textarea"
                                        />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="6">
                                    <el-form-item label="语音">
                                        <div v-if="item.url" class="flex items-center gap-2 text-gray-600">
                                            <el-icon><Document /></el-icon>
                                            <span class="flex-1 truncate">{{ item.fileName || item.url }}</span>
                                            <el-icon
                                                class="text-red-400 cursor-pointer"
                                                @click="handleDeleteAudio(item)"
                                            >
                                                <CircleClose />
                                            </el-icon>
                                        </div>
                                        <div v-else class="flex items-center">
                                            <file-oss
                                                moduleName="courseware"
                                                service-name="teaching"
                                                :file-type="config?.audioPictureBook.videoUploadType"
                                                :file-size="config?.audioPictureBook.videoUploadSize"
                                                @success="uploadSuccess($event, item)"
                                            />
                                        </div>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="2" class="flex items-center">
                                    <el-button
                                        type="danger"
                                        :icon="Delete"
                                        plain
                                        circle
                                        @click="deleteVideo(index)"
                                        v-if="form.dialogue.length > 1"
                                    />
                                </el-col>
                            </el-row>
                        </div>
                    </el-collapse-item>

                    <!-- 使用说明面板 -->
                    <el-collapse-item title="使用说明" name="4">
                        <div class="mt8 ml8">{{ remark || '此模板暂无操作说明，敬请谅解!' }}</div>
                    </el-collapse-item>
                </el-collapse>
            </div>
        </el-form>
    </div>
</template>

<script lang="ts" setup>
import { ref, reactive, watch } from 'vue';
import { PropType } from 'vue';
import ImageOss from '/@/components/Upload/ImageOss.vue';
import { TextType, PublicType, dialogueType, roleType } from './types';
import FileOss from '/@/components/Upload/FileOss.vue';
import { useMessage } from '/@/hooks/message';
import { editAllRole } from '/@/api/eduConnect/audioPictureBook';
import { getPictureBookList } from '/@/api/eduConnect/audioPictureBook';
import { Delete, Document, CircleClose, Plus, Checked } from '@element-plus/icons-vue';

const route = useRoute();
const emit = defineEmits(['change', 'validate']);
const activeNames = ref(['1', '2', '3']);

// refs
const dataFormRef = ref();
const formLoading = ref(false);
const useSaveRole = ref(false);

interface DetailType extends PublicType {
    title: TextType;
    dialogue: dialogueType[];
    notes: TextType;
    role: roleType[];
}

const props = defineProps({
    detail: {
        type: Object as PropType<DetailType>,
        default: () => ({}),
    },
    config: {
        type: Object,
        default: () => ({}),
    },
    remark: {
        type: String,
        default: '',
    },
});

// 表单数据
const form = reactive<any>({
    title: {
        type: 'text',
        content: '',
    },
    background: {
        type: 'image',
        url: '',
        path: '',
    },
    role: [],
    dialogue: [],
    notes: {
        type: 'text',
        content: '',
    },
});

// 初始化数据
onMounted(() => {
    getRoleInfo();
});

// 获取角色信息
const getRoleInfo = async () => {
    try {
        let res = await getPictureBookList({ bookId: route.query.bookId });
        Object.assign(form, {
            ...props.detail,
            role: res.data,
        });
        formLoading.value = true;
    } catch (error: any) {
        useMessage().error(error.msg);
    }
};

// 处理视频删除
const handleDeleteAudio = (item: any) => {
    item.url = '';
    item.fileName = '';
    item.path = '';
};

// 上传成功回调
const uploadSuccess = (event: any, item: any) => {
    item.fileName = event.fileName;
    item.url = event.relativePath;
    item.path = event.fullPath;
};

// 删除角色
const deleteRole = (index: any) => {
    form.role.splice(index, 1);
};

// 表单验证函数
const validateBackImg = (rule: any, value: any, callback: any) => {
    if (form.background.path === '' || form.background.url === '') {
        callback(new Error('请上传背景图片'));
    } else {
        callback();
    }
};

const validateRole = (rule: any, value: any, callback: any) => {
    let count = 0;
    form.role.forEach((element: any) => {
        if (element.roleName == value) {
            count++;
        }
    });
    if (value === '') {
        callback(new Error('角色昵称不能为空'));
    } else if (count > 1) {
        callback(new Error('角色昵称不能重复'));
    } else {
        callback();
    }
};

const validateAvatar = (rule: any, value: any, callback: any) => {
    if (value.fullPath === '' || value.relative === '') {
        callback(new Error('角色头像不能为空'));
    } else {
        callback();
    }
};

const validateSpeakRole = (rule: any, value: any, callback: any) => {
    if (value === '') {
        callback(new Error('角色为必选项'));
    } else {
        callback();
    }
};

const validateSpeakContent = (rule: any, value: any, callback: any) => {
    if (value === '') {
        callback(new Error('对话内容不能为空'));
    } else {
        callback();
    }
};

// 表单验证规则
const rules = reactive({
    background: [{ required: true, validator: validateBackImg, trigger: 'blur' }],
    roleName: [{ required: true, validator: validateRole, trigger: 'blur' }],
    avatar: [{ required: true, validator: validateAvatar, trigger: 'blur' }],
});

const speakRules = {
    speakRole: [{ required: true, validator: validateSpeakRole, trigger: 'change' }],
    speakContent: [{ required: true, validator: validateSpeakContent, trigger: 'blur' }],
};

// 表单验证方法
const validate = () => {
    return new Promise((resolve, reject) => {
        dataFormRef.value?.validate((valid: boolean) => {
            if (valid) {
                resolve(true);
            } else {
                reject(new Error('表单验证失败'));
            }
        });
    });
};

// 创建并发送数据
const createAndEmitData = () => {
    // 创建非响应式数据副本
    const formData = {
        title: {
            type: 'text',
            content: form.title.content,
        },
        background: {
            type: 'image',
            url: form.background.url,
            path: form.background.path,
        },
        role: form.role,
        dialogue: form.dialogue,
        notes: {
            type: 'text',
            content: form.notes?.content || '',
        },
    };
    emit('change', formData);
};

// 监听表单变化
watch(
    form,
    () => {
        createAndEmitData();
    },
    { deep: true }
);

// 角色变更标记
const roleHasChange = () => {
    useSaveRole.value = true;
};

// 添加角色
const addRole = () => {
    if (form.role.length < 6) {
        form.role.push({
            roleName: '',
            avatar: {
                relativePath: '',
                fullPath: '',
            },
        });
    } else {
        useMessage().warning('最多添加六个角色');
    }
};

// 保存角色
const saveRole = async () => {
    // 检查角色名称是否有重复
    const roleNameMap = new Map<string, number>();
    const duplicateIndexes: number[] = [];

    // 先找出所有重复的角色昵称及其索引
    form.role.forEach((role: any, index: number) => {
        if (role.roleName) {
            if (roleNameMap.has(role.roleName)) {
                // 不是第一次出现的重复项，记录索引
                duplicateIndexes.push(index);
            } else {
                // 第一次出现，记录到Map中
                roleNameMap.set(role.roleName, index);
            }
        }
    });

    // 如果有重复，显示错误提示并返回
    if (duplicateIndexes.length > 0) {
        // 清除可能存在的验证消息
        dataFormRef.value?.clearValidate();

        // 使用Element Plus的正确方法显示错误
        // 先验证所有字段，确保表单实例已准备好
        await dataFormRef.value?.validateField(duplicateIndexes.map((index) => `role.${index}.roleName`));

        // 然后显示全局错误消息
        useMessage().warning('存在重复的角色昵称，请修改后再保存');
        return;
    }

    let arr = [];
    for (let index = 0; index < form.role.length; index++) {
        arr.push(`role.${index}.roleName`);
        arr.push(`role.${index}.avatar`);
    }

    let saveRoles = {
        bookRoleList: [] as Object[],
    };

    // 修改validateRole方法来处理重复校验
    const tempValidateRole = (rule: any, value: any, callback: any) => {
        if (value === '') {
            callback(new Error('角色昵称不能为空'));
        } else {
            // 检查当前输入的角色名称在整个表单中是否重复
            let fieldPath = rule.field; // 例如: "role.2.roleName"
            let currentIndex = parseInt(fieldPath.split('.')[1]);

            let count = 0;
            form.role.forEach((element: any, index: number) => {
                if (element.roleName === value) {
                    count++;
                    // 如果是自己，不计入重复
                    if (index === currentIndex) {
                        count--;
                    }
                }
            });

            if (count > 0) {
                callback(new Error('角色昵称不能重复'));
            } else {
                callback();
            }
        }
    };

    // 临时覆盖验证规则
    const originalRules = rules.roleName;
    rules.roleName = [{ required: true, validator: tempValidateRole, trigger: 'blur' }];

    dataFormRef.value.validateField(arr, async (valid: any) => {
        // 恢复原始验证规则
        rules.roleName = originalRules;

        if (valid) {
            form.role.forEach((item: any) => {
                saveRoles.bookRoleList.push({
                    id: item.id,
                    bookId: route.query.bookId,
                    roleName: item.roleName,
                    url: item.avatar.relativePath,
                });
            });

            try {
                await editAllRole(saveRoles);
                getRoleInfo();
                useMessage().success('保存角色信息成功');
            } catch (error: any) {
                useMessage().error('保存角色信息失败');
            }
        }
    });
};

// 添加对话
const addVideo = () => {
    // 如果没有对话，初始化一个
    if (!form.dialogue || form.dialogue.length == 0) {
        form.dialogue = [
            {
                url: '',
                role: '',
                type: 'audio',
                content: '',
                fileName: '',
            },
        ];
        return;
    }

    // 最多添加6个对话
    if (form.dialogue.length >= 6) {
        useMessage().warning('最多添加六个对话');
        return;
    }

    // 添加新对话，不需要验证现有对话
    form.dialogue.push({
        url: '',
        role: '',
        type: 'audio',
        content: '',
        fileName: '',
    });
};

// 删除对话
const deleteVideo = (index: any) => {
    // 如果只剩1个对话，不允许删除
    if (form.dialogue.length <= 1) {
        useMessage().warning('至少需要保留1个对话');
        return;
    }
    form.dialogue.splice(index, 1);
};

// 暴露方法
defineExpose({
    validate,
});
</script>

<style lang="scss" scoped>
:deep(.el-form-item) {
    margin-bottom: 20px;
}

:deep(.el-form-item__label) {
    font-weight: normal;
}

:deep(.el-collapse-item__header) {
    background-color: #f9f9f9;
    padding-left: 24px;
    box-sizing: border-box;
    font-size: 14px;
    font-weight: normal;
}

:deep(.el-collapse-item__content) {
    padding: 24px 12px 24px 24px;
}
</style>
