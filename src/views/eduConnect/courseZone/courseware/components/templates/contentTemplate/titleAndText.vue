<template>
    <el-form ref="dataFormRef" :model="form" :rules="rules" label-position="top">
        <el-collapse v-model="activeNames">
            <el-collapse-item title="基本内容" name="1">
                <el-row :gutter="12">
                    <el-col :span="12">
                        <el-form-item label="标题">
                            <el-input
                                v-model="form.title.content"
                                :maxlength="config?.titleMaxLength"
                                show-word-limit
                                placeholder="请输入标题"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="背景图片" prop="background.url">
                            <image-oss
                                v-model:relative-path="form.background.url"
                                v-model:full-path="form.background.path"
                                :service-name="config?.imageUploadOssName"
                                :module-name="config?.imageUploadOssModuleName"
                                :file-type="config?.backgroundUploadType"
                                :file-size="config?.imageUploadSize"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-collapse-item>
            <el-collapse-item title="内容文本" name="2">
                <el-row :gutter="12">
                    <el-col :span="24">
                        <el-form-item label="文本内容" prop="paragraph.content">
                            <editor
                                v-model:get-html="form.paragraph.content"
                                :mode="editorMode"
                                :height="'400'"
                                :placeholder="'请输入文本内容...'"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-collapse-item>
            <el-collapse-item title="使用说明" name="3">
                <div class="mt8 ml8">{{ remark || '此模板暂无操作说明，敬请谅解!' }}</div>
            </el-collapse-item>
        </el-collapse>
    </el-form>
</template>

<script lang="ts" setup>
import { ref, reactive, watch } from 'vue';
import { PropType } from 'vue';
import ImageOss from '/@/components/Upload/ImageOss.vue';

import { TextareaType, TextType, PublicType } from './types';

const emit = defineEmits(['change']);
const activeNames = ref(['1', '2']);
const editorMode = ref('simple'); // 'simple'模式会自动显示简化版工具栏

interface DetailType extends PublicType {
    paragraph: TextareaType;
    title: TextType;
    notes: TextType;
}

const props = defineProps({
    detail: {
        type: Object as PropType<DetailType>,
        default: () => ({}),
    },
    config: {
        type: Object,
        default: () => ({}),
    },
    remark: {
        type: String,
        default: '',
    },
});

// 提交表单数据
const form = reactive({
    ...props.detail,
});
const dataFormRef = ref<any>(null);

const validateBackgroundUrl = (rule: any, value: any, callback: any) => {
    if (value === '') {
        callback(new Error('请上传背景图片'));
    } else {
        callback();
    }
};

const validateParagraphContent = (rule: any, value: any, callback: any) => {
    if (value === '') {
        callback(new Error('请输入文本内容'));
    } else {
        callback();
    }
};

const rules = reactive({
    'background.url': [{ required: true, validator: validateBackgroundUrl, trigger: 'blur' }],
    'paragraph.content': [{ required: true, validator: validateParagraphContent, trigger: 'blur' }],
});

watch(
    form,
    () => {
        // 手动创建非响应式数据的副本
        const nonReactiveData = {
            background: {
                type: 'image',
                url: form.background.url,
                path: form.background.path,
            },
            title: {
                type: 'text',
                content: form.title.content,
            },
            paragraph: {
                type: 'textarea',
                content: form.paragraph.content,
            },
            notes: {
                type: 'text',
                content: form.notes?.content || '',
            },
        };

        emit('change', nonReactiveData);
    },
    { deep: true }
);

// 添加验证方法
const validate = () => {
    return new Promise((resolve, reject) => {
        dataFormRef.value?.validate((valid: boolean) => {
            if (valid) {
                resolve(true);
            } else {
                reject(new Error('表单验证失败'));
            }
        });
    });
};

defineExpose({
    validate,
});
</script>
