<template>
    <div class="h-full flex flex-col">
        <el-form
            ref="dataFormRef"
            :model="form"
            :rules="rules"
            label-position="top"
            class="flex-1 h-full overflow-hidden"
        >
            <div class="h-full overflow-y-auto">
                <el-collapse v-model="activeNames">
                    <el-collapse-item title="基本内容" name="1">
                        <el-row :gutter="28">
                            <el-col class="mb20" :span="12">
                                <el-form-item label="标题">
                                    <el-input
                                        v-model="form.title.content"
                                        :maxlength="config?.titleMaxLength"
                                        show-word-limit
                                        placeholder="请输入标题"
                                    />
                                </el-form-item>
                            </el-col>
                            <el-col class="mb20" :span="12">
                                <el-form-item label="背景图片" prop="background.url">
                                    <image-oss
                                        v-model:relative-path="form.background.url"
                                        v-model:full-path="form.background.path"
                                        :service-name="config?.imageUploadOssName"
                                        :module-name="config?.imageUploadOssModuleName"
                                        :file-type="config?.backgroundUploadType"
                                        :file-size="config?.imageUploadSize"
                                    />
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-collapse-item>
                    <el-collapse-item title="视频地址" name="2">
                        <el-row :gutter="28">
                            <el-col class="mb20" :span="24">
                                <el-form-item label="视频地址" prop="video.path">
                                    <el-input
                                        v-model.trim="form.video.path"
                                        type="textarea"
                                        :autosize="{ minRows: 4 }"
                                        placeholder="请输入视频地址"
                                    />
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-collapse-item>

                    <el-collapse-item title="使用说明" name="3">
                        <div class="mt8 ml8">{{ remark || '此模板暂无操作说明，敬请谅解!' }}</div>
                    </el-collapse-item>
                </el-collapse>
            </div>
        </el-form>
    </div>
</template>

<script lang="ts" setup>
import { ref, reactive, watch } from 'vue';
import { PropType } from 'vue';
import { VideoType, TextType, PublicType } from './types';
import ImageOss from '/@/components/Upload/ImageOss.vue';
import { isValidVideoUrl } from '/@/utils/validate';

const emit = defineEmits(['change']);
const activeNames = ref(['1', '2']);

interface DetailType extends PublicType {
    video: VideoType;
    title: TextType;
    notes: TextType;
}

const props = defineProps({
    detail: {
        type: Object as PropType<DetailType>,
        default: () => ({}),
    },
    config: {
        type: Object,
        default: () => ({}),
    },
    remark: {
        type: String,
        default: '',
    },
});

const form = reactive({
    ...props.detail,
});

const validateBackgroundUrl = (rule: any, value: any, callback: any) => {
    if (!value) {
        callback(new Error('请上传背景图片'));
    } else {
        callback();
    }
};

const validateVideoPath = (rule: any, value: any, callback: any) => {
    if (!value) {
        callback(new Error('请上传视频'));
    } else if (!isValidVideoUrl(value)) {
        callback(new Error('视频地址不正确'));
    } else {
        callback();
    }
};

const rules = reactive({
    'background.url': [{ required: true, validator: validateBackgroundUrl, trigger: 'blur' }],
    'video.path': [{ required: true, validator: validateVideoPath, trigger: 'blur' }],
});

const dataFormRef = ref<any>(null);

watch(
    form,
    () => {
        // 手动创建非响应式数据的副本
        const nonReactiveData = {
            title: {
                type: 'text',
                content: form.title.content,
            },
            background: {
                type: 'image',
                url: form.background.url,
                path: form.background.path,
            },
            video: {
                type: 'video',
                path: form.video.path,
                url: form.video.path.includes('http') ? form.video.path : '',
            },
            notes: {
                type: 'text',
                content: form.notes?.content || '',
            },
        };

        emit('change', nonReactiveData);
    },
    { deep: true }
);

// 改进验证方法
const validate = () => {
    return new Promise((resolve, reject) => {
        dataFormRef.value?.validate((valid: boolean) => {
            if (valid) {
                if (!isValidVideoUrl(form.video.path)) {
                    reject(new Error('视频地址不正确'));
                    return;
                }
                resolve(true);
            } else {
                reject(new Error('表单验证失败'));
            }
        });
    });
};

defineExpose({
    validate,
});
</script>

<style lang="scss" scoped>
:deep(.el-form-item) {
    margin-bottom: 20px;
}

:deep(.el-form-item__label) {
    font-weight: normal;
}
</style>
