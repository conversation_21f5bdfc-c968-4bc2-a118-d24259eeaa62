<template>
    <el-form ref="dataFormRef" :model="form" :rules="rules" label-position="top">
        <el-collapse v-model="activeNames">
            <el-collapse-item title="基本内容" name="1">
                <el-row :gutter="12">
                    <el-col :span="12">
                        <el-form-item label="标题">
                            <el-input
                                v-model="form.title.content"
                                :maxlength="config?.titleMaxLength"
                                show-word-limit
                                placeholder="请输入标题"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="背景图片" prop="background.url">
                            <image-oss
                                v-model:relative-path="form.background.url"
                                v-model:full-path="form.background.path"
                                :service-name="config?.imageUploadOssName"
                                :module-name="config?.imageUploadOssModuleName"
                                :file-type="config?.backgroundUploadType"
                                :file-size="config?.imageUploadSize"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-collapse-item>
            <el-collapse-item title="内容图片" name="2">
                <el-row :gutter="12" class="mb-6">
                    <el-col :span="8">
                        <el-form-item label="内容图片1" prop="leftImage.url">
                            <image-oss
                                v-model:relative-path="form.leftImage.url"
                                v-model:full-path="form.leftImage.path"
                                :service-name="config?.imageUploadOssName"
                                :module-name="config?.imageUploadOssModuleName"
                                :file-type="config?.imageUploadType"
                                :file-size="config?.imageUploadSize"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="16">
                        <el-form-item label="内容讲解">
                            <el-input
                                v-model="form.leftExplain.content"
                                :maxlength="config?.twoPicturesContentMaxLength"
                                show-word-limit
                                placeholder="请输入内容"
                                :autosize="{ minRows: 3 }"
                                type="textarea"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="12">
                    <el-col :span="8">
                        <el-form-item label="内容图片2" prop="rightImage.url">
                            <image-oss
                                v-model:relative-path="form.rightImage.url"
                                v-model:full-path="form.rightImage.path"
                                :service-name="config?.imageUploadOssName"
                                :module-name="config?.imageUploadOssModuleName"
                                :file-type="config?.imageUploadType"
                                :file-size="config?.imageUploadSize"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="16">
                        <el-form-item label="内容讲解">
                            <el-input
                                v-model="form.rightExplain.content"
                                :maxlength="config?.twoPicturesContentMaxLength"
                                show-word-limit
                                placeholder="请输入内容"
                                :autosize="{ minRows: 3 }"
                                type="textarea"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-collapse-item>
            <el-collapse-item title="使用说明" name="3">
                <div class="mt8 ml8">{{ remark || '此模板暂无操作说明，敬请谅解!' }}</div>
            </el-collapse-item>
        </el-collapse>
    </el-form>
</template>

<script lang="ts" setup>
import { PropType } from 'vue';
import ImageOss from '/@/components/Upload/ImageOss.vue';
import { ImageType, TextType, PublicType } from './types';

const emit = defineEmits(['change']);
const activeNames = ref(['1', '2']);

interface DetailType extends PublicType {
    title: TextType;
    leftImage: ImageType;
    rightImage: ImageType;
    leftExplain: TextType;
    rightExplain: TextType;
    notes: TextType;
}

const props = defineProps({
    detail: {
        type: Object as PropType<DetailType>,
        default: () => ({}),
    },
    config: {
        type: Object,
        default: () => ({}),
    },
    remark: {
        type: String,
        default: '',
    },
});

// 提交表单数据
const form = reactive({
    ...props.detail,
});
const dataFormRef = ref<any>(null);
const validateBackgroundUrl = (rule: any, value: any, callback: any) => {
    if (value === '') {
        callback(new Error('请上传背景图片'));
    } else {
        callback();
    }
};
const validateLeftImageUrl = (rule: any, value: any, callback: any) => {
    if (value === '') {
        callback(new Error('请上传图片'));
    } else {
        callback();
    }
};
const validateRightImageUrl = (rule: any, value: any, callback: any) => {
    if (value === '') {
        callback(new Error('请上传图片'));
    } else {
        callback();
    }
};

const rules = reactive({
    'background.url': [{ required: true, validator: validateBackgroundUrl, trigger: 'blur' }],
    'leftImage.url': [{ required: true, validator: validateLeftImageUrl, trigger: 'blur' }],
    'rightImage.url': [{ required: true, validator: validateRightImageUrl, trigger: 'blur' }],
});

watch(
    form,
    () => {
        // 手动创建非响应式数据的副本
        const nonReactiveData = {
            background: {
                type: 'image',
                url: form.background.url,
                path: form.background.path,
            },
            title: {
                type: 'text',
                content: form.title.content,
            },
            leftImage: {
                type: 'image',
                url: form.leftImage.url,
                path: form.leftImage.path,
            },
            rightImage: {
                type: 'image',
                url: form.rightImage.url,
                path: form.rightImage.path,
            },
            leftExplain: {
                type: 'text',
                content: form.leftExplain.content,
            },
            rightExplain: {
                type: 'text',
                content: form.rightExplain.content,
            },
            notes: {
                type: 'text',
                content: form.notes?.content || '',
            },
        };

        emit('change', nonReactiveData);
    },
    { deep: true }
);

// 添加验证方法
const validate = () => {
    return new Promise((resolve, reject) => {
        dataFormRef.value?.validate((valid: boolean) => {
            if (valid) {
                resolve(true);
            } else {
                reject(new Error('表单验证失败'));
            }
        });
    });
};

defineExpose({
    validate,
});
</script>
