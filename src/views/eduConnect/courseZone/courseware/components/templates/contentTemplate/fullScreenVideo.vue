<template>
    <el-form ref="dataFormRef" :model="form" :rules="rules" label-position="top">
        <el-collapse v-model="activeNames">
            <el-collapse-item title="基本内容" name="1">
                <el-row :gutter="12">
                    <el-col :span="12">
                        <el-form-item label="标题">
                            <el-input
                                v-model="form.title.content"
                                :maxlength="config?.titleMaxLength"
                                show-word-limit
                                placeholder="请输入标题"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-collapse-item>
            <el-collapse-item title="视频地址" name="2">
                <el-form-item label="视频地址" prop="video.path">
                    <el-input
                        type="textarea"
                        :autosize="{ minRows: 4 }"
                        v-model.trim="form.video.path"
                        placeholder="请输入视频地址"
                    >
                    </el-input>
                </el-form-item>
            </el-collapse-item>
            <el-collapse-item title="使用说明" name="3">
                <div class="mt8 ml8">{{ remark || '此模板暂无操作说明，敬请谅解!' }}</div>
            </el-collapse-item>
        </el-collapse>
    </el-form>
</template>

<script lang="ts" setup>
import { PropType } from 'vue';
import { VideoType, TextType, PublicType } from './types';
import { isValidVideoUrl } from '/@/utils/validate';

const emit = defineEmits(['change']);
const activeNames = ref(['1', '2']);

interface DetailType extends PublicType {
    video: VideoType;
    title: TextType;
    notes: TextType;
}

const props = defineProps({
    detail: {
        type: Object as PropType<DetailType>,
        default: () => {},
    },
    config: {
        type: Object,
        default: () => {},
    },
    remark: {
        type: String,
        default: '',
    },
});

// 提交表单数据
const form = reactive({
    ...props.detail,
});

const dataFormRef = ref<any>(null);

const validateVideoPath = (rule: any, value: any, callback: any) => {
    if (!value) {
        callback(new Error('请上传视频'));
    } else if (!isValidVideoUrl(value)) {
        callback(new Error('视频地址不正确'));
    } else {
        callback();
    }
};

const rules = reactive({
    'video.path': [{ required: true, validator: validateVideoPath, trigger: 'blur' }],
});

watch(
    form,
    () => {
        // 手动创建非响应式数据的副本
        const nonReactiveData = {
            title: {
                type: 'text',
                content: form.title.content,
            },
            video: {
                type: 'video',
                path: form.video.path,
                url: form.video.path.includes('http') ? form.video.path : '',
            },
            notes: {
                type: 'text',
                content: form.notes?.content || '',
            },
        };

        emit('change', nonReactiveData);
    },
    { deep: true }
);

// 添加验证方法
const validate = () => {
    return new Promise((resolve, reject) => {
        dataFormRef.value?.validate((valid: boolean) => {
            if (valid) {
                resolve(true);
            } else {
                reject(new Error('表单验证失败'));
            }
        });
    });
};

defineExpose({
    validate,
});
</script>
