<template>
    <el-form :model="form" ref="dataFormRef" label-position="top">
        <el-collapse v-model="activeNames">
            <el-collapse-item title="基本内容" name="1">
                <el-row :gutter="12" class="ml-[12px]">
                    <el-col :span="12">
                        <el-form-item label="标题">
                            <el-input
                                v-model="form.title.content"
                                :maxlength="config?.titleMaxLength"
                                show-word-limit
                                placeholder="请输入标题"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item
                            label="背景图片"
                            prop="background.url"
                            :rules="[
                                {
                                    required: true,
                                    message: '请上传背景图片',
                                    trigger: 'blur',
                                },
                            ]"
                        >
                            <image-oss
                                v-model:relative-path="form.background.url"
                                v-model:full-path="form.background.path"
                                :service-name="config?.imageUploadOssName"
                                :module-name="config?.imageUploadOssModuleName"
                                :file-type="config?.imageUploadType"
                                :file-size="config?.imageUploadSize"
                            />
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-collapse-item>
            <el-collapse-item title="使用说明" name="2">
                <div class="mt8 ml8">{{ remark || '此模板暂无操作说明，敬请谅解!' }}</div>
            </el-collapse-item>
        </el-collapse>
    </el-form>
</template>

<script lang="ts" setup>
import { PropType, reactive, ref, watch } from 'vue';
import ImageOss from '/@/components/Upload/ImageOss.vue';

import { PublicType, TextType } from '../contentTemplate/types';

const emit = defineEmits(['change']);
const activeNames = ref(['1']);

interface DetailType extends PublicType {
    title: TextType;
}
const props = defineProps({
    detail: {
        type: Object as PropType<DetailType>,
        default: () => ({}),
    },
    config: {
        type: Object,
        default: () => ({}),
    },
    remark: {
        type: String,
        default: '',
    },
});

const form = reactive({
    ...props.detail,
});

const dataFormRef = ref<any>(null);

// 监听表单变化
watch(
    form,
    () => {
        const nonReactiveData = {
            title: {
                type: 'text',
                content: form.title.content,
            },
            background: {
                type: 'image',
                url: form.background.url,
                path: form.background.path,
            },
        };

        emit('change', nonReactiveData);
    },
    { deep: true }
);

// 表单验证方法
const validate = () => {
    return new Promise((resolve, reject) => {
        dataFormRef.value?.validate((valid: boolean) => {
            if (valid) {
                resolve(true);
            } else {
                reject(new Error('表单验证失败'));
            }
        });
    });
};

// 获取表单数据
const getData = () => {
    return {
        title: {
            type: 'text',
            content: form.title.content,
        },
        background: {
            type: 'image',
            url: form.background.url,
            path: form.background.path,
        },
    };
};

defineExpose({
    validate,
    getData,
});
</script>
