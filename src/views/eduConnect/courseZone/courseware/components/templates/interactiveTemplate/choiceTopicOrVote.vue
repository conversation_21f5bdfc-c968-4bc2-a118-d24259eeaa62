<template>
    <div class="h-full flex flex-col">
        <el-form
            ref="dataFormRef"
            :model="form"
            :rules="rules"
            label-position="top"
            class="flex-1 h-full overflow-hidden"
        >
            <div class="h-full overflow-y-auto">
                <el-collapse v-model="activeNames">
                    <el-collapse-item title="基本内容" name="1">
                        <el-row :gutter="28">
                            <el-col class="mb20" :span="24">
                                <el-form-item label="题目图片" prop="background.url">
                                    <image-oss
                                        v-model:relative-path="form.background.url"
                                        v-model:full-path="form.background.path"
                                        :service-name="config?.imageUploadOssName"
                                        :module-name="config?.imageUploadOssModuleName"
                                        :file-type="config?.backgroundUploadType"
                                        :file-size="config?.imageUploadSize"
                                        width="160px"
                                        height="90px"
                                    />
                                </el-form-item>
                            </el-col>
                            <el-col class="mb20" :span="24">
                                <el-form-item label="题目" prop="question.content">
                                    <div class="w-full flex gap-[12px]">
                                        <el-input
                                            class="flex-auto"
                                            v-model.trim="form.question.content"
                                            placeholder="请输入题目"
                                            :maxlength="100"
                                            show-word-limit
                                            type="textarea"
                                            :rows="2"
                                        />
                                        <el-button
                                            class="flex-none"
                                            type="primary"
                                            plain
                                            icon="plus"
                                            :disabled="form.options.length >= 6"
                                            @click="addOption"
                                        >
                                            添加选项
                                        </el-button>
                                    </div>
                                </el-form-item>
                            </el-col>
                            <el-col class="mb20" :span="24">
                                <el-form-item class="w-full" label="选项" prop="options">
                                    <el-col
                                        class="mb20"
                                        v-for="(item, index) in form.options"
                                        :key="index"
                                        :lg="12"
                                        :sm="24"
                                    >
                                        <el-form-item
                                            :label="String.fromCharCode(65 + index)"
                                            label-width="20"
                                            :prop="`options.${index}.content`"
                                            :rules="[
                                                { required: true, validator: validateOptionContent, trigger: 'blur' },
                                            ]"
                                        >
                                            <div class="w-full flex gap-[6px] items-center">
                                                <el-input
                                                    class="flex-auto"
                                                    v-model.trim="item.content"
                                                    placeholder="请输入选项"
                                                    :maxlength="200"
                                                >
                                                    <template v-if="form.questionType == 'choiceTopic'" #append>
                                                        <el-checkbox
                                                            v-model="item.correct"
                                                            label="正确"
                                                            size="small"
                                                            @change="onOptionCheckChange(item)"
                                                        />
                                                    </template>
                                                </el-input>
                                                <el-button
                                                    class="flex-none mr-0"
                                                    size="small"
                                                    type="danger"
                                                    icon="Delete"
                                                    circle
                                                    @click="removeOption(index)"
                                                />
                                            </div>
                                        </el-form-item>
                                    </el-col>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-collapse-item>
                    <el-collapse-item title="使用说明" name="2">
                        <div class="mt8 ml8">{{ remark || '此模板暂无操作说明，敬请谅解!' }}</div>
                    </el-collapse-item>
                </el-collapse>
            </div>
        </el-form>
    </div>
</template>

<script lang="ts" setup>
import { ref, reactive, watch, nextTick } from 'vue';
import { PropType } from 'vue';
import ImageOss from '/@/components/Upload/ImageOss.vue';
import { TextType, ImageType, PublicType } from '../contentTemplate/types';

const emit = defineEmits(['change', 'validate']);
const activeNames = ref(['1']);

interface DetailType extends PublicType {
    background: ImageType;
    question: TextType;
    options: any[];
    questionType: 'choiceTopic' | 'vote';
}

const props = defineProps({
    detail: {
        type: Object as PropType<DetailType>,
        default: () => ({}),
    },
    config: {
        type: Object,
        default: () => ({}),
    },
    remark: {
        type: String,
        default: '',
    },
});

const form = reactive({
    ...props.detail,
});

const validateBackgroundUrl = (rule: any, value: any, callback: any) => {
    if (!value) {
        callback(new Error('请选择图片'));
    } else {
        callback();
    }
};

const validateQuestionContent = (rule: any, value: any, callback: any) => {
    if (!value) {
        callback(new Error('请输入题目内容'));
    } else {
        callback();
    }
};

const validateOptions = (rule: any, value: any[], callback: any) => {
    if (!value || value.length < 2) {
        callback(new Error('请至少添加两个选项'));
    } else if (form.questionType === 'choiceTopic' && !value.find((item) => item.correct)) {
        callback(new Error('请选择一个正确选项'));
    } else {
        callback();
    }
};

const validateOptionContent = (rule: any, value: any, callback: any) => {
    if (!value) {
        callback(new Error('请输入选项内容'));
    } else {
        callback();
    }
};

const rules = reactive({
    'background.url': [{ required: true, validator: validateBackgroundUrl, trigger: 'blur' }],
    'question.content': [{ required: true, validator: validateQuestionContent, trigger: 'blur' }],
    options: [{ required: true, validator: validateOptions, trigger: 'change' }],
});

const dataFormRef = ref();

// 验证表单
const validate = () => {
    return new Promise((resolve, reject) => {
        dataFormRef.value?.validate((valid: any) => {
            if (valid) {
                resolve(true);
            } else {
                reject(new Error('表单验证失败'));
            }
        });
    });
};

// 创建并发送数据
const createAndEmitData = () => {
    // 手动创建非响应式数据的副本
    const nonReactiveData = {
        background: {
            type: 'image',
            url: form.background.url,
            path: form.background.path,
        },
        question: {
            type: 'text',
            content: form.question.content,
        },
        options: form.options.map((item, index) => ({
            ...item,
            option: String.fromCharCode(65 + index),
        })),
        questionType: form.questionType,
    };

    emit('change', nonReactiveData);
};

// 监听数据变化
watch(
    form,
    () => {
        createAndEmitData();
    },
    { deep: true }
);

// 选项相关方法
const onOptionCheckChange = (item: any) => {
    form.options.forEach((option: any) => {
        option.correct = option == item;
    });
};

const addOption = () => {
    if (form.options.length >= 6) {
        return;
    }
    form.options.push({ type: 'text', content: '', correct: false });
    // 延迟一帧再触发数据更新，避免立即验证
    nextTick(() => {
        createAndEmitData();
    });
};

const removeOption = (index: number) => {
    form.options.splice(index, 1);
};

defineExpose({
    validate,
});
</script>

<style lang="scss" scoped>
:deep(.el-form-item) {
    margin-bottom: 20px;
}

:deep(.el-form-item__label) {
    font-weight: normal;
}
</style>
