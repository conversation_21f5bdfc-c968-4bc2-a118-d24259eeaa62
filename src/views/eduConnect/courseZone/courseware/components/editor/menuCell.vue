<template>
    <div class="w-full">
        <!-- <div
            class="cell p-[12px] flex gap-[8px] items-center hover:bg-gray-100"
            :class="{
                'bg-gray-100': activeId == data.id,
                'cursor-pointer': (isPhase && selectable) || (!isPhase && !selectable),
            }"
            :style="{ paddingLeft: _deepIndex * 12 + 'px' }"
            @click="handleClick"
        >
            <template v-if="selectable">
                <el-checkbox v-if="isPhase" :model-value="data.isSelect" />
                <div v-else class="w-[18px]" />
            </template>
            <el-icon :class="{ active: activeId == data.id }">
                <div>
                    <Menu v-if="isPhase" />
                    <Tickets v-else />
                </div>
            </el-icon>
            <span class="flex-auto line-clamp-1" :class="{ active: activeId == data.id }">{{
                data.stepName || '-'
            }}</span>
            <el-dropdown v-if="!selectable">
                <el-icon class="more-icon" size="18"><MoreFilled /></el-icon>
                <template #dropdown>
                    <el-dropdown-menu>
                        <el-dropdown-item v-if="isPhase" @click="handleAddPhase">新建教学环节</el-dropdown-item>
                        <el-dropdown-item v-if="isPhase" @click="handleAddPhaseFromModel">
                            插入教学环节
                        </el-dropdown-item>
                        <el-dropdown-item @click="handleAddPage">插入教学页</el-dropdown-item>
                        <el-dropdown-item @click="handleRename">重命名</el-dropdown-item>
                        <el-dropdown-item :disabled="!deletable" @click="handleDelete">删除</el-dropdown-item>
                    </el-dropdown-menu>
                </template>
            </el-dropdown>
        </div> -->
        <!-- <template v-if="data.children && data.children.length > 0"> -->
        <template v-if="data.children">
            <div
                class="cell p-[12px] flex gap-[8px] items-center hover:bg-gray-100"
                :class="{
                    'bg-gray-100': activeId == data.id,
                    'cursor-pointer': (isPhase && selectable) || (!isPhase && !selectable),
                }"
                :style="{ paddingLeft: _deepIndex * 12 + 'px' }"
                @click="handleClick"
            >
                <template v-if="selectable">
                    <el-checkbox v-if="isPhase" :model-value="data.isSelect" />
                    <div v-else class="w-[18px]" />
                </template>
                <el-icon :class="{ active: activeId == data.id }">
                    <div>
                        <Menu v-if="isPhase" />
                        <Tickets v-else />
                    </div>
                </el-icon>
                <span class="flex-auto line-clamp-1" :class="{ active: activeId == data.id }">{{
                    data.stepName || '-'
                }}</span>
                <el-dropdown v-if="!selectable">
                    <el-icon class="more-icon" size="18"><MoreFilled /></el-icon>
                    <template #dropdown>
                        <el-dropdown-menu>
                            <el-dropdown-item v-if="isPhase" @click="handleAddPhase">新建教学环节</el-dropdown-item>
                            <el-dropdown-item v-if="isPhase" @click="handleAddPhaseFromModel">
                                插入教学环节
                            </el-dropdown-item>
                            <el-dropdown-item @click="handleAddPage">插入教学页</el-dropdown-item>
                            <el-dropdown-item @click="handleRename">重命名</el-dropdown-item>
                            <el-dropdown-item :disabled="!deletable" @click="handleDelete">删除</el-dropdown-item>
                        </el-dropdown-menu>
                    </template>
                </el-dropdown>
            </div>

            <vue-draggable
                v-model="data.children"
                :animation="150"
                ghostClass="ghost"
                :group="'child' + level"
                :disabled="selectable"
                @update="onOrderUpdate"
                @add="onAdd"
                @remove="remove"
                @end="onEnd"
            >
                <menu-cell
                    v-for="item in data.children"
                    :key="item.id"
                    :data="item"
                    :deep-index="_deepIndex + 1"
                    :selectable="selectable"
                    :active-id="activeId"
                    :level="level + 1"
                    :deletable="true"
                    @click="emit('click', $event)"
                    @check="emit('check', $event)"
                    @rename="emit('rename', $event)"
                    @delete="emit('delete', $event)"
                    @add-phase="emit('addPhase', $event)"
                    @add-phase-from-model="emit('addPhaseFromModel', $event)"
                    @add-page="emit('addPage', $event)"
                    @sort="emit('sort', $event)"
                />
            </vue-draggable>
        </template>
    </div>
</template>

<script setup lang="ts">
import { VueDraggable } from 'vue-draggable-plus';
import { useMessageBox } from '/@/hooks/message';
import menuCell from './menuCell.vue';
import { ElMessageBox } from 'element-plus';

const props = defineProps({
    activeId: { type: Number, default: -1 },
    data: { type: Object, default: () => ({}) },
    deepIndex: { type: Number, default: 1 },
    selectable: { type: Boolean, default: false },
    deletable: { type: Boolean, default: true },
    level: { type: Number, default: 1 },
});
const _deepIndex = props.deepIndex;
const isPhase = computed(() => props.data.type == 1); // 是否是环节

interface CellEmits {
    (e: 'click', value: typeof props.data): void;
    (e: 'addPhase', value: object): void;
    (e: 'addPhaseFromModel', value: typeof props.data): void;
    (e: 'addPage', value: typeof props.data): void;
    (e: 'rename', value: { data: typeof props.data; newName: string }): void;
    (e: 'delete', value: typeof props.data): void;
    (e: 'check', value: typeof props.data): void;
    (e: 'sort', value: object[]): void;
    (e: 'add', value: object[]): void;
    (e: 'remove', value: object[]): void;
    (e: 'end'): void;
}
const emit = defineEmits<CellEmits>();

const handleClick = () => {
    if (props.selectable && isPhase.value) {
        handleCheck(!props.data.isSelect);
    }
    if (!isPhase.value) {
        emit('click', { ...props.data });
    }
};

const handleCheck = (val: boolean) => {
    emit('check', { ...props.data, isSelect: val });
};

// 新建教学环节
const handleAddPhase = () => {
    ElMessageBox.prompt('', '新建教学环节', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPlaceholder: '请输入环节名称',
        inputValidator: (val) => {
            const value = (val ?? '').trim();
            if (value.length == 0) return '请输入环节名称';
            if (value.length > 50) return '不能超过50个字符';
            return true;
        },
        inputErrorMessage: '请输入环节名称',
    }).then(({ value }) => {
        emit('addPhase', {
            stepName: value,
            stepParent: props.data.stepParent,
            type: props.data.type,
            stepOrder: props.data.stepOrder + 1,
            pageTemplateId: 0,
        });
    });
};
// 插入教学环节(从模版选择)
const handleAddPhaseFromModel = () => {
    emit('addPhaseFromModel', { ...props.data });
};

// 插入教学页
const handleAddPage = () => {
    emit('addPage', { ...props.data });
};
// 重命名
const handleRename = () => {
    ElMessageBox.prompt('', '重命名', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValue: props.data.stepName,
        inputPlaceholder: '请输入名称',
        inputValidator: (val) => {
            const value = (val ?? '').trim();
            if (value.length == 0) return '请输入名称';
            if (value.length > 50) return '不能超过50个字符';
            return true;
        },
        inputErrorMessage: '请输入名称',
    }).then(({ value }) => {
        emit('rename', { data: { ...props.data }, newName: value });
    });
};
// 删除
const handleDelete = async () => {
    try {
        await useMessageBox().confirm(
            isPhase.value
                ? '确定要删除此教学环节吗？<br/>删除环节将会同时删除子页面，删除后将无法恢复'
                : '确定要删除此教学页吗？<br/>删除后将无法恢复',
            true
        );
    } catch {
        return;
    }
    emit('delete', { ...props.data });
};

// 排序
const onOrderUpdate = () => {
    emit('sort', [...(props.data.children || [])]);
};
const onAdd = () => {
    const children = Array.isArray(props.data?.children) ? props.data.children : [];

    // 构建新的数组并传递给 emit
    const updatedChildren = children.map((item) => ({
        ...item,
        stepParent: props.data.id,
    }));

    emit('add', updatedChildren);
};
const remove = () => {
    emit('remove', [...(props.data.children || [])]);
};
const onEnd = () => {
    emit('end');
};
</script>

<style lang="scss" scoped>
.active {
    color: var(--el-color-primary);
}

.more-icon {
    visibility: hidden;
}
.cell:hover .more-icon {
    visibility: visible;
}

:deep(.el-dialog__header) {
    padding: 0px !important;
}
:deep(.el-dialog__body) {
    padding: 1px !important;
}
</style>
