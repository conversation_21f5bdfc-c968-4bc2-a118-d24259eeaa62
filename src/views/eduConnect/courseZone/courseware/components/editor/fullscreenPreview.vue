<template>
    <div>
        <!-- 全屏容器 -->
        <div ref="fullscreenContainer" class="fullscreen-container" :class="{ 'fullscreen-active': isFullscreen }">
            <div class="content" ref="playerRef"></div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue';
import { SlidePlayer } from 'courseware-player';
import { coursewarePreview } from '/@/api/integraEdu/courseware';
import { useMessage } from '/@/hooks/message';

import screenfull from 'screenfull';
const isFullscreen = ref(false);
const fullscreenContainer = ref<HTMLElement>();
const previewPlayer = ref();
const playerRef = ref();
const props = defineProps({
    materielId: {
        type: String,
        default: '',
    },
    coursewareId: {
        type: String,
        default: '',
    },
});
const coursewareData = ref<any>([]); //课件是否为空
const emit = defineEmits(['exitFullscreen']);
// 切换全屏状态
const toggleFullscreen = async () => {
    if (!document.fullscreenElement) {
        await getCoursewarePreviewData();
        if (coursewareData.value.data.length === 0) {
            useMessage().warning('暂无教学页可预览，请先保存教学页');
            emit('exitFullscreen');
            return;
        }
        await enterFullscreen();
    } else {
        await exitFullscreen();
    }
};
// 进入全屏
const enterFullscreen = async () => {
    if (screenfull.isEnabled && fullscreenContainer.value) {
        try {
            await screenfull.request(fullscreenContainer.value);
            createPlay(); // 确保全屏后初始化播放器
        } catch (err) {
            console.error('全屏失败:', err);
        }
    }
};
const getCoursewarePreviewData = async () => {
    coursewareData.value = await coursewarePreview({
        coursewareId: props.coursewareId,
        coursewareDataId: props.materielId,
    });
};
const createPlay = async () => {
    try {
        if (previewPlayer.value) {
            previewPlayer.value.destroy();
        }
        previewPlayer.value = new SlidePlayer({
            container: playerRef.value,
            getPlaylist: (): any => {
                return coursewareData.value;
            },

            options: {
                hidePageNumber: false,
                hideUtilityToolbar: true,
            },
        });
        previewPlayer.value.on('close', () => {
            exitFullscreen();
        });
    } catch (error) {
        console.error(error);
    }
};
// 退出全屏
const exitFullscreen = () => {
    if (screenfull.isEnabled && screenfull.isFullscreen) {
        screenfull.exit();
    }
};

// 监听全屏变化
const handleFullscreenChange = () => {
    isFullscreen.value = screenfull.isFullscreen;
    if (!isFullscreen.value) {
        previewPlayer.value?.destroy();
        emit('exitFullscreen');
    }
};

// 键盘事件监听
// const handleKeyDown = (e: KeyboardEvent) => {
//     if (e.key === 'Escape' && isFullscreen.value) {
//         exitFullscreen();
//     }
// };

// 生命周期
onMounted(() => {
    if (screenfull.isEnabled) {
        screenfull.on('change', handleFullscreenChange);
    }
});

onBeforeUnmount(() => {
    if (screenfull.isEnabled) {
        screenfull.off('change', handleFullscreenChange);
    }
});

defineExpose({
    toggleFullscreen,
});
</script>

<style scoped>
.fullscreen-container {
    display: none;
    background: rgba(0, 0, 0, 0.9);
}

.fullscreen-container.fullscreen-active {
    display: flex;
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.content {
    width: 100vw;
    height: 100vh;
    text-align: center;
    color: white;
}

.preview-image {
    max-width: 100%;
    max-height: 80vh;
    border: 2px solid #fff;
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
}

.close-button {
    margin-top: 20px;
}

/* 全屏模式样式 */
:fullscreen .content {
    background: rgba(0, 0, 0, 0.95);
}

:-ms-fullscreen .content {
    background: rgba(0, 0, 0, 0.95);
}

:-webkit-full-screen .content {
    background: rgba(0, 0, 0, 0.95);
}
</style>
