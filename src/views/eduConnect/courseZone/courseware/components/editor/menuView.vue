<template>
    <div ref="scrollRef" class="flex flex-col items-center justify-between h-full" v-loading="loading">
        <template v-if="dataArray.length">
            <el-scrollbar class="menu-view w-full text-gray-600">
                <vue-draggable
                    v-model="dataArray"
                    :animation="150"
                    ghostClass="ghost"
                    group="people"
                    @update="
                        () => {
                            onOrderUpdate([...dataArray]);
                        }
                    "
                >
                    <menu-cell
                        v-for="item in dataArray"
                        :key="item.id"
                        :data="item"
                        :active-id="activeData?.id ?? -1"
                        :deletable="dataArray.length > 1"
                        :level="1"
                        @click="handleClickCell"
                        @rename="handleRename"
                        @delete="handleDelete"
                        @add-phase="handleAddPhaseAndPage"
                        @add-phase-from-model="handleAddPhaseFromModel"
                        @add-page="handleAddPage"
                        @sort="onOrderUpdate"
                        @add="handleAdd"
                        @remove="handleRemove"
                        @end="handleEnd"
                    />
                </vue-draggable>
            </el-scrollbar>
            <el-button class="my-[12px]" :disabled="loading" @click="handleSaveToModel">保存为模版</el-button>
        </template>
        <el-empty v-else class="m-auto" description="没有找到数据哦~" />

        <phase-model-alert ref="phaseModelDialog" @complete="handleAddPhaseFromModelComplete" />
        <page-picker ref="pagePickerRef" @complete="handleAddPageComplete" />
    </div>
</template>

<script setup lang="ts">
import { VueDraggable } from 'vue-draggable-plus';
import menuCell from './menuCell.vue';
import {
    fetchTeachPhases,
    editTeachPhase,
    sortTeachPhase,
    deleteTeachPhase,
    addTeachPhase,
    addTeachPhaseFromTemplate,
    addTeachPhaseTemplate,
} from '/@/api/integraEdu/courseware';
import { useMessage } from '/@/hooks/message';
import { ElMessageBox } from 'element-plus';
import phaseModelAlert from './phaseModelAlert.vue';
import pagePicker from '/@/views/eduConnect/courseZone/teachPage/pagePicker.vue';

const props = defineProps({
    coursewareName: { type: String, default: '' },
    coursewareId: { type: String, default: '' },
    materielId: { type: String, default: '' },
    dataTemplateId: { type: String, default: '' },
});
const dataArray = ref<any[]>([]);
const loading = ref(false);
const activeData = ref<undefined | any>(undefined);
const scrollRef = ref();
const phaseModelDialog = ref();
const pagePickerRef = ref();
const willEditCellData = ref<any>();
const emit = defineEmits(['change', 'clickCell', 'stepNameChange']);
const changedList = ref<any[]>([]);

onMounted(() => {
    loadData();
});

// 获取菜单数据
const loadData = () => {
    loading.value = true;
    fetchTeachPhases(props.coursewareId, props.materielId)
        .then((res) => {
            dataArray.value = res.data || [];
        })
        .catch((err) => {
            useMessage().error(err.msg);
        })
        .finally(() => {
            loading.value = false;
        });
};

// 点击行
const handleClickCell = (data: any) => {
    activeData.value = data;
    emit('clickCell', data);
};
// 添加教学页
const handleAddPage = (data: any) => {
    willEditCellData.value = data;
    pagePickerRef.value.open();
};
const handleAddPageComplete = (pageValue: any) => {
    const isPhase = willEditCellData.value.type == 1;
    handleAddPhaseAndPage({
        stepName: pageValue.templateName || '教学页',
        stepParent: isPhase ? willEditCellData.value.id : willEditCellData.value.stepParent,
        type: 2,
        stepOrder: isPhase ? 1 : willEditCellData.value.stepOrder + 1,
        pageTemplateId: pageValue.id,
    });
    willEditCellData.value = null;
};
// 添加环节和页面
const handleAddPhaseAndPage = (value: any) => {
    addTeachPhase({ ...value, coursewareId: props.coursewareId, coursewareDataId: props.materielId })
        .then((res) => {
            useMessage().success('添加成功');
            loadData();
            emit('change');
            if (res.data.stepParent) {
                handleClickCell(res.data);
            }
        })
        .catch((err) => {
            useMessage().error(err.msg);
        });
};
// 从模版添加环节
const handleAddPhaseFromModel = (data: any) => {
    willEditCellData.value = data;
    phaseModelDialog.value.open();
};
const handleAddPhaseFromModelComplete = (value: any) => {
    addTeachPhaseFromTemplate({
        ...value,
        stepOrder: willEditCellData.value.stepOrder + 1,
        coursewareId: props.coursewareId,
        coursewareDataId: props.materielId,
        dataTemplateId: props.dataTemplateId,
    })
        .then(() => {
            useMessage().success('添加成功');
            loadData();
            emit('change');
        })
        .catch((err) => {
            useMessage().error(err.msg);
        });
    willEditCellData.value = null;
};

// 重命名
const handleRename = (val: any) => {
    editTeachPhase({
        ...val.data,
        stepName: val.newName,
        coursewareId: props.coursewareId,
        coursewareDataId: props.materielId,
    })
        .then(() => {
            useMessage().success('修改成功');
            loadData();
            emit('change');
            emit('stepNameChange', {
                ...val.data,
                stepName: val.newName,
            });
        })
        .catch((err) => {
            useMessage().error(err.msg);
        });
};

// 删除
const handleDelete = (val: any) => {
    deleteTeachPhase(val.id, props.coursewareId)
        .then(() => {
            useMessage().success('删除成功');
            loadData();
            emit('change');
            if (activeData.value && (activeData.value.id == val.id || activeData.value.stepParent == val.id)) {
                emit('clickCell', 'reset');
            }
        })
        .catch((err) => {
            useMessage().error(err.msg);
        });
};

// 保存为模版
const handleSaveToModel = () => {
    ElMessageBox.prompt('', '保存为模版', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPlaceholder: '请输入模版名称',
        inputValidator: (val) => {
            const value = (val ?? '').trim();
            if (value.length == 0) return '请输入模版名称';
            if (value.length > 50) return '不能超过50个字符';
            return true;
        },
        inputErrorMessage: '请输入模版名称',
    }).then(({ value }) => {
        addTeachPhaseTemplate({
            coursewareId: props.coursewareId,
            coursewareDataId: props.materielId,
            templateName: value,
        })
            .then(() => {
                useMessage().success('保存成功');
            })
            .catch((err) => {
                useMessage().error(err.msg);
            });
    });
};
const handleAdd = (data: any) => {
    changedList.value = [...data];
};
const handleRemove = (data: any) => {
    changedList.value = [...unref(changedList.value), ...data];
};
const handleEnd = () => {
    if (changedList.value.length) {
        onOrderUpdate(changedList.value);
    }
};
// 排序
const onOrderUpdate = (array: any[]) => {
    loading.value = true;
    sortTeachPhase({
        coursewareId: props.coursewareId,
        coursewareDataId: props.materielId,
        infoList: array.map((item: any, index: number) => ({
            id: item.id,
            stepOrder: index + 1,
            stepParent: item.stepParent,
        })),
    })
        .catch((err) => {
            useMessage().error(err.msg);
        })
        .finally(() => {
            loading.value = false;
            loadData();
            changedList.value = [];
            emit('clickCell', '');
        });
};
</script>

<style lang="scss" scoped></style>
