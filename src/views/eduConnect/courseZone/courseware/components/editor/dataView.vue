<template>
    <div class="h-full flex" v-loading="loading">
        <!-- 左侧菜单 -->
        <div class="w-25 border-l border-gray-300 pt-[49px] bg-gray-100">
            <div
                v-for="(tab, index) in tabs"
                :key="index"
                class="p-3 cursor-pointer border-b border-l-4 border-l-transparent border-gray-200 flex flex-col items-center justify-center text-center transition-all duration-200"
                :class="{ 'bg-white !border-l-[#4ea259]': activeIndex === tab.id }"
                @click="setActiveIndex(tab.id)"
            >
                <img :src="tab.icon" class="w-12 h-12" draggable="false" />
                <span class="text-xs pt-1" draggable="false">{{ tab.label }}</span>
            </div>
        </div>

        <!-- 右侧内容区 -->
        <div class="flex-1 h-full overflow-hidden">
            <!-- 内容编辑 -->
            <div v-show="activeIndex === 1" class="h-full overflow-y-auto">
                <Suspense>
                    <template #default>
                        <component
                            v-if="editComponent"
                            ref="contentEditRef"
                            :is="editComponent"
                            v-bind="componentProps"
                            @change="handleContentChange"
                        />
                    </template>
                    <template #fallback>
                        <div class="p-4 text-center text-gray-500">Loading...</div>
                    </template>
                </Suspense>
            </div>

            <!-- 飞飞辅助工具 -->
            <div v-show="activeIndex === 3" class="h-full">
                <feifei-auxiliary-tool
                    ref="feifeiRef"
                    :key="`feifei-${props.menuData?.id || 'default'}`"
                    :data="defaultAssistantData"
                    @change="handleAssistantChange"
                />
            </div>

            <!-- 笔顺工具 -->
            <div v-show="activeIndex === 4" class="h-full">
                <strokeOrder
                    ref="strokeOrderRef"
                    :key="`stroke-${props.menuData?.id || 'default'}`"
                    :data="defaultStrokeOrderData"
                    @change="handleStrokeOrderChange"
                />
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
// import { useMessage } from '/@/hooks/message';
import strokeOrder from '../pageUtils/strokeOrder.vue';
// import phaseGuideView_bak from '../../backup/phaseGuideView_bak.vue';
import feifeiAuxiliaryTool from '../pageUtils/feifeiAuxiliaryTool.vue';
// import { editTeachPageDetail } from '/@/api/integraEdu/courseware';
import { templateList } from '../../constants/templateList';
import { config } from '../../constants/templateConfig';
import contentIcon from '/@/assets/courseware/content.webp';
import feifeiIcon from '/@/assets/courseware/feifei.webp';
import strokeOrderIcon from '/@/assets/courseware/stroke.webp';

// Props 定义
const props = defineProps({
    coursewareId: { type: String, default: '' },
    materielId: { type: String, default: '' },
    menuData: { type: Object, default: () => ({}) },
    phaseData: { type: Object, default: () => ({}) },
    data: { type: Object, default: () => ({}) },
    stepId: { type: [String, Number], default: '' },
});

const emit = defineEmits(['change']);

// 基础状态
const activeIndex = ref(1);
const loading = ref(false);

// Tab 定义
const tabs = [
    { id: 1, label: '内容编辑', icon: contentIcon },
    { id: 3, label: '飞飞辅助工具', icon: feifeiIcon },
    { id: 4, label: '笔顺工具', icon: strokeOrderIcon },
];

// 添加 refs
const contentEditRef = ref();
const feifeiRef = ref();
const strokeOrderRef = ref();

// 添加状态管理
const contentData = ref<any>(null);
const assistantData = ref<any>(null);
const strokeOrderData = ref<any>(null);
const currentPageId = ref<string | null>(null);
const validationErrors = ref<{ [key: string]: boolean }>({
    content: false,
    assistant: false,
    strokeOrder: false,
});

// 添加数据状态管理
const dataState = reactive({
    isDirty: false,
    isLoading: false,
    error: null as Error | null,
});

// 计算属性
const editComponent = computed(() => {
    const component = templateList.find((item) => item.pageTemplateId == props.data.pageTemplateId)?.component;

    if (!component) return null;

    return defineAsyncComponent({
        loader: component,
        timeout: 10000,
        suspensible: true,
        onError: (error, retry, fail, attempts) => {
            if (attempts <= 3) retry();
            else fail();
        },
    });
});

// 重置数据方法
const resetPageData = () => {
    contentData.value = null;
    assistantData.value = null;
    strokeOrderData.value = null;
    dataState.isDirty = false;
    validationErrors.value = {
        content: false,
        assistant: false,
        strokeOrder: false,
    };
};

// 确保在props.data或menuData变化时初始化所有数据
watch(
    [() => props.data, () => props.menuData?.id],
    ([newData, newMenuId]) => {
        // 如果页面ID变化，重置所有数据
        if (newMenuId !== currentPageId.value) {
            resetPageData();
            currentPageId.value = newMenuId;
        }

        if (newData) {
            // 初始化内容数据
            if (newData.details) {
                contentData.value = newData.details;
            }
            // 初始化工具数据
            if (newData.tool) {
                assistantData.value = newData.tool.assistant || null;
                strokeOrderData.value = newData.tool.strokeOrder || null;
            } else {
                // 如果没有工具数据，确保置为null
                assistantData.value = null;
                strokeOrderData.value = null;
            }
            dataState.isDirty = false;
        }
    },
    { immediate: true }
);

const componentProps = computed(() => {
    return {
        detail: {
            ...props.data.details,
            stepName: props.data.stepName,
        },
        config,
        remark: props.data.remark || '',
    };
});

const defaultAssistantData = computed(() => {
    // 使用深拷贝返回数据，避免共享引用
    const defaultData = {
        enable: false,
        content: {
            text: '',
            type: 'audio',
            url: '',
            fileName: '',
        },
    };

    if (props.data.tool?.assistant) {
        // 深拷贝，确保返回全新对象
        return {
            enable: props.data.tool.assistant.enable,
            content: {
                text: props.data.tool.assistant.content?.text || '',
                type: props.data.tool.assistant.content?.type || 'audio',
                url: props.data.tool.assistant.content?.url || '',
                fileName: props.data.tool.assistant.content?.fileName || '',
            },
        };
    } else {
        return defaultData;
    }
});

const defaultStrokeOrderData = computed(() => {
    // 使用深拷贝返回数据，避免共享引用
    const defaultData = {
        enable: false,
        content: [],
    };

    if (props.data.tool?.strokeOrder) {
        const content = props.data.tool.strokeOrder.content || [];
        return {
            enable: props.data.tool.strokeOrder.enable,
            content: content.map((item: any) => ({
                word: item.word || '',
                url: item.url || '',
                path: item.path || '',
                type: item.type || 'image',
            })),
        };
    } else {
        return defaultData;
    }
});

// 统一的数据变更处理
const emitDataChange = () => {
    dataState.isDirty = true;
    const data: any = {
        details: contentData.value ? toRaw(contentData.value) : null,
        tool: {},
    };

    if (assistantData.value) {
        data.tool.assistant = toRaw(assistantData.value);
    }

    if (strokeOrderData.value) {
        data.tool.strokeOrder = toRaw(strokeOrderData.value);
    }

    emit('change', data);
};

// 处理各个组件的数据变更
const handleContentChange = (val: any) => {
    contentData.value = val;
    emitDataChange();
};

const handleAssistantChange = (val: any) => {
    assistantData.value = val;
    emitDataChange();
};

const handleStrokeOrderChange = (val: any) => {
    strokeOrderData.value = val;
    emitDataChange();
};

// 验证所有表单
const validateAllForms = async () => {
    const validations = [
        { ref: contentEditRef, key: 'content' },
        { ref: feifeiRef, key: 'assistant' },
        { ref: strokeOrderRef, key: 'strokeOrder' },
    ];

    const results = await Promise.all(
        validations.map(async ({ ref }) => {
            if (!ref.value?.validate) return true;
            try {
                await ref.value.validate();
                return true;
            } catch {
                return false;
            }
        })
    );

    validationErrors.value = validations.reduce((acc, { key }, index) => {
        acc[key] = results[index];
        return acc;
    }, {} as Record<string, boolean>);

    return results.every(Boolean);
};

// 获取所有数据的方法
const getAllData = async () => {
    const isValid = await validateAllForms();
    if (!isValid) {
        return null;
    }

    // 创建深拷贝，避免共享引用
    const saveData: any = {
        details: null,
        tool: {},
    };

    if (contentData.value) {
        // 为内容数据创建深拷贝
        saveData.details = JSON.parse(JSON.stringify(contentData.value));
    }

    if (assistantData.value) {
        // 为飞飞助手工具创建深拷贝
        saveData.tool.assistant = {
            enable: assistantData.value.enable,
            content: {
                text: assistantData.value.content?.text || '',
                type: assistantData.value.content?.type || 'audio',
                url: assistantData.value.content?.url || '',
                fileName: assistantData.value.content?.fileName || '',
            },
        };
    }

    if (strokeOrderData.value) {
        // 为笔顺工具创建深拷贝
        const content = strokeOrderData.value.content || [];
        saveData.tool.strokeOrder = {
            enable: strokeOrderData.value.enable,
            content: content.map((item: any) => ({
                word: item.word || '',
                url: item.url || '',
                path: item.path || '',
                type: item.type || 'image',
            })),
        };
    }

    return saveData;
};

// 公开方法
const setActiveIndex = (index: number) => {
    activeIndex.value = index;
};

defineExpose({
    activeIndex,
    setActiveIndex,
    getAllData,
    validationErrors,
});
</script>

<style lang="scss" scoped>
/* 确保内容区域可滚动 */
.overflow-y-auto {
    overflow-y: auto;
}

/* 确保高度正确传递 */
.h-full {
    height: 100%;
}

:deep(.el-collapse-item__header) {
    background-color: #f9f9f9;
    padding-left: 24px;
    box-sizing: border-box;
    font-size: 14px;
    font-weight: normal;
}
:deep(.el-collapse-item__content) {
    padding: 24px 12px 24px 24px;
}
</style>
