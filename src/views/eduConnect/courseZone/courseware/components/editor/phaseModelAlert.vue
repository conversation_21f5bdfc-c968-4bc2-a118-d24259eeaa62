<template>
    <el-dialog title="选择你要插入的教学环节" v-model="visible" :close-on-click-modal="false" width="700">
        <div class="flex h-[400px] gap-0" v-loading="loading">
            <template v-if="dataArray.length">
                <div class="w-[200px]">
                    <el-scrollbar class="w-full">
                        <div
                            class="w-full px-[10px] py-[14px] text-[15px] hover:bg-gray-100 hover:text-primary flex items-center cursor-pointer"
                            :class="{ 'bg-gray-100 text-primary': selectIndex == index }"
                            v-for="(item, index) in dataArray"
                            :key="index"
                            @click="handleSelectMenu(index)"
                        >
                            <el-icon class="mr-[6px]"><Collection /></el-icon>
                            <span class="line-clamp-1">{{ item.templateName }}</span>
                        </div>
                    </el-scrollbar>
                </div>
                <el-divider class="h-full m-0" direction="vertical" />
                <div class="flex-1 h-full pl-[12px] flex flex-col items-start gap-[12px]" v-loading="infoLoading">
                    <el-scrollbar v-if="selectIndex >= 0">
                        <menu-cell
                            v-for="(item, index) in dataArray[selectIndex].details"
                            :key="index"
                            :data="item"
                            :selectable="true"
                            @check="handleCheck"
                        />
                    </el-scrollbar>
                    <el-button
                        :type="isAllSelected ? 'primary' : ''"
                        text
                        :icon="isAllSelected ? 'CircleCheckFilled' : 'CircleCheck'"
                        @click="handleSelectAll"
                    >
                        全选
                    </el-button>
                </div>
            </template>
            <el-empty v-else class="m-auto" description="没有找到数据哦~" />
        </div>

        <template #footer>
            <el-button @click="visible = false">取消</el-button>
            <el-button type="primary" :disabled="loading || !dataArray.length" @click="onConfirm">确认</el-button>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
import menuCell from './menuCell.vue';
import { fetchTeachPhaseTemplate, fetchTeachPhaseTemplateInfo } from '/@/api/integraEdu/courseware';
import { useMessage } from '/@/hooks/message';

const visible = ref(false);
const loading = ref(false);
const infoLoading = ref(false);
const selectIndex = ref(-1);
const dataArray = ref<any[]>([]);
const isAllSelected = computed(() => {
    if (selectIndex.value < 0 || !dataArray.value[selectIndex.value].details) return false;
    return dataArray.value[selectIndex.value].details.every((item: any) => item.isSelect);
});
const emit = defineEmits(['complete']);

// 打开弹窗
const open = () => {
    dataArray.value = [];
    selectIndex.value = -1;
    visible.value = true;
    loadData();
};

const loadData = () => {
    loading.value = true;
    fetchTeachPhaseTemplate()
        .then((res) => {
            dataArray.value = res.data || [];
            if (dataArray.value.length) {
                selectIndex.value = 0;
                loadDetail();
            }
        })
        .catch((err) => {
            useMessage().error(err.msg);
        })
        .finally(() => {
            loading.value = false;
        });
};

const loadDetail = () => {
    infoLoading.value = true;
    fetchTeachPhaseTemplateInfo(dataArray.value[selectIndex.value].id)
        .then((res) => {
            (res.data || []).forEach((item: any) => {
                item.isSelect = false;
            });
            dataArray.value[selectIndex.value].details = res.data || [];
        })
        .catch((err) => {
            useMessage().error(err.msg);
        })
        .finally(() => {
            infoLoading.value = false;
        });
};

const handleSelectMenu = (index: number) => {
    selectIndex.value = index;
    if (!dataArray.value[selectIndex.value].details || !dataArray.value[selectIndex.value].details.length) {
        loadDetail();
    }
};

const handleCheck = (value: any) => {
    dataArray.value[selectIndex.value].details.forEach((item: any) => {
        if (item.id == value.id) {
            item.isSelect = value.isSelect;
        }
    });
};

const handleSelectAll = () => {
    const val = isAllSelected.value;
    dataArray.value[selectIndex.value].details.forEach((item: any) => {
        item.isSelect = !val;
    });
};

const onConfirm = () => {
    const array: any[] = dataArray.value[selectIndex.value].details.filter((item: any) => item.isSelect);
    if (array.length == 0) {
        useMessage().warning('请选择要添加的教学环节');
        return;
    }
    emit('complete', {
        stepTemplateId: dataArray.value[selectIndex.value].id,
        stepIds: array.map((item: any) => item.id),
    });
    visible.value = false;
};

// 暴露变量
defineExpose({
    open,
});
</script>

<style lang="scss" scoped></style>
