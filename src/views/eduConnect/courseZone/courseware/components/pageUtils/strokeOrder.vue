<template>
    <div class="h-full flex flex-col">
        <el-row type="flex" align="middle" class="border-b h-12 px-6 bg-[#f9f9f9] flex-shrink-0">
            <span>是否启用笔顺工具</span>
            <el-switch v-model="form.enable" class="ml-2" active-value :active-color="'#13ce66'" />
            <el-button :disabled="!form.enable" type="primary" size="small" @click="addStroke" class="ml20" plain>
                增加笔顺
            </el-button>
        </el-row>
        <div class="flex-1 h-full overflow-hidden">
            <div v-loading="loading" class="h-full overflow-y-auto">
                <el-form ref="strokeForm" :model="form" :rules="rules" label-position="top" type="inline" class="px-6">
                    <div
                        v-for="(item, index) in form.content"
                        :key="index"
                        class="flex items-start mb10 border-b border-gray-300 py-6 gap-x-4"
                    >
                        <el-form-item
                            :label="`笔顺${index + 1}文字`"
                            :prop="'content.' + index + '.word'"
                            :rules="rules.word"
                        >
                            <el-input
                                v-model="item.word"
                                :disabled="!form.enable"
                                style="width: 220px"
                                maxlength="4"
                                placeholder="请输入文字"
                                show-word-limit
                                type="text"
                            />
                        </el-form-item>
                        <el-form-item :label="'笔顺图片'" :prop="'content.' + index + '.url'" :rules="rules.imagePath">
                            <image-oss
                                service-name="teaching"
                                v-model:relative-path="item.url"
                                v-model:full-path="item.path"
                                :disabled="!form.enable"
                                module-name="courseware"
                                :file-type="['jpg', 'png', 'jpeg']"
                                :fileSize="10"
                                width="80px"
                                height="80px"
                                :iconSize="20"
                            />
                        </el-form-item>
                        <el-button
                            :disabled="!form.enable"
                            type="danger"
                            icon="Delete"
                            plain
                            circle
                            class="self-center"
                            @click="handleDelete(index)"
                        >
                        </el-button>
                    </div>
                </el-form>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, reactive, watch } from 'vue';
import ImageOss from '/@/components/Upload/ImageOss.vue';
import { useMessage } from '/@/hooks/message';
import type { FormInstance } from 'element-plus';

const emit = defineEmits(['change', 'validate']);
const loading = ref(false);

const validateText = (rule: any, value: any, callback: any) => {
    if (form.enable && value === '') {
        callback(new Error('请输入文字'));
    } else if (form.enable && value.trim() === '') {
        callback(new Error('笔顺文字不能全部为空格'));
    } else {
        callback();
    }
};

const validateUrl = (rule: any, value: any, callback: any) => {
    if (form.enable && value === '') {
        callback(new Error('请上传图片'));
    } else {
        callback();
    }
};

const rules = reactive({
    word: [{ required: true, validator: validateText, trigger: 'change' }],
    imagePath: [{ required: true, validator: validateUrl, trigger: 'blur' }],
});

const props = defineProps({
    data: {
        type: Object,
        default: () => ({
            enable: false,
            content: [] as StrokeItemType[],
        }),
    },
});

interface StrokeItemType {
    word: string;
    url: string;
    path: string;
    type: string;
}

// 创建独立的本地状态，不直接引用props
const form = reactive<{ enable: boolean; content: StrokeItemType[] }>({
    enable: false,
    content: [],
});

// 监听props变化，完全重置组件状态
watch(
    () => props.data,
    (newData) => {
        if (newData) {
            // 重置状态
            form.enable = newData.enable;

            // 清空内容并重新填充
            form.content = [];

            if (newData.content && newData.content.length > 0) {
                // 深拷贝数据，避免引用原始对象
                newData.content.forEach((item: any) => {
                    form.content.push({
                        word: item.word || '',
                        url: item.url || '',
                        path: item.path || '',
                        type: item.type || 'image',
                    });
                });
            } else if (form.content.length === 0) {
                // 添加默认项
                form.content.push({
                    word: '',
                    url: '',
                    path: '',
                    type: 'image',
                });
            }
        }
    },
    { immediate: true, deep: true }
);

const strokeForm = ref<FormInstance>();

// 验证表单
const validate = () => {
    if (!form.enable) {
        return Promise.resolve(true);
    }
    return new Promise((resolve, reject) => {
        strokeForm.value?.validate((valid: any) => {
            if (valid) {
                resolve(true);
            } else {
                reject(new Error('表单验证失败'));
            }
        });
    });
};

// 创建并发送数据
const createAndEmitData = () => {
    // 手动创建非响应式数据的副本
    const rawData = {
        enable: form.enable,
        content: form.content.map((item) => ({
            word: item.word,
            url: item.url,
            path: item.path,
            type: item.type,
        })),
    };
    emit('change', rawData);
};

// 监听数据变化
watch(
    form,
    () => {
        createAndEmitData();
    },
    { deep: true }
);

// 删除笔顺项
const handleDelete = (index: number) => {
    if (form.content.length > 0) {
        form.content.splice(index, 1);
    } else {
        useMessage().error('没有可删除的笔顺项！');
    }
};

// 增加笔顺项
const addStroke = () => {
    if (form.content.length >= 5) {
        useMessage().info('最多五个笔顺哦~');
        return;
    }
    const newStroke: StrokeItemType = {
        word: '',
        url: '',
        path: '',
        type: 'image',
    };
    form.content.push(newStroke);
};

defineExpose({
    validate,
});
</script>

<style lang="scss" scoped>
:deep(.el-form-item) {
    align-items: center;
}

.stroke-item {
    border: 1px solid #ebeef5;
    padding: 8px;
    height: 140px;
}
</style>
