<template>
    <div class="h-full flex flex-col">
        <el-row type="flex" align="middle" class="border-b h-12 px-6 bg-[#f9f9f9] flex-shrink-0">
            <span>是否启用飞飞辅助工具</span>
            <el-switch v-model="assistant.enable" class="ml-2" active-value :active-color="'#13ce66'" />
        </el-row>
        <div class="flex-1 h-full overflow-hidden">
            <div class="h-full overflow-y-auto">
                <el-form ref="dataFormRef" :model="assistant" formDialogRef label-position="top" :rules="rules">
                    <el-row>
                        <el-col :span="24">
                            <el-form-item label="内容" prop="content.text" :rules="rules.text">
                                <el-input
                                    :disabled="!assistant.enable"
                                    v-model="assistant.content.text"
                                    :maxlength="config?.feifei.feifeiInputMaxLength"
                                    show-word-limit
                                    placeholder="请输入内容"
                                    :autosize="{ minRows: 3 }"
                                    type="textarea"
                                />
                            </el-form-item>
                        </el-col>
                        <el-col>
                            <el-form-item label="音频" prop="content.url" :rules="rules.url">
                                <div
                                    v-if="assistant.content.url"
                                    class="w-full flex items-center gap-[3px] text-gray-600"
                                >
                                    <el-icon>
                                        <Document />
                                    </el-icon>
                                    <span class="line-clamp-1 overflow-hidden">{{
                                        assistant.content.fileName || assistant.content.url
                                    }}</span>
                                    <el-icon
                                        class="cursor-pointer text-red-400"
                                        :disabled="!assistant.enable"
                                        @click="handleDeleteAssistant"
                                    >
                                        <CircleClose />
                                    </el-icon>
                                </div>
                                <file-oss
                                    v-else
                                    moduleName="courseware"
                                    service-name="teaching"
                                    :file-type="config?.feifei.videoUploadType"
                                    :file-size="config?.feifei.videoUploadSize"
                                    :disabled="!assistant.enable"
                                    @success="
                                        assistant.content.url = $event.relativePath;
                                        assistant.content.fileName = $event.fileName;
                                    "
                                />
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { config } from '../../constants/templateConfig';
import FileOss from '/@/components/Upload/FileOss.vue';
import { Document, CircleClose } from '@element-plus/icons-vue';

const props = defineProps({
    data: {
        type: Object,
        default: () => ({
            enable: false,
            content: {
                text: '',
                type: 'audio',
                url: '',
                fileName: '',
            },
        }),
    },
});

const dataFormRef = ref();
// 使用深拷贝创建本地状态，避免直接引用props
const assistant = reactive({
    enable: false,
    content: {
        text: '',
        type: 'audio',
        url: '',
        fileName: '',
    },
});

// 初始化并监听props变化
watch(
    () => props.data,
    (newData) => {
        if (newData) {
            // 重置并使用深拷贝填充状态
            assistant.enable = newData.enable;
            assistant.content.text = newData.content?.text || '';
            assistant.content.type = newData.content?.type || 'audio';
            assistant.content.url = newData.content?.url || '';
            assistant.content.fileName = newData.content?.fileName || '';
        }
    },
    { immediate: true, deep: true }
);

const handleDeleteAssistant = () => {
    if (!assistant.enable) {
        return;
    }
    assistant.content.fileName = '';
    assistant.content.url = '';
};

const validateText = (rule: any, value: any, callback: any) => {
    if (assistant.enable && value === '') {
        callback(new Error('请输入文字'));
    } else {
        callback();
    }
};

const validateUrl = (rule: any, value: any, callback: any) => {
    if (assistant.enable && value === '') {
        callback(new Error('请上传音频'));
    } else {
        callback();
    }
};

const rules = reactive({
    text: [{ required: true, validator: validateText, trigger: 'change' }],
    url: [{ required: true, validator: validateUrl, trigger: 'blur' }],
});

const emit = defineEmits(['change', 'validate']);

const validate = () => {
    if (!assistant.enable) {
        return Promise.resolve(true);
    }
    return new Promise((resolve, reject) => {
        dataFormRef.value.validate((valid: any) => {
            if (valid) {
                resolve(true);
            } else {
                reject(new Error('表单验证失败'));
            }
        });
    });
};

const createAndEmitData = () => {
    // 手动创建非响应式数据的副本
    const rawData = {
        enable: assistant.enable,
        content: {
            text: assistant.content.text,
            type: assistant.content.type,
            url: assistant.content.url,
            fileName: assistant.content.fileName,
        },
    };
    emit('change', rawData);
};

// 监听数据变化
watch(
    assistant,
    () => {
        createAndEmitData();
    },
    { deep: true }
);

defineExpose({
    validate,
});
</script>

<style lang="scss" scoped>
:deep(.el-form-item) {
    padding-bottom: 36px !important;
}

:deep(.el-form) {
    padding: 20px;
}
</style>
