<template>
    <el-card class="mb-[12px] cursor-pointer" shadow="hover">
        <div class="text-[15px] font-medium">{{ data.title }}</div>
        <div class="text-gray-500 mt10">作者：{{ data.author }}</div>
        <div class="text-gray-500 mt10">课件数量：{{ data.coursewareCount }}</div>
    </el-card>
</template>

<script setup lang="ts">
defineProps({
    data: { type: Object, default: () => ({ title: '123' }) },
});
</script>

<style lang="scss" scoped></style>
