<template>
    <el-dialog title="添加课件" v-model="visible" :close-on-click-modal="false" width="600">
        <el-form
            ref="dataFormRef"
            :model="form"
            :rules="dataRules"
            formDialogRef
            label-width="90px"
            v-loading="loading"
        >
            <el-row :gutter="24">
                <el-col :span="24" class="mb20">
                    <el-form-item label="课件名称" prop="coursewareName">
                        <el-input
                            v-model.trim="form.coursewareName"
                            placeholder="请输入课件名称"
                            maxlength="50"
                            show-word-limit
                        />
                    </el-form-item>
                </el-col>

                <el-col :span="24" class="mb20">
                    <el-form-item label="选择版本" prop="bookVersionId">
                        <el-select v-model="form.bookVersionId" placeholder="请选择图书版本" size="large">
                            <el-option
                                v-for="(item, index) in versionData"
                                :key="index"
                                :label="item.press"
                                :value="item.id"
                            />
                        </el-select>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="visible = false">取消</el-button>
                <el-button type="primary" @click="onSubmit" :disabled="loading">确认</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script setup lang="ts" name="CoursewareDialog">
import { useMessage } from '/@/hooks/message';
import { fetchBookVersions } from '/@/api/integraEdu/bookTrack';
import { addCourseware } from '/@/api/integraEdu/courseware';

const emit = defineEmits(['refresh', 'version']);
const dataFormRef = ref();
const visible = ref(false);
const loading = ref(false);
const versionData = ref<any[]>([]);

// 提交表单数据
const form = reactive({
    bookId: '',
    coursewareName: '',
    bookVersionId: '',
});

// 定义校验规则
const dataRules = ref({
    coursewareName: [{ required: true, message: '课件名称不能为空', trigger: 'blur' }],
    bookVersionId: [{ required: true, message: '版本不能为空', trigger: 'change' }],
});

// 打开弹窗
const openDialog = (bookId: string) => {
    visible.value = true;
    form.bookId = '';
    nextTick(() => {
        dataFormRef.value?.resetFields();
    });
    if (bookId) {
        form.bookId = bookId;
        getBookVersionData(bookId);
    }
};

const getBookVersionData = (bookId: string) => {
    loading.value = true;
    fetchBookVersions({ bookId })
        .then((res: any) => {
            versionData.value = res.data || [];
        })
        .catch((err) => {
            useMessage().error(err.msg);
        })
        .finally(() => {
            loading.value = false;
        });
};

// 提交
const onSubmit = async () => {
    const valid = await dataFormRef.value.validate().catch(() => {});
    if (!valid) return false;
    try {
        loading.value = true;
        await addCourseware(form);
        useMessage().success('添加成功');
        visible.value = false;
        emit('refresh');
    } catch (err: any) {
        useMessage().error(err.msg);
    } finally {
        loading.value = false;
    }
};

// 暴露变量
defineExpose({
    openDialog,
});
</script>
