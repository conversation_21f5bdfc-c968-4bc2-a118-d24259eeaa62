<template>
    <el-dialog
        v-model="visible"
        :title="title"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :show-close="false"
        width="400"
    >
        <p>{{ message }}</p>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="onClose">{{ cancelText }}</el-button>
                <el-button type="primary" @click="onConfirm">{{ `${confirmText} (${secondCount}S)` }}</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
const emit = defineEmits(['cancel', 'confirm']);

defineProps({
    title: { type: String, default: '提示' },
    message: { type: String, default: '' },
    cancelText: { type: String, default: '取消' },
    confirmText: { type: String, default: '确认' },
});

const visible = ref(false);
const secondCount = ref(0);
const timer = ref();

const open = (second: number) => {
    visible.value = true;
    secondCount.value = second;
    timer.value = setInterval(function () {
        secondCount.value--;
        if (secondCount.value <= 0) {
            clearInterval(timer.value);
            onConfirm();
        }
    }, 1000);
};
const onClose = () => {
    clearInterval(timer.value);
    visible.value = false;
    emit('cancel');
};
const onConfirm = async () => {
    clearInterval(timer.value);
    visible.value = false;
    emit('confirm');
};

defineExpose({
    open,
});
</script>
