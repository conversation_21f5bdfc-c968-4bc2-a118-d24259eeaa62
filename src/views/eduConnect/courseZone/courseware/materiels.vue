<template>
    <div class="layout-padding">
        <div class="layout-padding-auto layout-padding-view">
            <navi-bar class="mb20" :from="decodeURIComponent(route.query.from as string)">
                <template #title>
                    <template v-if="!wareNameEdit">
                        <span class="text-lg font-600 line-clamp-1">{{ wareData.coursewareName }}</span>
                        <el-button
                            type="primary"
                            text
                            icon="edit"
                            :loading="wareLoading"
                            @click="wareNameEdit = true"
                        />
                    </template>
                    <template v-else>
                        <el-input
                            class="w-[200px] mr10"
                            v-model.trim="wareData.nameTemp"
                            placeholder="请输入课件名称"
                            maxlength="50"
                            @keyup.enter="handleEditWareName"
                        />
                        <el-button type="danger" plain icon="close" @click="handleEditWareNameCancel" />
                        <el-button
                            type="primary"
                            plain
                            icon="check"
                            :disabled="!wareData.nameTemp"
                            @click="handleEditWareName"
                        />
                    </template>
                </template>
                <template #extra>
                    <el-button
                        icon="promotion"
                        type="primary"
                        :disabled="!canPublish"
                        :loading="publishLoading"
                        @click="handlePublish"
                    >
                        发布
                    </el-button>
                </template>
            </navi-bar>

            <!-- 内容区域使用滚动组件包裹 -->
            <el-scrollbar height="calc(100vh - 120px)" class="content-scrollbar" v-loading="loading">
                <div class="content-container">
                    <!-- 课件资料区域 -->
                    <div class="mb-[12px] text-black font-medium bg-gray-100 rounded-[3px]">
                        <el-row>
                            <el-col :span="3">
                                <div class="item-view">
                                    <p class="line-clamp-1">是否可发布</p>
                                </div>
                            </el-col>
                            <el-col :span="5">
                                <div class="item-view">
                                    <p class="line-clamp-1">资料</p>
                                </div>
                            </el-col>
                            <el-col :span="4">
                                <div class="item-view">
                                    <p class="line-clamp-1">编辑人</p>
                                </div>
                            </el-col>
                            <el-col :span="4">
                                <div class="item-view">
                                    <p class="line-clamp-1">编辑时间</p>
                                </div>
                            </el-col>
                            <el-col :span="8">
                                <div class="item-view">
                                    <p class="line-clamp-1">操作</p>
                                </div>
                            </el-col>
                        </el-row>
                    </div>
                    <div v-if="coursewareData.length">
                        <div
                            class="mb-[12px] py-[6px] border border-solid border-gray-200 rounded-[3px] text-black"
                            v-for="(item, index) in coursewareData"
                            :key="'courseware-' + index"
                        >
                            <el-row>
                                <el-col :span="3">
                                    <div class="item-view">
                                        <el-icon v-if="item.canPublish == 1" size="16" color="var(--el-color-primary)">
                                            <SuccessFilled />
                                        </el-icon>
                                        <el-icon v-else size="16" color="var(--el-text-color-secondary)">
                                            <CircleCheck />
                                        </el-icon>
                                    </div>
                                </el-col>
                                <el-col :span="5">
                                    <div class="item-view">
                                        <p class="line-clamp-1">
                                            <span v-if="item.required == 1" class="text-red-400 mr4">※</span>
                                            <span>{{ item.coursewareName }}</span>
                                        </p>
                                    </div>
                                </el-col>
                                <el-col :span="4">
                                    <div class="item-view">
                                        <p class="line-clamp-1">{{ item.updateBy ?? '/' }}</p>
                                    </div>
                                </el-col>
                                <el-col :span="4">
                                    <div class="item-view">
                                        <p class="line-clamp-1">{{ item.updateTime ?? '/' }}</p>
                                    </div>
                                </el-col>
                                <el-col :span="8">
                                    <div class="item-view">
                                        <div class="text-center w-full">
                                            <el-button
                                                v-if="item.type == 1"
                                                icon="edit-pen"
                                                text
                                                type="primary"
                                                @click="handleEditMateriel(item)"
                                            >
                                                编辑
                                            </el-button>
                                        </div>
                                    </div>
                                </el-col>
                            </el-row>
                        </div>
                    </div>

                    <el-empty v-else class="m-[50px]" description="没有找到课件数据哦~" />

                    <!-- 其他资料区域 -->
                    <div class="mb-[12px] mt-[16px] text-black font-medium bg-gray-100 rounded-[3px]">
                        <el-row>
                            <el-col :span="3">
                                <div class="item-view">
                                    <p class="line-clamp-1">是否开放给门店</p>
                                </div>
                            </el-col>
                            <el-col :span="5">
                                <div class="item-view">
                                    <p class="line-clamp-1">资料</p>
                                </div>
                            </el-col>
                            <el-col :span="4">
                                <div class="item-view">
                                    <p class="line-clamp-1">编辑人</p>
                                </div>
                            </el-col>
                            <el-col :span="4">
                                <div class="item-view">
                                    <p class="line-clamp-1">编辑时间</p>
                                </div>
                            </el-col>
                            <el-col :span="3">
                                <div class="item-view">
                                    <p class="line-clamp-1">是否允许下载</p>
                                </div>
                            </el-col>
                            <el-col :span="5">
                                <div class="item-view">
                                    <p class="line-clamp-1">操作</p>
                                </div>
                            </el-col>
                        </el-row>
                    </div>
                    <div v-if="otherData.length">
                        <div
                            class="mb-[12px] py-[6px] border border-solid border-gray-200 rounded-[3px] text-black"
                            v-for="(item, index) in otherData"
                            :key="'other-' + index"
                        >
                            <el-row>
                                <el-col :span="3">
                                    <div class="item-view">
                                        <el-switch
                                            v-model="item.open"
                                            :active-value="1"
                                            :inactive-value="0"
                                            @change="handleOpenChange(item)"
                                        />
                                    </div>
                                </el-col>
                                <el-col :span="5">
                                    <div class="item-view">
                                        <p class="line-clamp-1">
                                            <span v-if="item.required == 1" class="text-red-400 mr4">※</span>
                                            <span>{{ item.coursewareName }}</span>
                                        </p>
                                    </div>
                                </el-col>
                                <el-col :span="4">
                                    <div class="item-view">
                                        <p class="line-clamp-1">{{ item.updateBy ?? '/' }}</p>
                                    </div>
                                </el-col>
                                <el-col :span="4">
                                    <div class="item-view">
                                        <p class="line-clamp-1">{{ item.updateTime ?? '/' }}</p>
                                    </div>
                                </el-col>
                                <el-col :span="3">
                                    <div class="item-view">
                                        <el-switch
                                            v-if="item.dataTemplateId !== 6"
                                            v-model="item.download"
                                            :active-value="1"
                                            :inactive-value="0"
                                            @change="handleDownloadChange(item)"
                                        />
                                    </div>
                                </el-col>
                                <el-col :span="5">
                                    <div class="item-view">
                                        <div class="text-center w-full">
                                            <template v-if="item.type == 2">
                                                <!-- 推荐视频特殊处理 -->
                                                <template v-if="item.dataTemplateId === 5">
                                                    <div
                                                        v-if="getFilesList(item).length > 0"
                                                        class="w-full flex flex-col items-center justify-center gap-2"
                                                    >
                                                        <div class="w-full flex flex-col gap-1">
                                                            <div
                                                                v-for="(file, fileIndex) in getFilesList(item)"
                                                                :key="fileIndex"
                                                                class="flex items-center justify-between px-2 py-1 border border-gray-200 rounded"
                                                                :title="file.fileName || file.path"
                                                            >
                                                                <div
                                                                    class="flex items-center gap-[3px] text-gray-600 flex-1 overflow-hidden"
                                                                >
                                                                    <span class="line-clamp-1 overflow-hidden">
                                                                        {{ file.fileName || file.path }}
                                                                    </span>
                                                                    <span
                                                                        v-if="file.lecturerName"
                                                                        class="text-primary text-xs ml-1"
                                                                    >
                                                                        (主讲: {{ file.lecturerName }})
                                                                    </span>
                                                                </div>
                                                                <div class="flex items-center gap-2">
                                                                    <el-icon
                                                                        v-if="item.dataTemplateId !== 6"
                                                                        class="cursor-pointer text-primary ml-2"
                                                                        @click="downloadMaterielFile(file)"
                                                                    >
                                                                        <Download />
                                                                    </el-icon>
                                                                    <el-icon
                                                                        class="cursor-pointer text-red-400 ml-2"
                                                                        @click="handleDeleteSingleFile(item, fileIndex)"
                                                                    >
                                                                        <CircleClose />
                                                                    </el-icon>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <el-button
                                                            v-if="getFilesList(item).length < (item.quantity || 1)"
                                                            type="primary"
                                                            size="small"
                                                            icon="Plus"
                                                            @click="openVideoUploadDialog(item)"
                                                        >
                                                            上传推荐视频
                                                        </el-button>
                                                    </div>
                                                    <el-button
                                                        v-else
                                                        type="primary"
                                                        plain
                                                        icon="Upload"
                                                        @click="openVideoUploadDialog(item)"
                                                    >
                                                        上传推荐视频
                                                    </el-button>
                                                </template>

                                                <!-- 其他类型物料 -->
                                                <template v-else>
                                                    <div
                                                        v-if="getFilesList(item).length > 0"
                                                        class="w-full flex flex-col items-center justify-center gap-2"
                                                    >
                                                        <div class="w-full flex flex-col gap-1">
                                                            <div
                                                                v-for="(file, fileIndex) in getFilesList(item)"
                                                                :key="fileIndex"
                                                                class="flex items-center justify-between px-2 py-1 border border-gray-200 rounded"
                                                                :title="file.fileName || file.path"
                                                            >
                                                                <div
                                                                    class="flex items-center gap-[3px] text-gray-600 flex-1 overflow-hidden"
                                                                >
                                                                    <span class="line-clamp-1 overflow-hidden">
                                                                        {{ file.fileName || file.path }}
                                                                    </span>
                                                                    <span
                                                                        v-if="file.lecturerName"
                                                                        class="text-primary text-xs ml-1"
                                                                    >
                                                                        (主讲: {{ file.lecturerName }})
                                                                    </span>
                                                                </div>
                                                                <div class="flex items-center gap-2">
                                                                    <el-icon
                                                                        v-if="item.dataTemplateId !== 6"
                                                                        class="cursor-pointer text-primary ml-2"
                                                                        @click="downloadMaterielFile(file)"
                                                                    >
                                                                        <Download />
                                                                    </el-icon>
                                                                    <el-icon
                                                                        class="cursor-pointer text-red-400 ml-2"
                                                                        @click="handleDeleteSingleFile(item, fileIndex)"
                                                                    >
                                                                        <CircleClose />
                                                                    </el-icon>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <file-oss
                                                            v-if="getFilesList(item).length < (item.quantity || 1)"
                                                            moduleName="courseware"
                                                            serviceName="teaching"
                                                            :file-type="item.fileFormat.split(',')"
                                                            :file-size="parseInt(item.fileSize)"
                                                            :multiple="(item.quantity || 1) > 1"
                                                            :max-files="item.quantity || 1"
                                                            :existing-files="getFilesList(item)"
                                                            @success="
                                                                (val) => {
                                                                    handleUpdateSuccess(item, val);
                                                                }
                                                            "
                                                        />
                                                    </div>
                                                    <file-oss
                                                        v-else
                                                        moduleName="courseware"
                                                        serviceName="teaching"
                                                        :file-type="item.fileFormat.split(',')"
                                                        :file-size="parseInt(item.fileSize)"
                                                        :multiple="(item.quantity || 1) > 1"
                                                        :max-files="item.quantity || 1"
                                                        :existing-files="[]"
                                                        @success="
                                                            (val) => {
                                                                handleUpdateSuccess(item, val);
                                                            }
                                                        "
                                                    />
                                                </template>
                                            </template>
                                        </div>
                                    </div>
                                </el-col>
                            </el-row>
                        </div>
                    </div>

                    <el-empty v-else class="m-[50px]" description="没有找到其他资料哦~" />

                    <!-- 留白区域，确保底部有足够空间 -->
                    <div class="py-[20px]"></div>
                </div>
            </el-scrollbar>
        </div>
        <!-- 视频上传对话框 -->
        <el-dialog
            v-model="videoDialogVisible"
            :title="currentItem && currentItem.dataTemplateId === 5 ? '上传推荐视频' : '上传通课视频'"
            width="550px"
            :close-on-click-modal="false"
            destroy-on-close
        >
            <div v-loading="videoDialogLoading">
                <el-form ref="videoFormRef" :model="videoForm" :rules="videoFormRules" label-width="120px">
                    <el-form-item label="主讲老师" prop="lecturerId">
                        <fuzzy-search
                            ref="lecturerRef"
                            class="w-full"
                            placeholder="输入搜索主讲老师"
                            v-model="videoForm.lecturerId"
                            :requestUrl="getLecturerList"
                            request-name="query"
                            data-label="lecturerName"
                            clearable
                            @change="handleLecturerSelect"
                        ></fuzzy-search>
                    </el-form-item>
                    <el-form-item label="上传文件" prop="file">
                        <div v-if="videoForm.file">
                            <div class="flex items-center gap-2">
                                <span class="text-gray-600">{{ videoForm.fileName }}</span>
                                <el-button type="danger" icon="Delete" circle @click="videoForm.file = null" />
                            </div>
                        </div>
                        <el-upload
                            v-else
                            class="upload-demo"
                            action=""
                            :auto-upload="false"
                            :on-change="handleVideoFileChange"
                            accept=".mp4"
                            :limit="1"
                        >
                            <el-button type="primary">选择文件</el-button>
                            <template #tip>
                                <div class="el-upload__tip">
                                    只能上传mp4文件，且不超过{{ currentItem ? parseInt(currentItem.fileSize) : 2000 }}MB
                                </div>
                            </template>
                        </el-upload>
                    </el-form-item>
                </el-form>
            </div>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="videoDialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="handleVideoUploadSubmit" :loading="videoDialogLoading">
                        {{ currentItem && currentItem.dataTemplateId === 5 ? '确认上传推荐视频' : '确认上传通课视频' }}
                    </el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import { ElMessageBox, ElMessage, ElNotification } from 'element-plus';
import { useMessage, useMessageBox } from '/@/hooks/message';
import NaviBar from '/@/components/NaviBar/index.vue';
import FileOss from '/@/components/Upload/FileOss.vue';
import FuzzySearch from '/@/components/EduComponents/fuzzySearch.vue';
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue';
import {
    fetchCoursewareDetail,
    editCourseware,
    fetchWareMaterielList,
    wareMaterielAdd,
    wareMaterielEdit,
    publishCourseware,
    updateRecordTaskStatus,
} from '/@/api/integraEdu/courseware';
import { getLecturerList } from '/@/api/eduConnect/ssQuery';
import { getUpdateSign } from '/@/api/common/index';
import OSS from 'ali-oss';
import type { UploadFile } from 'element-plus';

const route = useRoute();
const router = useRouter();
const wareLoading = ref(false);
const wareNameEdit = ref(false);
const wareData = reactive({
    coursewareName: '',
    nameTemp: '',
});
const loading = ref(false);
const coursewareData = ref<any[]>([]);
const otherData = ref<any[]>([]);
const canPublish = computed(
    () =>
        coursewareData.value.length &&
        coursewareData.value.every((item) => item.required == 0 || (item.required == 1 && item.canPublish == 1))
);
const publishLoading = ref(false);
const dataInitialized = ref(false);

// 新增通课视频相关变量
const videoDialogVisible = ref(false);
const videoDialogLoading = ref(false);
const currentItem = ref<any>(null);
const videoFormRef = ref<any>(null);
const lecturerRef = ref<any>(null);
const videoForm = reactive({
    lecturerId: '', // 主讲老师ID
    lecturerName: '', // 主讲老师名称
    file: null as File | null,
    fileName: '',
});
const videoFormRules = {
    lecturerId: [{ required: true, message: '请选择主讲老师', trigger: 'change' }],
    file: [{ required: true, message: '请上传视频文件', trigger: 'change' }],
};

// 保存资料数据的公共方法
const saveMaterielData = async (item: any, data: any) => {
    try {
        const api = item.id ? wareMaterielEdit : wareMaterielAdd;
        await api({
            id: item.id,
            coursewareId: item.coursewareId,
            dataTemplateId: item.dataTemplateId,
            ...data,
        });
        return true;
    } catch (err: any) {
        useMessage().error(err.msg || '保存失败');
        return false;
    }
};

// 解析details字段
const parseDetails = (item: any) => {
    if (!item.details) return null;

    try {
        const parsed = JSON.parse(item.details);

        // 处理单个对象的情况，转换为数组格式
        if (!Array.isArray(parsed) && typeof parsed === 'object' && parsed.url) {
            return [parsed];
        }

        // 添加检测循环引用的保护
        if (!Array.isArray(parsed) && typeof parsed === 'object' && !parsed.path && !parsed.fileName && !parsed.url) {
            console.warn('解析details获取到不合法的对象:', parsed);
            return [];
        }

        return parsed;
    } catch (e) {
        console.error('解析details失败', e);
        return null;
    }
};

// 更新item的details字段
const updateItemDetails = (item: any, newFile: any) => {
    const details = parseDetails(item);

    let filesArray = details || [];

    // 确保filesArray是数组
    if (!Array.isArray(filesArray)) {
        filesArray = [];
    }

    // 添加新文件
    filesArray.push(newFile);

    // 更新details
    return JSON.stringify(filesArray);
};

// 下载物料文件
const downloadMaterielFile = (file: any) => {
    if (!file) {
        ElMessage.warning('没有可下载的文件');
        return;
    }

    // 获取文件URL，根据不同的数据格式处理
    const fileUrl = file.url || '';
    if (!fileUrl) {
        ElMessage.warning('文件链接不存在');
        return;
    }

    try {
        // 使用fetch获取文件内容并强制下载
        ElMessage.success('开始下载文件...');

        fetch(fileUrl)
            .then((response) => {
                if (!response.ok) {
                    throw new Error(`下载失败: ${response.status} ${response.statusText}`);
                }
                return response.blob();
            })
            .then((blob) => {
                // 创建blob链接
                const blobUrl = window.URL.createObjectURL(blob);

                // 获取文件名
                const fileName = file.fileName || '未命名文件';

                // 创建隐藏的a标签实现下载
                const link = document.createElement('a');
                link.href = blobUrl;
                link.download = fileName;
                link.style.display = 'none';
                document.body.appendChild(link);
                link.click();

                // 清理
                setTimeout(() => {
                    document.body.removeChild(link);
                    window.URL.revokeObjectURL(blobUrl);
                }, 200);
            })
            .catch((err) => {
                console.error('下载文件时出错:', err);
                ElMessage.error('下载失败，请稍后重试');
            });
    } catch (error) {
        console.error('下载文件时出错:', error);
        ElMessage.error('下载失败，请稍后重试');
    }
};

onMounted(() => {
    loadCoursewareDetail();
    loadWareMaterielList();
});

const loadCoursewareDetail = async () => {
    wareLoading.value = true;
    try {
        const res = await fetchCoursewareDetail(route.query.coursewareId as string);
        wareData.coursewareName = res.data.coursewareName || '';
        wareData.nameTemp = wareData.coursewareName;
    } catch (err: any) {
        useMessage().error(err.msg);
    } finally {
        wareLoading.value = false;
    }
};

const loadWareMaterielList = async () => {
    // 防止重复加载
    if (loading.value) return;

    loading.value = true;
    try {
        const res = await fetchWareMaterielList({ coursewareId: route.query.coursewareId as string });
        // 处理新的数据结构
        coursewareData.value = res.data?.courseware || [];
        otherData.value = res.data?.other || [];
        setTimeout(() => {
            dataInitialized.value = true;
        }, 100);
    } catch (err: any) {
        useMessage().error(err.msg);
    } finally {
        loading.value = false;
    }
};

const handleOpenChange = async (item: any) => {
    if (!dataInitialized.value) return;

    try {
        const success = await saveMaterielData(item, { open: item.open });
        if (success) {
            useMessage().success('修改门店开放权限成功');
        }
    } catch (err: any) {
        // 恢复原值
        item.open = item.open ? 0 : 1;
        useMessage().error(err.msg);
    }
};

const handleDownloadChange = async (item: any) => {
    if (!dataInitialized.value) return;

    try {
        const success = await saveMaterielData(item, { download: item.download });
        if (success) {
            useMessage().success('修改下载权限成功');
        }
    } catch (err: any) {
        // 恢复原值
        item.download = item.download ? 0 : 1;
        useMessage().error(err.msg);
    }
};

const handleEditWareName = async () => {
    wareNameEdit.value = false;
    wareLoading.value = true;
    try {
        await editCourseware({ id: route.query.coursewareId as string, coursewareName: wareData.nameTemp });
        wareData.coursewareName = wareData.nameTemp;
        useMessage().success('修改成功');
    } catch (err: any) {
        wareData.nameTemp = wareData.coursewareName;
        useMessage().error(err.msg);
    } finally {
        wareLoading.value = false;
    }
};
const handleEditWareNameCancel = () => {
    wareNameEdit.value = false;
    wareData.nameTemp = wareData.coursewareName;
};

// 发布课件
const handlePublish = async () => {
    try {
        await useMessageBox().confirm('您确定要发布此课件包吗？');
    } catch {
        return;
    }

    publishLoading.value = true;
    try {
        const publishResult = await publishCourseware(route.query.coursewareId as string);

        // 判断是否为首次发布
        const isFirstPublish = publishResult.data?.isFirstPublish === 1;

        // 非首次发布才弹出推送确认框
        if (!isFirstPublish) {
            try {
                await ElMessageBox.confirm('是否推送新的录课任务？', '正在发布新的课件', {
                    confirmButtonText: '推送录课任务',
                    cancelButtonText: '不推送',
                    type: 'info',
                });
                await updateRecordTaskStatus({
                    id: route.query.coursewareId as string,
                    version: publishResult.data.version,
                    genRecordTask: 1,
                });
            } catch (err) {
                await updateRecordTaskStatus({
                    id: route.query.coursewareId as string,
                    version: publishResult.data.version,
                    genRecordTask: 0,
                });
            }
        } else {
            // 首次发布时直接设置为不推送
            await updateRecordTaskStatus({
                id: route.query.coursewareId as string,
                version: publishResult.data.version,
                genRecordTask: 0,
            });
        }

        loadWareMaterielList();
        useMessage().success('发布成功');
    } catch (err: any) {
        console.error('发布课件失败:', err);
        useMessage().error(err.msg || '发布失败');
    } finally {
        publishLoading.value = false;
    }
};

const handleEditMateriel = async (item: any) => {
    try {
        let val = item;
        if (!item.id) {
            const res = await wareMaterielAdd({ coursewareId: item.coursewareId, dataTemplateId: item.dataTemplateId });
            val = res.data;
        }
        router.push({
            path: '/eduConnect/courseZone/courseware/contentEdit',
            query: {
                bookId: route.query.bookId,
                coursewareId: route.query.coursewareId,
                materielId: val.id,
                dataTemplateId: val.dataTemplateId,
                coursewareName: wareData.coursewareName,
                materielName: val.coursewareName || val.templateName,
                canPublish: val.canPublish,
                from: encodeURIComponent(router.currentRoute.value.fullPath),
            },
        });
    } catch (err: any) {
        useMessage().error(err.msg);
    }
};

const handleDeleteMateriel = async (item: any) => {
    try {
        const success = await saveMaterielData(item, {
            path: '',
            fileName: '',
            details: '',
        });

        if (success) {
            item.url = '';
            item.fileName = '';
            item.canPublish = 0;
        }
        await loadWareMaterielList();
    } catch (err: any) {
        useMessage().error(err.msg);
        await loadWareMaterielList();
    }
};

const handleUpdateSuccess = async (item: any, val: any) => {
    // 只有推荐视频使用对话框上传，通课视频使用FileOss
    if (item.dataTemplateId === 5) {
        openVideoUploadDialog(item);
        return;
    }

    // 构建文件数据
    const fileData = {
        path: val.relativePath,
        fileName: val.fileName,
    };

    // 支持多文件上传
    let newDetails;
    if (item.quantity > 1) {
        newDetails = updateItemDetails(item, fileData);
    } else {
        // 单文件上传
        newDetails = JSON.stringify({ path: val.relativePath, fileName: val.fileName });
    }

    // 先创建副本，避免直接修改响应式对象引起循环
    const updatedData = {
        path: val.relativePath,
        fileName: val.fileName,
        details: newDetails,
    };

    try {
        // 先保存到服务端
        const success = await saveMaterielData(item, updatedData);

        if (success) {
            // 保存成功后才更新本地对象
            item.url = val.fullPath;
            item.fileName = val.fileName;
            item.details = newDetails;

            // 无论是单个还是多个文件上传，都刷新列表
            await loadWareMaterielList();

            // 提示上传成功
            ElMessage.success('文件上传成功');
        }
    } catch (err: any) {
        useMessage().error(err.msg);
    }
};

// 获取文件列表
const getFilesList = (item: any) => {
    const details = parseDetails(item);
    if (!details) return [];

    // parseDetails已处理为数组格式
    return details;
};

// 处理主讲老师选择
const handleLecturerSelect = (item: any) => {
    if (item) {
        videoForm.lecturerName = item.lecturerName || '';
    } else {
        videoForm.lecturerName = '';
    }
};

// 打开视频上传对话框
const openVideoUploadDialog = (item: any) => {
    // 重置表单数据
    videoForm.lecturerId = '';
    videoForm.lecturerName = '';
    videoForm.file = null;
    videoForm.fileName = '';
    // 保存当前项目引用
    currentItem.value = item;
    videoDialogVisible.value = true;

    // 确保表单验证状态重置
    setTimeout(() => {
        if (videoFormRef.value) {
            videoFormRef.value.resetFields();
        }
    }, 0);
};

// 处理视频文件选择
const handleVideoFileChange = (uploadFile: UploadFile) => {
    const file = uploadFile.raw;
    if (!file) return;

    // 检查文件类型
    if (!file.type.includes('video/mp4')) {
        ElMessage.error('只能上传MP4视频文件！');
        return;
    }

    // 检查文件大小
    const maxSize = currentItem.value ? parseInt(currentItem.value.fileSize) : 2000;
    if (file.size / 1024 / 1024 > maxSize) {
        ElMessage.error(`文件大小不能超过${maxSize}MB`);
        return;
    }

    videoForm.file = file;
    videoForm.fileName = file.name;
};

// 上传视频文件到OSS
const uploadVideoToOSS = async () => {
    if (!videoForm.file || !currentItem.value) return null;

    try {
        // 获取文件扩展名
        const fileName = videoForm.file.name.split('.');
        const fileExt = fileName[fileName.length - 1].toLowerCase();

        // 检查文件类型
        if (fileExt !== 'mp4') {
            ElNotification({
                title: '温馨提示',
                message: '只能上传MP4格式的视频文件！',
                type: 'warning',
            });
            return null;
        }

        // 检查文件大小
        const maxSize = currentItem.value ? parseInt(currentItem.value.fileSize) : 2000;
        if (videoForm.file.size / 1024 / 1024 > maxSize) {
            ElNotification({
                title: '温馨提示',
                message: `文件大小不能超过${maxSize}MB！`,
                type: 'warning',
            });
            return null;
        }

        // 获取上传凭证
        const res = await getUpdateSign('teaching', 'courseware', fileExt);
        if (res.data == null) {
            ElNotification({
                title: '温馨提示',
                message: '获取上传凭证失败！',
                type: 'error',
            });
            return null;
        }

        // 创建OSS客户端
        const client = new OSS({
            bucket: res.data.bucket,
            region: 'oss-' + res.data.endpoint,
            accessKeyId: res.data.accessKeyId,
            accessKeySecret: res.data.accessKeySecret,
            stsToken: res.data.securityToken,
        });

        // 上传文件
        const result = await client.put(res.data.uploadPath, videoForm.file);

        // 返回上传结果
        return {
            fileName: videoForm.file.name,
            relativePath: result.name,
            fullPath: result.url,
        };
    } catch (error) {
        console.error('上传文件失败:', error);
        ElNotification({
            title: '温馨提示',
            message: '文件上传失败，请重新上传！',
            type: 'error',
        });
        return null;
    }
};

// 处理删除单个文件
const handleDeleteSingleFile = async (item: any, fileIndex: number) => {
    try {
        // 获取文件列表
        const filesList = getFilesList(item);
        if (filesList.length === 0) return;

        // 如果只有一个文件，则调用原有的删除方法
        if (filesList.length === 1) {
            await handleDeleteMateriel(item);
            return;
        }

        // 删除指定索引的文件
        filesList.splice(fileIndex, 1);

        // 更新details字段
        const newDetails = JSON.stringify(filesList);

        // 如果还有文件，更新最新的文件信息到url和fileName
        let updatedPath = '';
        let updatedFileName = '';

        if (filesList.length > 0) {
            const lastFile = filesList[filesList.length - 1];
            updatedPath = lastFile.path || '';
            updatedFileName = lastFile.fileName || '';
        }

        // 创建更新数据对象，避免直接修改响应式对象
        const updateData = {
            details: newDetails,
            fileName: updatedFileName,
            path: updatedPath,
        };

        // 调用API更新
        const success = await saveMaterielData(item, updateData);

        if (success) {
            // 更新成功后才修改本地对象
            item.url = filesList.length > 0 ? filesList[filesList.length - 1].url || '' : '';
            item.fileName = updatedFileName;
            item.path = updatedPath;
            item.details = newDetails;

            // 如果没有文件了，将canPublish设为0
            if (filesList.length === 0) {
                item.canPublish = 0;
            }

            useMessage().success('删除成功');
        }
    } catch (err: any) {
        useMessage().error(err.msg || '删除失败');
        // 刷新数据
        await loadWareMaterielList();
    }
};

// 提交视频上传
const handleVideoUploadSubmit = async () => {
    if (!videoFormRef.value) return;

    try {
        await videoFormRef.value.validate();
    } catch (error) {
        return;
    }

    if (!currentItem.value || !videoForm.file) {
        useMessage().error('上传数据不完整');
        return;
    }

    videoDialogLoading.value = true;

    try {
        // 上传文件到OSS
        const uploadResult = await uploadVideoToOSS();
        if (!uploadResult) {
            videoDialogLoading.value = false;
            return;
        }

        // 构建包含教师信息的文件数据
        const fileData = {
            path: uploadResult.relativePath,
            fileName: `[${videoForm.lecturerName}]-${uploadResult.fileName}`,
            url: uploadResult.fullPath, // 添加url字段，确保在details中包含完整信息
        };

        // 更新details字段
        const newDetails = updateItemDetails(currentItem.value, fileData);

        // 创建更新数据对象
        const updateData = {
            details: newDetails,
            // 同时更新path和fileName，保持一致性
            path: uploadResult.relativePath,
            fileName: `[${videoForm.lecturerName}]-${uploadResult.fileName}`,
        };

        // 更新数据
        const success = await saveMaterielData(currentItem.value, updateData);

        if (success) {
            // 视频上传完成
            ElNotification({
                title: '温馨提示',
                message:
                    currentItem.value && currentItem.value.dataTemplateId === 5
                        ? '推荐视频上传成功！'
                        : '通课视频上传成功！',
                type: 'success',
            });

            videoDialogVisible.value = false;

            // 清理数据后再刷新
            currentItem.value = null;
            videoForm.file = null;

            // 重新加载数据，避免直接操作响应式对象
            await loadWareMaterielList();
        }
    } catch (err: any) {
        ElNotification({
            title: '温馨提示',
            message: err.msg || '视频上传失败！',
            type: 'error',
        });
    } finally {
        videoDialogLoading.value = false;
    }
};

// 组件卸载时清理资源
onUnmounted(() => {
    // 清理引用，防止内存泄漏
    currentItem.value = null;
    videoForm.file = null;
});
</script>

<style lang="scss" scoped>
.item-view {
    @apply w-full h-full flex items-center justify-center py-[8px] px-[10px] overflow-hidden;
}

.content-scrollbar {
    @apply overflow-auto;
}

.content-container {
    @apply pb-[20px];
}

.upload-demo {
    width: 100%;
}
</style>
