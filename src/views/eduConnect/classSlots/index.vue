<template>
    <div class="layout-padding">
        <div class="layout-padding-auto layout-padding-view">
            <el-table
                :data="state"
                border
                :cell-style="tableStyle.cellStyle"
                :header-cell-style="tableStyle.headerCellStyle"
            >
                <el-table-column type="index" label="序号" width="60" />
                <el-table-column prop="name" label="时段" />
                <el-table-column prop="correspondingTime" label="对应时间" />
                <el-table-column prop="updateBy" label="操作人" show-overflow-tooltip>
                    <template #default="scope"> {{ scope.row.updateBy ?? '/' }} </template>
                </el-table-column>
                <el-table-column prop="updateTime" label="操作时间" />
            </el-table>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { useTable } from '/@/hooks/table';
import { ref } from 'vue';
import { useMessage } from '/@/hooks/message';
import { fetchList } from '/@/api/eduConnect/timePeriod';

let state = ref([]);

//@ts-ignore
const { tableStyle } = useTable(state);

//查看
const getDataList = async () => {
    try {
        let res = await fetchList();
        state.value = res.data;
    } catch (error) {
        useMessage().error(error as string);
    }
};
onMounted(() => {
    getDataList();
});
</script>
