// 存储系统相关类型定义

// 菜单树查询参数
export interface MenuTreeQueryParams {
    parentId?: number; // 父节点ID
    menuName?: string; // 菜单名称
    type?: number; // 菜单类型
    app_type?: 'store_services' | 'mp_course'; // 应用类型
}

// 菜单实体类型
export interface StoreMenu {
    menuId?: number; // 菜单ID
    name: string; // 菜单名称
    permission?: string; // 菜单权限标识
    parentId: number; // 父菜单ID
    icon?: string; // 图标
    path?: string; // 前端路由标识路径
    component?: string; // 前端组件
    visible?: string; // 菜单显示隐藏控制
    sortOrder?: number; // 排序值
    menuType: string; // 菜单类型 （0菜单 1按钮）
    keepAlive?: string; // 路由缓冲
    embedded?: string;
    createBy?: string; // 创建人
    updateBy?: string; // 修改人
    createTime?: string; // 创建时间
    updateTime?: string; // 更新时间
    delFlag?: number; // 0--正常 1--删除
    children?: StoreMenu[]; // 子菜单
}

// 菜单详情查询参数
export interface MenuDetailsParams {
    menuId?: string; // 菜单ID
    name?: string; // 菜单名称
    permission?: string; // 菜单权限标识
    parentId?: number; // 父菜单ID
    icon?: string; // 图标
    path?: string; // 前端路由标识路径
    component?: string; // 前端组件
    visible?: string; // 菜单显示隐藏控制
    sortOrder?: string; // 排序值
    menuType?: number; // 菜单类型 （0菜单 1按钮）
    keepAlive?: string; // 路由缓冲
    embedded?: string;
    createBy?: string; // 创建人
    updateBy?: string; // 修改人
    createTime?: string; // 创建时间
    updateTime?: string; // 更新时间
    delFlag?: boolean; // 0--正常 1--删除
}

// 菜单创建/更新请求体
export interface MenuRequestBody {
    menuId?: number; // 菜单ID
    name: string; // 菜单名称
    permission?: string; // 菜单权限标识
    parentId: number; // 父菜单ID
    icon?: string; // 图标
    path?: string; // 前端路由标识路径
    component?: string; // 前端组件
    visible?: string; // 菜单显示隐藏控制
    sortOrder?: number; // 排序值
    menuType: string; // 菜单类型 （0菜单 1按钮）
    keepAlive?: string; // 路由缓冲
    embedded?: string;
    createBy?: string; // 创建人
    updateBy?: string; // 修改人
    createTime?: string; // 创建时间
    updateTime?: string; // 更新时间
    delFlag?: number; // 0--正常 1--删除
}

// 通用响应数据类型
export interface ResponseData<T = any> {
    code?: number;
    errCode?: string;
    msg?: string;
    data?: T;
}

// 角色相关类型定义
export interface StoreRole {
    roleId?: number;
    roleName: string;
    roleKey: string;
    roleSort?: number;
    dataScope?: string;
    menuCheckStrictly?: boolean;
    deptCheckStrictly?: boolean;
    status: string;
    delFlag?: string;
    createBy?: string;
    createTime?: string;
    updateBy?: string;
    updateTime?: string;
    remark?: string;
    menuIds?: number[];
}

export interface RoleQueryParams {
    roleName?: string;
    roleKey?: string;
    status?: string;
    createTime?: string[];
}

export interface RoleRequestBody {
    roleId?: number;
    roleName: string;
    roleKey: string;
    roleSort?: number;
    dataScope?: string;
    menuCheckStrictly?: boolean;
    deptCheckStrictly?: boolean;
    status: string;
    delFlag?: string;
    createBy?: string;
    createTime?: string;
    updateBy?: string;
    updateTime?: string;
    remark?: string;
    menuIds?: number[];
}

export interface StoreMenuTree extends StoreMenu {
    children: StoreMenuTree[];
}

export interface CreateStoreMenuDto {
    name: string;
    permission: string;
    path?: string;
    component?: string;
    parent_id?: number;
    icon?: string;
    visible: '0' | '1';
    sort_order: number;
    menu_type: '0' | '1' | '2';
    app_type: 'store_services' | 'mp_course';
}

export interface UpdateStoreMenuDto extends Partial<CreateStoreMenuDto> {}

export interface StoreMenuQueryParams {
    app_type?: 'store_services' | 'mp_course';
    menu_type?: '0' | '1' | '2';
    parent_id?: number;
    visible?: '0' | '1';
    name?: string;
    permission?: string;
}

export interface RoleMenuPermission {
    role_id: number;
    menu_ids: number[];
    app_type: 'store_services' | 'mp_course';
}

export interface PageResult<T> {
    records: T;
    total: number;
    size: number;
    current: number;
    pages: number;
}

// 新增：门店角色实体类型
export interface StoreRoleEntity {
    roleId?: number;
    roleName: string;
    roleCode: string;
    roleDesc?: string;
    dsType: number;
    dsScope?: string; // 数据权限作用范围
    createBy?: string; // 创建人
    updateBy?: string; // 修改人
    createTime?: string; // 创建时间
    updateTime?: string; // 修改时间
    delFlag?: number; // 删除标识（0-正常,1-删除）
}

// 新增：角色查询参数类型
export interface StoreRoleQueryParams {
    roleId?: number;
    roleName?: string;
    roleCode?: number;
    roleDesc?: string;
    dsType?: number;
    dsScope?: string;
    createBy?: string;
    updateBy?: string;
    createTime?: string;
    updateTime?: string;
    delFlag?: boolean;
}

// 新增：角色列表查询参数类型

export interface StoreRoleListParams {
    current?: number;
    size?: number;
    roleName?: string;
    roleCode?: string;
}

// 新增：角色创建请求体类型
export interface StoreRoleCreateRequest {
    roleName: string;
    roleCode: string;
    roleDesc?: string;
    dsType: number;
    dsScope?: string;
    createBy?: string;
    updateBy?: string;
    createTime?: string;
    updateTime?: string;
    delFlag?: number;
}

// 新增：角色更新请求体类型
export interface StoreRoleUpdateRequest {
    roleId?: number;
    roleName: string;
    roleCode: string;
    roleDesc?: string;
    dsType: number;
    dsScope?: string;
    createBy?: string;
    updateBy?: string;
    createTime?: string;
    updateTime?: string;
    delFlag?: number;
}

// 新增：角色删除请求体类型
export interface StoreRoleDeleteRequest {
    ids: number[];
}

// 新增：角色菜单更新请求体类型
export interface RoleMenuUpdateRequest {
    roleId?: number; // 角色id
    menuIds?: string; // 菜单列表
}

// 新增：通过角色ID查询角色列表请求体类型
export interface RoleListByIdsRequest {
    roleIds: string[]; // 角色id数组
}
