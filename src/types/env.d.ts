/// <reference types="vite/client" />

interface ImportMetaEnv {
    // 基础信息
    readonly VITE_GLOBAL_TITLE: string;
    readonly VITE_FOOTER_TITLE: string;

    // 环境与架构
    readonly ENV: 'development' | 'test' | 'production';
    readonly VITE_IS_MICRO: string;
    readonly VITE_PUBLIC_PATH: string;
    readonly VITE_API_URL: string;
    readonly VITE_LIVE_SDK_URL: string;

    // 服务配置
    readonly VITE_PORT: string;
    readonly VITE_OPEN: string;
    readonly VITE_ADMIN_PROXY_PATH: string;
    readonly VITE_GEN_PROXY_PATH: string;

    // 安全配置
    readonly VITE_PWD_ENC_KEY: string;
    readonly VITE_OAUTH2_PASSWORD_CLIENT: string;
    readonly VITE_OAUTH2_MOBILE_CLIENT: string;
    readonly VITE_OAUTH2_SOCIAL_CLIENT: string;

    // 功能开关
    readonly VITE_VERIFY_ENABLE: string;
    readonly VITE_VERIFY_IMAGE_ENABLE: string;
    readonly VITE_WEBSOCKET_ENABLE: string;
    readonly VITE_REGISTER_ENABLE: string;
    readonly VITE_AUTO_TENANT: string;

    // 资源配置
    readonly VITE_APP_IMAGE_OSSURL: string;
    readonly VITE_ALI_KEY: string;
    readonly VITE_LICENSE_DOMAIN: string;
}

interface ImportMeta {
    readonly env: ImportMetaEnv;
}
