<template>
    <el-dialog title="教室空闲时间" v-model="visible" :close-on-click-modal="false" draggable width="70%">
        <el-row class="ml10">
            <el-form :inline="true" :model="state.queryForm" ref="queryRef">
                <el-form-item label="教室名称">
                    <el-input
                        v-model="state.queryForm.classRoomName"
                        style="width: 240px"
                        placeholder="请输入教室名称"
                    />
                </el-form-item>
                <el-form-item label="日期">
                    <el-select v-model="modelTimeType" @change="changeClassTime">
                        <el-option label="本周" :value="FreeTimeFilterDateType.week" />
                        <el-option label="下周" :value="FreeTimeFilterDateType.nextWeek" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button @click="getData" icon="search" type="primary">
                        {{ $t('common.queryBtn') }}
                    </el-button>
                    <el-button @click="resetQuery" icon="Refresh">{{ $t('common.resetBtn') }} </el-button>
                </el-form-item>
            </el-form>
        </el-row>
        <el-table
            :data="state?.dataList && state.dataList[0] && state.dataList[0]?.classRooms"
            v-loading="state.loading"
            border
            :cell-style="tableStyle.cellStyle"
            ref="singleTableRef"
            :header-cell-style="tableStyle.headerCellStyle"
            @current-change="handleCurrentChange"
            highlight-current-row
        >
            <!-- <el-table-column type="index" width="50" /> -->
            <el-table-column prop="classRoomName" label="上课教室" show-overflow-tooltip width="160" />
            <el-table-column :prop="'week' + item" v-for="item in 7" :key="item" show-overflow-tooltip width="160">
                <template #header="{ column }">
                    {{ setDateHeader(column.property) }}
                </template>
                <template #default="{ row, column }">
                    <p v-if="row[column.property].morning">
                        <span style="flex-shrink: 0">上午：</span>
                        <span>
                            <p v-for="item in row[column.property].morning?.classTimes" :key="item.id">
                                {{ item.classTimePeriod }}
                            </p>
                        </span>
                    </p>
                    <p v-if="row[column.property].afternoon">
                        <span style="flex-shrink: 0">下午：</span>
                        <span>
                            <p v-for="item in row[column.property].afternoon?.classTimes" :key="item.id">
                                {{ item.classTimePeriod }}
                            </p>
                        </span>
                    </p>
                    <p v-if="row[column.property].evening">
                        <span style="flex-shrink: 0">晚上：</span>
                        <span>
                            <p v-for="item in row[column.property].evening?.classTimes" :key="item.id">
                                {{ item.classTimePeriod }}
                            </p>
                        </span>
                    </p>
                    <p v-else>/</p>
                </template>
            </el-table-column>
        </el-table>
        <!-- <pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" v-bind="state.pagination" /> -->
        <!-- <template #footer>
            <span class="dialog-footer">
                <el-button @click="visible = false">取消</el-button>
                <el-button type="primary" @click="onSubmit" :disabled="!currentRow">确认</el-button>
            </span>
        </template> -->
    </el-dialog>
</template>

<script lang="ts" setup>
import moment from 'moment';
import { FreeTimeFilterDateType } from '../enums/eduEnums';
import { weeks } from '../types/classTimeType';
import { classRoomFreeTimeCalendar } from '/@/api/eduConnect/ssQuery';
import { BasicTableProps, useTable } from '/@/hooks/table';
// import fuzzySearch from '../fuzzySearch.vue'
// import { getLecturerList } from '/@/api/eduConnect/ssQuery';
import { ElTable } from 'element-plus';
const visible = ref(false);
const state: BasicTableProps = reactive<BasicTableProps>({
    queryForm: {},
    createdIsNeed: false,
    pageList: classRoomFreeTimeCalendar,
});
const dateFileterDate = ref(FreeTimeFilterDateType.week);
const modelTimeType = ref(FreeTimeFilterDateType.week);
const singleTableRef = ref<InstanceType<typeof ElTable>>();
const queryRef = ref();
const lecturerId = ref();
//单选
const currentRow = ref();
const {
    getDataList,
    // currentChangeHandle,
    // sizeChangeHandle,
    tableStyle,
    getDataListReturnStatus,
} = useTable(state);
// 打开弹窗
const openDialog = (id?: string) => {
    visible.value = true;
    lecturerId.value = id;
    getData();
};
const getData = async () => {
    changeClassTime(unref(modelTimeType));
    await getDataListReturnStatus();
    // if (unref(lecturerId) && state.dataList) {
    //     let row = state.dataList.find(item => item.lecturerId === unref(lecturerId)) || {}
    //     singleTableRef.value!.setCurrentRow(row)
    // }
    dateFileterDate.value = unref(modelTimeType);
};
// 单选事件
const handleCurrentChange = (val: any) => {
    currentRow.value = val;
};
const changeClassTime = (e: FreeTimeFilterDateType) => {
    state.queryForm.selectAttendClassStartTime =
        (e === FreeTimeFilterDateType.week
            ? moment().day(1).format('YYYY-MM-DD')
            : moment().add(1, 'weeks').day(1).format('YYYY-MM-DD')) + ' 00:00:00';
    state.queryForm.selectAttendClassEndTime =
        (e === FreeTimeFilterDateType.week
            ? moment().day(7).format('YYYY-MM-DD')
            : moment().add(1, 'weeks').day(7).format('YYYY-MM-DD')) + ' 23:59:59';
};
const setDateHeader = (name: 'week1' | 'week2' | 'week3' | 'week4' | 'week5' | 'week6' | 'week7') => {
    let weekId = Number(name.replace('week', ''));
    return `${
        unref(dateFileterDate) === FreeTimeFilterDateType.week
            ? moment().day(weekId).format('YYYY-MM-DD')
            : moment().day(weekId).add(1, 'weeks').format('YYYY-MM-DD')
    }(${weeks.find((item) => item.id === weekId)?.weekname})`;
};
// 清空搜索条件
const resetQuery = () => {
    // 清空搜索条件
    queryRef.value?.resetFields();
    modelTimeType.value = FreeTimeFilterDateType.week;
    dateFileterDate.value = unref(modelTimeType);
    changeClassTime(unref(modelTimeType));
    state.queryForm = {};
    getDataList();
};
// 暴露变量
defineExpose({
    openDialog,
});
</script>

<style lang="scss" scoped></style>
