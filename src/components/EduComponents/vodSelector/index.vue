<template>
    <div class="w-full max-w-120">
        <el-button type="primary" :disabled="props.disabled" @click="selectVod">选择视频课程</el-button>
        <div v-if="detail" class="w-full px-3 mt-2 border-2 border-solid h-33 video_box_w border-stone-200">
            <div class="flex items-center justify-between min-h-33">
                <div class="relative mr-4 cursor-pointer shrink-0 h-27" @click.stop="handlePreview(detail)">
                    <img :src="imageUrl + 'video_default.png'" alt="" class="max-w-full w-36" />
                    <div
                        class="absolute top-0 left-0 flex items-center justify-center w-full h-full bg-black bg-opacity-30"
                    >
                        <img :src="imageUrl + 'play.png'" alt="" class="w-10 h-10" />
                    </div>
                </div>
                <div class="flex flex-col justify-between flex-1 overflow-hidden leading-5 video_item_right">
                    <div class="m-0">阶段：{{ setGrade(detail.grade) }}</div>
                    <div class="m-0">录课书籍：{{ detail.booksName }}</div>
                    <div class="m-0">主讲师：{{ detail.lecturerName }}</div>
                    <div class="m-0">
                        <span>原上课时间段：</span>
                        <div>
                            <span>{{ detail.originalCourseStartDate }}</span> -
                            <span>{{ detail.originalCourseEndDate }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <vod-selector ref="vodSelectorRef" @selected="selected"></vod-selector>
    <player-dialog ref="playerDialogRef"></player-dialog>
</template>

<script lang="ts" setup>
import vodSelector from '../vodSelector.vue';
import { useDict } from '/@/hooks/dict';
import { ssRecording } from '/@/api/eduConnect/ssPoint';
import PlayerDialog from '/@/components/EduComponents/player/playerDialog.vue';
import { useMessage } from '/@/hooks/message';

const playerDialogRef = ref();
const detail = ref();
const props = defineProps({
    modelValue: {
        type: String,
    },
    disabled: {
        type: Boolean,
        default: false,
    },
});
watch(
    () => props.modelValue,
    (val) => {
        if (val) {
            getDetailData(val);
        } else {
            detail.value = null;
        }
    }
);
const imageUrl = import.meta.env.VITE_APP_IMAGE_OSSURL;
const vodSelectorRef = ref();
const { grade } = useDict('grade');
const emit = defineEmits(['update:modelValue']);
const getDetailData = (val: string) => {
    ssRecording(val).then((res) => {
        detail.value = res.data;
    });
};
const selectVod = () => {
    vodSelectorRef.value.open();
};
const setGrade = (value: string) => {
    return grade.value.find((item: any) => item.value === value + '')?.label || '/';
};
const selected = (value: any) => {
    detail.value = value;
    emit('update:modelValue', value.id);
};
// 视频预览

const handlePreview = (row: any) => {
    if (row?.recordingResources) {
        playerDialogRef.value?.openDialog(row.recordingResources);
    } else {
        useMessage().warning('无法预览,视频未上传');
    }
};
</script>

<style scoped>
.video_item_right p:nth-of-type(-n + 3) {
    @apply max-w-full whitespace-nowrap overflow-hidden text-ellipsis;
}
</style>
