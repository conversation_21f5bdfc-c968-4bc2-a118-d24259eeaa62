export enum ClassTimeMethod {
    weeklyTime = '0', //按周排课
    dayTime = '1', //按日历排课
}

export enum AttendClassType {
    liveType = 0, //直播课
    pointType = 1, //点博课
}

//设备类型
export enum DeviceType {
    speakerType = '1', //主讲端
    classroomType = '2', //教室端
}

//设备状态
export enum DeviceState {
    enable = '0',
    disable = '1',
}

export enum ClassTimeEditEnums {
    'EDIT',
    'ADD',
}

export enum RoomCodeEnums {
    lecturerRoomCode = 1,
    classRoomCode = 2,
}
export const LIVE_CLASS_NAME = '读书会';
export const POINT_CLASS_NAME = '点播课';

//修改标签类型
export const setTagType = (
    key: string,
    obj: { [x: string]: 'primary' | 'success' | 'warning' | 'danger' | 'info' | 'text' }
) => {
    return obj?.[key] || 'primary';
};

//空闲时间筛选日期类型
export enum FreeTimeFilterDateType {
    'week', //本周
    'nextWeek', //下周
}

//课程状态
export enum ClassStatus {
    underway = 0, //进行中
    waitStart = 1, //即将开始
    week = 2, //本周
    nobeginning = 3, //未开始
    end = 4, //已结束
}

// 班级状态
export enum FinishSchool {
    common = 0, //正常
    finish = 1, // 结业
}
// 精品、单  常规
export enum LessonType {
    // 单
    singles = 0,
    // 常规
    common = 1,
    // 精品
    fine = 2,
}
