<template>
    <el-button type="primary" icon="Plus" class="mb-2" @click="addClassTime">添加一条上课时间</el-button>
    <el-table
        :data="tableData"
        border
        style="width: 100%"
        :expand-row-keys="expandedRowKeys"
        :row-key="(row: TableData) => row.id"
        @expand-change="expandChange"
    >
        <el-table-column type="expand" width="45">
            <template #default="{ row }">
                <div class="pl-10" v-if="!row.isEdit">
                    <!-- <p>上课时间: {{ row.tStartDate }} - {{ row.tEndDate }}</p>
                    <p>上课教室: {{ row.tClassRoom }}</p> -->
                    <el-table :data="row.dateList">
                        <el-table-column label="日期" prop="date" width="100" />
                        <el-table-column label="周" prop="weekName" width="100" />
                        <el-table-column label="关联书籍名" prop="booksName">
                            <template #header="{ column }">
                                <div>
                                    <span class="mr-2">{{ column.label }}</span>
                                    <el-tooltip effect="dark" content="打开批量设置" placement="top">
                                        <el-button
                                            icon="Setting"
                                            size="small"
                                            circle
                                            @click="row.showBatch = true"
                                            v-show="!row.showBatch"
                                        ></el-button>
                                    </el-tooltip>
                                    <span v-show="row.showBatch">
                                        <el-input
                                            style="width: 280px"
                                            v-model="row.batchName"
                                            maxlength="64"
                                            placeholder="批量设置"
                                            clearable
                                        />
                                        {{ row.batchName.length }}/64
                                        <el-tooltip effect="dark" content="批量设置书名" placement="top">
                                            <el-button icon="Finished" circle @click="batchBookName(row)"></el-button>
                                        </el-tooltip>
                                        <el-button icon="Close" circle @click="row.showBatch = false"></el-button>
                                    </span>
                                </div>
                            </template>
                            <template #default="cProps">
                                <el-input
                                    v-model="cProps.row.booksName"
                                    maxlength="64"
                                    style="width: 350px"
                                    placeholder="请输入"
                                />
                                {{ cProps.row.booksName.length }}/64
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
                <div class="pl-10 text-red-500" v-else>请先保存上课基础信息</div>
            </template>
        </el-table-column>
        <el-table-column prop="tWeek" label="星期" :width="attendClassType === AttendClassType.liveType ? 202 : null">
            <template #default="{ row }">
                <el-select v-model="row.tWeeks" :multiple="true" placeholder="请选择" class="w-44" v-if="row.isEdit">
                    <el-option
                        v-for="item in weeks"
                        :key="item.id"
                        :label="item.weekname"
                        :disabled="item.disabled"
                        :value="item.id"
                    ></el-option>
                </el-select>
                <div v-else>{{ showWeekName(row) }}</div>
            </template>
        </el-table-column>
        <el-table-column
            prop="tStartDate"
            label="时间段"
            :width="attendClassType === AttendClassType.liveType ? 220 : null"
        >
            <template #default="{ row }">
                <el-time-picker
                    v-model="row.dateValue"
                    format="HH:mm"
                    is-range
                    range-separator="至"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    style="width: 195px"
                    v-if="row.isEdit"
                />
                <div v-else>{{ row.tStartDate }} - {{ row.tEndDate }}</div>
            </template>
        </el-table-column>
        <el-table-column
            prop="tClassRoom"
            label="上课教室"
            width="170"
            v-if="attendClassType === AttendClassType.liveType"
        >
            <template #header="{ column }">
                <div>
                    <span>{{ column.label }}</span>
                    <el-tooltip class="box-item" effect="dark" content="查看教室空闲时间" placement="top-start">
                        <el-button size="small" circle icon="View" class="ml-2" @click="openRoomFreeTime"></el-button>
                    </el-tooltip>
                </div>
            </template>
            <template #default="{ row }">
                <el-select v-model="row.tClassRoom" placeholder="请选择" class="w-36" v-if="row.isEdit">
                    <el-option v-for="item in live_room" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <div v-else>{{ getClassRoomName(row) }}</div>
            </template>
        </el-table-column>
        <el-table-column prop="tAction" label="操作" :width="attendClassType === AttendClassType.liveType ? 120 : null">
            <template #default="{ row }">
                <el-tooltip effect="dark" content="保存" v-if="row.isEdit" placement="top">
                    <el-button type="success" icon="Check" @click="saveClassTime(row)" circle></el-button>
                </el-tooltip>
                <el-tooltip effect="dark" content="编辑" placement="top" v-else>
                    <el-button icon="Edit" @click="row.isEdit = true" circle></el-button>
                </el-tooltip>
                <el-popconfirm title="确认删除?" @confirm="deleteClassTime(row)">
                    <template #reference>
                        <el-button type="danger" icon="Delete" circle />
                    </template>
                </el-popconfirm>
            </template>
        </el-table-column>
    </el-table>
</template>

<script lang="ts" setup>
import moment from 'moment';
import { formatDate } from '/@/utils/formatTime';
import { useMessage } from '/@/hooks/message';
import { useDict } from '/@/hooks/dict';
import { TableData, ClassTime, weeks } from '../types/classTimeType';
import { AttendClassType } from '../enums/eduEnums';

const emit = defineEmits(['openRoomFreeTime']);
const props = defineProps({
    attendClassDate: {
        default: () => [],
        type: Array,
    },
    attendClassType: {
        type: Number,
        default: AttendClassType.liveType,
    },
});
watch(
    () => props.attendClassDate,
    () => {
        getWeekdaysBetweenDates();
    }
);
const { live_room } = useDict('live_room');
//周表格数据
const tableData = ref<TableData[]>([
    // { id: 1, tWeeks: [1], tStartDate: '09:38', tEndDate: '09:38', batchName: "", showBatch: false, dateValue: [new Date(), new Date()], tClassRoom: "", tAction: "", isEdit: false, dateList: [{ date: "2022-01-01", bookName: "书名1", weekName: "", showBatch: false }] },
    // { id: 2, tWeeks: [2], tStartDate: '', tEndDate: '', dateValue: undefined, tClassRoom: "102", tAction: "", isEdit: false, dateList: [{ date: "2022-01-02", bookName: "书名2" }] },
]);
//展开用
const expandedRowKeys = ref<number[]>([]);
const expandChange = (val: any, expandedRows: any) => {
    expandedRowKeys.value = expandedRows.map((item: TableData) => item.id);
};
//新增用
const addClassTime = () => {
    if (!props?.attendClassDate?.length) {
        return useMessage().warning('请先选择上课日期');
    }
    const id = unref(tableData).length + 1;
    tableData.value.push({
        tWeeks: [],
        tStartDate: '',
        tEndDate: '',
        tClassRoom: '',
        tAction: '',
        isEdit: true,
        dateList: [],
        dateValue: undefined,
        id: id,
        batchName: '',
        showBatch: false,
    });
};
const batchBookName = (row: TableData) => {
    row.dateList.forEach((item) => {
        item.booksName = row.batchName;
    });
};
//删除
const deleteClassTime = (row: TableData) => {
    tableData.value.splice(tableData.value.indexOf(row), 1);
};

//显示周
const showWeekName = (row?: TableData) => {
    return unref(weeks)
        .map((week) => {
            if (row?.tWeeks?.includes(week.id)) {
                return week.weekname;
            }
        })
        .filter((item) => item)
        .join('、');
};

//单个上课时间保存
const saveClassTime = (row: TableData) => {
    if (!row.tWeeks?.length) return useMessage().warning('请选择周信息');
    if (!row.dateValue?.length) return useMessage().warning('请选择上课时间段');
    if (!row.tClassRoom && props.attendClassType === AttendClassType.liveType)
        return useMessage().warning('请选择上课教室');
    if (Array.isArray(row.dateValue) && row.dateValue.length > 1) {
        row.tStartDate = formatDate(row.dateValue[0], 'HH:MM');
        row.tEndDate = formatDate(row.dateValue[1], 'HH:MM');
    }
    row.dateList = setSpecificTime(row.tWeeks);
    row.isEdit = false;
    expandedRowKeys.value = [row.id];
};

//根据日期判断包含周几的id集合
const getWeekdaysBetweenDates = () => {
    const classDateList = props.attendClassDate;
    if (!classDateList || classDateList.length == 0) {
        return false;
    }
    const startDate = moment(classDateList[0] as Date);
    const endDate = moment(classDateList[1] as Date);
    const weekdays: number[] = [];

    let currentDate = startDate.clone(); // 使用 clone 以避免修改原始开始日期
    while (currentDate.isBefore(endDate) || currentDate.isSame(endDate)) {
        const day = currentDate.day() == 0 ? 7 : currentDate.day();
        if (weekdays.indexOf(day) == -1) {
            weekdays.push(day); // 使用 'dddd' 格式化选项来获取完整的星期几名称
        }
        currentDate.add(1, 'days'); // 增加一天
    }
    weeks.forEach((week) => {
        week.disabled = !weekdays.includes(week.id);
    });
};
//根据周几获取设置具体时间
const setSpecificTime = (weeks: number[]): Array<ClassTime> => {
    const classDateList = props.attendClassDate;
    if (!classDateList || classDateList.length == 0) {
        return [];
    }
    const start = moment(classDateList[0] as Date);
    const end = moment(classDateList[1] as Date);
    const dateList: ClassTime[] = [];
    let currentDate = start.clone(); // 使用 clone 以避免修改原始开始日期
    while (currentDate.isSameOrBefore(end)) {
        const day = currentDate.day() == 0 ? 7 : currentDate.day();
        // start.day()
        if (day && weeks.indexOf(day) != -1) {
            dateList.push({
                date: currentDate.format('YYYY-MM-DD'),
                booksName: '',
                weekName: getWeekNameFromId(day),
            });
        }
        currentDate.add(1, 'day');
    }
    return dateList;
};
//根据id查周
const getWeekNameFromId = (id: number) => {
    // 查找匹配项
    const foundItem = weeks.find((item) => item.id === id);

    return foundItem?.weekname || '';
};
//清空所有数据
const clearAllSelectWeek = () => {
    tableData.value = [];
};
const getClassRoomName = (row: TableData) => {
    if (!live_room.value.length) {
        return row.tClassRoom;
    }
    if (!row.tClassRoom) return '/';
    return live_room.value.find((item: any) => item.value == row.tClassRoom).label;
};
const getData = () => {
    return tableData.value;
};
const openRoomFreeTime = () => {
    emit('openRoomFreeTime');
};
defineExpose({
    clearAllSelectWeek,
    getData,
});
</script>

<style lang="scss" scoped></style>
