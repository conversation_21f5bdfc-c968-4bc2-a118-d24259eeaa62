<template>
    <div class="relative w-full">
        <el-select
            class="z-10"
            v-model="selectDate"
            multiple
            filterable
            allow-create
            default-first-option
            :reserve-keyword="false"
            placeholder="请选择日期"
            popper-class="hidden"
            @focus="showPop = true"
            @remove-tag="changeSelect"
        >
        </el-select>
        <div class="calendarMultiple_container" v-show="showPop">
            <el-calendar ref="calendar">
                <template #header="{ date }">
                    <span>{{ date }}</span>
                    <el-button-group>
                        <el-button
                            v-for="item in dateBtns"
                            :key="item.value"
                            size="small"
                            @click="changeCalendar(item.value)"
                        >
                            {{ item.title }}
                        </el-button>
                    </el-button-group>
                    <div>
                        <el-tooltip effect="dark" content="清空" placement="top-start">
                            <el-popconfirm title="确定清空日期?" @confirm="clearDate">
                                <template #reference>
                                    <el-button type="danger" icon="Delete" circle />
                                </template>
                            </el-popconfirm>
                        </el-tooltip>

                        <el-tooltip effect="dark" content="关闭" placement="top-start" :hide-after="50">
                            <el-button icon="close" circle @click="showPop = false" />
                        </el-tooltip>
                    </div>
                </template>
                <template #date-cell="{ data }">
                    <p @click="dateClick(data.day)" class="w-full h-full">
                        {{ setDay(data) }}
                        {{ dateInArray(data.day) ? '✔️' : '' }}
                    </p>
                </template>
            </el-calendar>
        </div>
    </div>
</template>

<script lang="ts" setup>
import type { CalendarDateType, CalendarInstance } from 'element-plus';
import { ClassTime, weeks } from '../types/classTimeType';
const emit = defineEmits(['update:modelValue', 'update:attendClassInfo', 'selectDate']);

const props = defineProps({
    modelValue: {
        type: Array<string>,
        default: () => [],
    },
    attendClassInfo: {
        type: Array,
        default: () => [],
    },
});
watch(
    () => props.modelValue,
    (val) => {
        if (!val.length) {
            selectDate.value = [];
            selectDateInfo.value = [];
            changeCalendar('today');
        }
    }
);
const calendar = ref<CalendarInstance>();
const showPop = ref<boolean>(true);
const selectDate = ref<string[]>(props.modelValue);
const dateBtns = ref<{ title: string; value: CalendarDateType }[]>([
    { title: '上一年', value: 'prev-year' },
    { title: '上一月', value: 'prev-month' },
    { title: '今天', value: 'today' },
    { title: '下一月', value: 'next-month' },
    { title: '下一年', value: 'next-year' },
]);
const selectDateInfo = ref<ClassTime[]>([]);
const updateValue = () => {
    emit('update:modelValue', selectDate.value);
    emit('update:attendClassInfo', selectDateInfo.value);
}; //设置日期显示格式
const setDay = (data: any) => {
    return data.day.split('-').slice(2).join();
};
//切换月份
const changeCalendar = (val: CalendarDateType) => {
    if (!calendar.value) return;
    calendar.value.selectDate(val);
};

//判断日期是否已经选中
const dateInArray = (date: string) => {
    return selectDate.value.includes(date);
};
//时间排序
const sortDate = () => {
    selectDate.value = unref(selectDate).sort((a, b) => {
        const dateA = new Date(a);
        const dateB = new Date(b);
        return dateA.getTime() - dateB.getTime();
    });
    selectDateInfo.value = unref(selectDateInfo).sort((a, b) => {
        const dateA = new Date(a.date);
        const dateB = new Date(b.date);
        return dateA.getTime() - dateB.getTime();
    });
};
//点击每个日期
const dateClick = (day: string) => {
    let index = selectDate.value.indexOf(day);
    if (index == -1) {
        selectDate.value.push(day);
        selectDateInfo.value.push({
            date: day,
            booksName: '',
            weekName: getDayOfWeek(day),
            bookId: '',
        });
        sortDate();
    } else {
        selectDate.value.splice(index, 1);
        selectDateInfo.value.splice(index, 1);
    }
    updateValue();
    //为了触发校验规则
    emit('selectDate');
};
//根据日期获取星期信息
const getDayOfWeek = (dateString: string) => {
    const date = new Date(dateString);
    const day = date.getDay() == 0 ? 7 : date.getDay();
    return weeks.find((item) => item.id === day)?.weekname || '';
};
const changeSelect = (tagValue: string) => {
    selectDateInfo.value.splice(
        selectDateInfo.value.findIndex((item) => item.date === tagValue),
        1
    );
    updateValue();
};
//清空
const clearDate = () => {
    selectDate.value = [];
    selectDateInfo.value = [];
    changeCalendar('today');
    updateValue();
};
</script>

<style lang="scss" scoped>
.calendarMultiple_container {
    width: 100%;
    min-width: 600px;
    z-index: 9999;
    border-radius: 4px;
    background-color: #fff;
    left: 0;
    border: 1px solid #ccc;
}

::v-deep(.el-select-dropdown__empty) {
    display: none !important;
}

::v-deep(.el-calendar__header) {
    display: flex;
    align-items: center;
}
</style>
