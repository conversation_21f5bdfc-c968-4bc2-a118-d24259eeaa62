<template>
    <el-dialog
        :title="`新增${attendClassType === AttendClassType.liveType ? LIVE_CLASS_NAME : POINT_CLASS_NAME}排课`"
        v-model="visible"
        :close-on-click-modal="false"
        width="950px"
    >
        <el-form
            ref="dataFormRef"
            :model="form"
            :rules="dataRules"
            formDialogRef
            label-width="90px"
            v-loading="loading"
        >
            <el-row :gutter="24">
                <el-col :span="24" class="mb20">
                    <el-form-item label="班级名称" prop="classId">
                        <fuzzy-search
                            class="w-96"
                            :echo-content="form.className"
                            placeholder="输入搜索班级信息"
                            v-model="form.classId"
                            :requestUrl="getClassList"
                            request-name="className"
                            data-label="className"
                            :disabled="!!form.id"
                        ></fuzzy-search>
                    </el-form-item>
                </el-col>
                <el-col :span="24" class="mb20">
                    <el-form-item label="主讲老师" prop="lecturerId">
                        <fuzzy-search
                            class="w-96"
                            :echo-content="form.lecturerName"
                            placeholder="输入搜索主讲老师"
                            v-model="form.lecturerId"
                            :requestUrl="getLecturerList"
                            request-name="query"
                            data-label="lecturerName"
                        ></fuzzy-search>
                        <el-button type="primary" text class="ml-2" @click="teacherTimeRef.openDialog(form.lecturerId)"
                            >查看讲师空闲时间</el-button
                        >
                    </el-form-item>
                </el-col>
                <el-col :span="24" class="mb20">
                    <el-form-item label="排课方式" prop="classTimeMethod">
                        <el-radio-group v-model="form.classTimeMethod" @change="clearValidate">
                            <el-radio :label="item.value" border v-for="item in class_time_method" :key="item.id">{{
                                item.label
                            }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                </el-col>
                <!-- 按日历排课---start -->
                <div v-if="form.classTimeMethod == ClassTimeMethod.dayTime" class="w-full">
                    <el-col :span="24" class="mb20">
                        <el-form-item label="上课日期" prop="attendClassDates">
                            <multiple-date-select
                                v-model="form.attendClassDates"
                                v-model:attendClassInfo="form.attendClassInfo"
                                @selectDate="selectDate"
                            ></multiple-date-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24" class="mb20">
                        <el-form-item label="上课时间" prop="attendClassTime">
                            <el-time-picker
                                v-model="form.attendClassTime"
                                format="HH:mm"
                                is-range
                                range-separator="至"
                                start-placeholder="开始时间"
                                end-placeholder="结束时间"
                                class="max-w-96"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="24" class="mb20">
                        <el-form-item
                            label="上课教室"
                            prop="classRoomId"
                            v-if="attendClassType === AttendClassType.liveType"
                        >
                            <el-select v-model="form.classRoomId" placeholder="请选择" class="w-96">
                                <el-option
                                    v-for="item in live_room"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                />
                            </el-select>
                            <el-button type="primary" text class="ml-2" @click="roomTimeRef.openDialog()"
                                >查看教室空闲时间</el-button
                            >
                        </el-form-item>
                    </el-col>
                    <el-col :span="24" class="mb20">
                        <el-form-item label="上课信息" prop="attendClassInfo">
                            <el-table :data="form.attendClassInfo">
                                <el-table-column label="日期" prop="date" width="100" />
                                <el-table-column label="周" prop="weekName" width="100" />
                                <el-table-column label="关联书籍名" prop="booksName">
                                    <template #header="{ column }">
                                        <div>
                                            <span class="mr-2">{{ column.label }}</span>
                                            <el-tooltip effect="dark" content="打开批量设置" placement="top">
                                                <el-button
                                                    icon="Setting"
                                                    size="small"
                                                    circle
                                                    @click="form.showBatch = true"
                                                    v-show="!form.showBatch"
                                                ></el-button>
                                            </el-tooltip>
                                            <span v-show="form.showBatch">
                                                <el-input
                                                    style="width: 280px"
                                                    v-model="form.batchName"
                                                    maxlength="64"
                                                    placeholder="批量设置"
                                                    clearable
                                                />
                                                {{ form.batchName.length }}/64
                                                <el-tooltip effect="dark" content="批量设置书名" placement="top">
                                                    <el-button
                                                        icon="Finished"
                                                        circle
                                                        @click="batchBookName(form.batchName)"
                                                    ></el-button>
                                                </el-tooltip>
                                                <el-button
                                                    icon="Close"
                                                    circle
                                                    @click="form.showBatch = false"
                                                ></el-button>
                                            </span>
                                        </div>
                                    </template>
                                    <template #default="cProps">
                                        <el-input
                                            v-model="cProps.row.booksName"
                                            maxlength="64"
                                            style="width: 350px"
                                            placeholder="请输入"
                                        />
                                        {{ cProps.row.booksName.length }}/64
                                    </template>
                                </el-table-column>
                            </el-table>
                        </el-form-item>
                    </el-col>
                </div>
                <!-- 按日历排课---end -->

                <!-- 按周排课---start -->
                <div v-if="form.classTimeMethod == ClassTimeMethod.weeklyTime">
                    <el-col :span="24" class="mb20">
                        <el-form-item label="上课日期" prop="attendClassDate">
                            <el-date-picker
                                v-model="form.attendClassDate"
                                type="daterange"
                                class="max-w-96"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                                @change="changeClassDate"
                            />
                        </el-form-item>
                    </el-col>

                    <el-col :span="24" class="mb20">
                        <el-form-item label="排课上限" prop="scheduleCap">
                            <el-input
                                class="max-w-96"
                                v-model="form.scheduleCap"
                                placeholder="若不填默认按照选择日期排满"
                            />
                        </el-form-item>
                    </el-col>
                    <el-col :span="24" class="mb20">
                        <el-form-item label="上课时间" prop="classCourseScheduleVOList">
                            <set-class-time
                                ref="setClassTimeRef"
                                :attendClassDate="form.attendClassDate"
                                @openRoomFreeTime="roomTimeRef.openDialog()"
                                :attendClassType="attendClassType"
                            ></set-class-time>
                        </el-form-item>
                    </el-col>
                </div>
                <!-- 按周排课---end -->
                <el-col :span="24" class="mb20" v-if="attendClassType === AttendClassType.pointType">
                    <el-form-item label="课程视频" prop="recordingId">
                        <vod-selector v-model="form.recordingId"></vod-selector>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="visible = false">取消</el-button>
                <el-button type="primary" @click="onSubmit" :disabled="loading">确认</el-button>
            </span>
        </template>
    </el-dialog>
    <teacher-time ref="teacherTimeRef" @change="changeTeacher"></teacher-time>
    <room-time ref="roomTimeRef"></room-time>
</template>

<script setup lang="ts" name="SsClassTimeDialog">
// import { useDict } from '/@/hooks/dict';
import { useMessage, useMessageBox } from '/@/hooks/message';
import { addReading } from '/@/api/eduConnect/ssClassTime';
import { addVod } from '/@/api/eduConnect/ssPoint';

import MultipleDateSelect from './multipleDateSelect.vue';
import setClassTime from './setClassTime.vue';
import fuzzySearch from '../fuzzySearch.vue';
import { getSsClassList, getLecturerList } from '/@/api/eduConnect/ssQuery';
import { useDict } from '/@/hooks/dict';
import { AttendClassType, ClassTimeMethod, LIVE_CLASS_NAME, POINT_CLASS_NAME } from '../enums/eduEnums';
import { ClassCourseScheduleVoType, TableData, ClassTime } from '../types/classTimeType/index';
import { formatDate } from '/@/utils/formatTime';
import teacherTime from '../freeTime/teacherTime.vue';
import roomTime from '../freeTime/roomTime.vue';
import vodSelector from '../vodSelector/index.vue';
import { ElMessageBox } from 'element-plus';
const emit = defineEmits(['refresh']);
const attendClassType = ref<AttendClassType>(AttendClassType.liveType);
// 定义变量内容
const dataFormRef = ref();
const visible = ref(false);
const loading = ref(false);
const setClassTimeRef = ref();
const teacherTimeRef = ref();
const roomTimeRef = ref();
const { live_room, class_time_method } = useDict('live_room', 'class_time_method');
// 提交表单数据
const form = reactive<{
    id?: string;
    classTimeMethod: string;
    lecturerId: string;
    classId: string;
    recordingId?: string;
    isOutCollide: number;
    className?: string;
    lecturerName?: string;
    //按周排课用
    classCourseScheduleVOList: ClassCourseScheduleVoType[];
    attendClassDate?: [Date, Date] | [string, string];
    scheduleCap: string;

    //按日历排课用
    classRoomId: string;
    attendClassTime?: [Date, Date] | [string, string];
    attendClassDates: string[];
    showBatch: boolean;
    batchName: string;
    attendClassInfo: ClassTime[];
}>({
    // id: '',
    classTimeMethod: '1',
    attendClassDate: undefined,
    lecturerId: '',
    classId: '',
    classRoomId: '',
    attendClassTime: undefined,
    classCourseScheduleVOList: [],
    scheduleCap: '',
    attendClassDates: [],
    showBatch: false,
    batchName: '',
    attendClassInfo: [],
    recordingId: '',
    isOutCollide: 0,
    className: '',
});
const changeTeacher = (id: string, name: string) => {
    form.lecturerId = id;
    // teacherFuzzySearch.value.remoteMethod(name);
    form.lecturerName = name;
};
const clearValidate = () => {
    dataFormRef.value?.clearValidate();
};
// 定义校验规则
const dataRules = ref({
    classId: [{ required: true, message: '请选择班级', trigger: 'change' }],
    lecturerId: [{ required: true, message: '请选择主讲老师', trigger: 'change' }],
    classTimeMethod: [{ required: true, message: '请选择排课方式', trigger: 'change' }],
    classCourseScheduleVOList: [
        {
            required: true,
            validator: (rule?: any, value?: any, callback?: any) => {
                callback();
            },
        },
    ],

    attendClassDates: [
        {
            required: true,
            validator: (rule?: any, value?: any, callback?: any) => {
                if (value.length === 0) {
                    callback(new Error('请选择上课日期'));
                } else {
                    callback();
                }
            },
            trigger: 'change',
        },
    ],
    attendClassTime: [{ required: true, message: '请选择上课时间', trigger: 'change' }],
    classRoomId: [{ required: true, message: '请选择上课教室', trigger: 'change' }],
    attendClassDate: [{ required: true, message: '请选择上课日期', trigger: 'change' }],
    attendClassInfo: [
        {
            required: true,
            validator: (rule?: any, value?: any, callback?: any) => {
                callback();
            },
        },
    ],
    recordingId: [{ required: true, message: '请选择课程视频', trigger: 'change' }],
});

// 打开弹窗
const openDialog = (type: AttendClassType, row?: any) => {
    attendClassType.value = type;
    form.attendClassDates = [];
    form.attendClassInfo = [];
    form.showBatch = false;
    form.batchName = '';
    visible.value = true;
    const now = new Date();

    // 计算下一个整点
    const nextHour = new Date(now);
    nextHour.setHours(now.getHours() + 1);
    nextHour.setMinutes(0);
    nextHour.setSeconds(0);
    nextHour.setMilliseconds(0);

    // 计算下一个整点后一小时的整点
    const afterNextHour = new Date(nextHour);
    afterNextHour.setHours(nextHour.getHours() + 1);

    // 设置表单中的时间
    form.attendClassTime = [nextHour, afterNextHour];

    // 重置表单数据
    nextTick(() => {
        resetForm(row);
    });
};

const getClassList = async (params: Record<string, any>) => {
    const requestParams = {
        ...params,
        classState: 0,
    };
    const res = await getSsClassList(requestParams);
    return res;
};

const resetForm = async (row?: any) => {
    try {
        await dataFormRef.value?.resetFields();
        if (isValidRow(row)) {
            form.id = row.classId ?? row.id; //
            form.classId = row.classId ?? row.id;
            form.className = row.className;
        }
    } catch (error) {
        useMessage().warning('请重新打开');
    }
};

const isValidRow = (row?: any): boolean => {
    return row && row.id !== undefined;
};

//整理按周的数据
const disposalData = () => {
    return new Promise<void>((resolve) => {
        nextTick(() => {
            let setDateList: TableData[] = setClassTimeRef.value.getData();
            let dateList: ClassCourseScheduleVoType[] = [];
            if (!Array.isArray(setDateList)) {
                return;
            }

            for (let i = 0; i < setDateList.length; i++) {
                const weeks = setDateList[i];
                if (!weeks || !Array.isArray(weeks.dateList)) {
                    continue;
                }

                for (let j = 0; j < weeks.dateList.length; j++) {
                    const dateData = weeks.dateList[j];
                    dateList.push({
                        attendClassDate: dateData.date,
                        booksId: j + 1 + '',
                        booksName: dateData.booksName,
                        attendClassStartTime: weeks.tStartDate + ':00',
                        attendClassEndTime: weeks.tEndDate + ':00',
                        classRoomId: weeks.tClassRoom,
                    });
                }
            }
            form.classCourseScheduleVOList = dateList;
            // dataFormRef.value.validateField('classCourseScheduleVOList')
            resolve();
        });
    });
};
const batchBookName = (name: string) => {
    if (!name.trim()) {
        useMessage().warning('批量设置的书名不能为空');
        return;
    }

    form.attendClassInfo = form.attendClassInfo.map((item) => ({
        ...item,
        booksName: name,
    }));
};
const selectDate = () => {
    dataFormRef.value.validateField('attendClassDates');
    dataFormRef.value.validateField('attendClassInfo');
};
//校验上课信息完整性
const infoIsFull = (classCourseScheduleVOList: ClassCourseScheduleVoType[]) => {
    if (!classCourseScheduleVOList.length) {
        useMessageBox().warning(`请设置上课时间信息`);
        return false;
    }
    // const missingFieldsIndex = classCourseScheduleVOList.findIndex(item =>
    //     !item.attendClassDate ||
    //     !item.attendClassEndTime ||
    //     !item.attendClassStartTime ||
    //     !item.booksName ||
    //     !item.classRoomId
    // );
    const missingFieldsIndex = classCourseScheduleVOList.findIndex((item) => !item.booksName);
    if (missingFieldsIndex != -1) {
        useMessageBox().warning(
            `请补充日期为 ${classCourseScheduleVOList[missingFieldsIndex].attendClassDate} 的课程信息`
        );
        return false;
    }

    return true;
};

// 提交
const onSubmit = async () => {
    const valid = await dataFormRef.value.validate().catch();
    if (!valid) return false;

    let params: any = {
        classId: form.classId,
        lecturerId: form.lecturerId,
        classTimeMethod: form.classTimeMethod,
        attendClassType: unref(attendClassType),
        recordingId: form.recordingId || '',
        isOutCollide: form.isOutCollide,
    };
    if (form.classTimeMethod == ClassTimeMethod.weeklyTime) {
        //按周排
        await disposalData();
        params = Object.assign(params, {
            classCourseScheduleVOList: form.classCourseScheduleVOList,
            scheduleCap: form.scheduleCap,
            // 缺少开始结束日期
        });
    } else if (form.classTimeMethod == ClassTimeMethod.dayTime) {
        //按日历排
        const dateList: ClassCourseScheduleVoType[] = form.attendClassInfo.map((item, index) => {
            return {
                attendClassDate: item.date,
                booksId: index + 1 + '',
                booksName: item.booksName,
                attendClassStartTime: formatDate(form.attendClassTime?.[0] as Date, 'HH:MM') + ':00',
                attendClassEndTime: formatDate(form.attendClassTime?.[1] as Date, 'HH:MM') + ':00',
                classRoomId: form.classRoomId,
            };
        });
        params = Object.assign(params, {
            classCourseScheduleVOList: dateList,
        });
    } else {
        useMessage().error('无可用排课方式');
        return;
    }
    if (!infoIsFull(params.classCourseScheduleVOList)) {
        return false;
    }
    try {
        loading.value = true;
        const res = await (unref(attendClassType) === AttendClassType.liveType ? addReading(params) : addVod(params));
        if (res?.code === 0) {
            useMessage().success('添加成功');
            visible.value = false;
            loading.value = false;
            emit('refresh');
        } else {
            throw res;
        }
    } catch (err: any) {
        loading.value = false;
        if (err.data === 'COURSE_SCHEDULE_CONFLICT') {
            ElMessageBox.confirm(err.msg, '提示', {
                confirmButtonText: '继续排课',
                cancelButtonText: '取消',
            }).then(() => {
                form.isOutCollide = 1;
                onSubmit();
            });
        } else {
            useMessage().error(err.msg);
        }
    }
};

const changeClassDate = () => {
    nextTick(() => {
        setClassTimeRef.value.clearAllSelectWeek();
    });
};
// 暴露变量
defineExpose({
    openDialog,
});
</script>
