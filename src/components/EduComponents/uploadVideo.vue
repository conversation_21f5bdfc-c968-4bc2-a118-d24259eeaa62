<template>
    <el-dialog v-model="dialogVisible" title="上传视频" width="500">
        <Upload-file
            :fileSize="props.fileSize"
            :fileType="props.fileType"
            :uploadFileUrl="url"
            :method="props.method"
            :limit="1"
            @expFile="expFile"
            @addRes="addRes"
            @delRes="delRes"
            ref="uploadFile"
        ></Upload-file>
        <template #footer>
            <div class="dialog-footer">
                <el-button @click="dialogVisible = false">取消</el-button>
                <el-button type="primary" @click="onSubmit"> 确认 </el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { upLoadRecord, refreshRecord, getLoadRecord } from '/@/api/eduConnect/ssRecording';
import { useMessage } from '/@/hooks/message';
const emit = defineEmits(['refresh']);
// interface resType{
//     data: {
//         requestId: string,
//         uploadAddress: string,
//         uploadAuth: string,
//         videoId: string,
//     }
// }

const props = defineProps({
    fileSize: {
        type: Number,
        default: 2024,
    },
    fileType: {
        type: Array,
        default: ['MP4', 'AVI', 'MOV', 'mp4', 'avi', 'mov'],
    },
    // 上传凭证
    uploadFileUrl: {
        type: String,
        default: `/edusystem/ssRecording/getUploadAuth`,
    },
    // 是否自动上传
    autoUpLoad: {
        type: Boolean,
        default: true,
    },
    // 请求方式
    method: {
        type: String,
        default: 'GET',
    },
});
const upLoadkey = ref(false);
const dialogVisible = ref(false);
let uploadFile: any;
const sendFile = ref();
// url
let url = ref(props.uploadFileUrl);
let res: any = reactive({});
let params = reactive({
    title: '',
    fileName: '',
});
const form: formType = reactive({
    recordingType: 0,
    deviceId: -1,
    booksName: '',
    lecturerId: -1,
    roomUuid: 0,
    storageType: 0,
});
//@ts-ignore
let uploader = new AliyunUpload.Vod({
    // userID，用于标识上传者的身份，必填，有值即可，可以是阿里云账号ID或者您自定义的用户ID，您可以访问阿里云账号中心（https://account.console.aliyun.com/）查看账号ID
    userId: '122',
    // 上传到视频点播的地域，默认值为'cn-shanghai'，
    //eu-central-1，ap-southeast-1
    region: '',
    // 分片大小默认1 MB，不能小于100 KB（100*1024）
    partSize: 1048576,
    // 并行上传分片个数，默认5
    parallel: 5,
    // 网络原因失败时，重新上传次数，默认为3
    retryCount: 3,
    // 网络原因失败时，重新上传间隔时间，默认为2秒
    retryDuration: 2,
    // 开始上传
    onUploadstarted: async function (uploadInfo: any) {
        if (uploadInfo.videoId) {
            //如果uploadInfo.videoId存在，调用刷新音视频上传凭证接口
            // 此处URL需要替换为服务端AppServer对应的地址
            res = await refreshRecord(uploadInfo.videoId);
            form.vodVideoId = res.data.videoId;
        } else {
            //如果uploadInfo.videoId不存在，调用获取音视频上传地址和凭证接口
            // 此处URL需要替换为您自己服务端的“获取音视频上传地址和凭证”接口的对应服务地址，例如：https://xxxx.com/createUploadVideo
            res = await getLoadRecord(params);
            form.vodVideoId = res.data.videoId;
        }
        if (res.data) {
            uploader.setUploadAuthAndAddress(uploadInfo, res.data.uploadAuth, res.data.uploadAddress, res.data.videoId);
        }
    },
    // 文件上传成功
    onUploadSucceed: function (uploadInfo: any) {
        useMessage().success('上传成功');
    },
    // 文件上传失败
    onUploadFailed: function (uploadInfo: any, code: any, message: any) {
        useMessage().error('上传失败');
    },
    // 文件上传进度，单位：字节
    onUploadProgress: function (uploadInfo: any, totalSize: any, loadedPercent: any) {
        console.log('上传进度', uploadInfo);
        console.log('上传进度', loadedPercent);
    },
    // 上传凭证或STS token超时
    onUploadTokenExpired: async function (uploadInfo: any) {
        res = await getLoadRecord(params);
        if (res.data) {
            uploader.setUploadAuthAndAddress(uploadInfo, res.data.uploadAuth, res.data.uploadAddress, res.data.videoId);
            form.vodVideoId = res.data.videoId;
        }
    },
    // 全部文件上传结束
    onUploadEnd: function (uploadInfo: any) {
        console.log(uploadInfo, 'onUploadEnd: uploaded all the files');
        upLoadkey.value = true;
        useMessage().success('上传结束');
    },
});
interface formType {
    recordingType?: number;
    deviceId?: number;
    booksName?: string;
    lecturerId?: number;
    roomUuid?: number;
    storageType?: number;
    vodVideoId?: string;
}
const openUploadDialog = () => {
    dialogVisible.value = true;
};

const expFile = (file: File) => {
    sendFile.value = file;
    form.booksName = sendFile.value?.type.substring(0, sendFile.value?.type.indexOf('/'));
    url.value += '?title=' + sendFile.value?.type + '&fileName=' + sendFile.value?.name;
    params.title = sendFile.value?.type;
    params.fileName = sendFile.value?.name;
    // 添加需要上传的文件
    uploader.addFile(file, null, null, null, '{"Vod":{}}');
};
const addRes = async (e: any) => {
    if (e.code == 0 && e.data.videoId) {
        res = e;
        form.storageType = 1;
        form.vodVideoId = e.data.videoId;
    }
    uploader.startUpload();
};
// 重置表单
const resetForm = () => {
    Object.assign(form, {
        vodVideoId: null,
    });
};
// 删除视频后关闭
const delRes = (e: File) => {
    resetForm();
};
const onSubmit = async () => {
    if (form.vodVideoId != null && upLoadkey.value == true) {
        try {
            let res = await upLoadRecord(form);

            if (res.code == 0) {
                useMessage().success('视频上传成功');
                dialogVisible.value = false;
                resetForm();
                console.log(form);
                emit('refresh');
            }
        } catch (error) {
            useMessage().error('视频上传失败');
        }
    } else {
        useMessage().error('请先要上传的选择视频');
    }
};

defineExpose({
    openUploadDialog,
});
</script>

<style scoped></style>
