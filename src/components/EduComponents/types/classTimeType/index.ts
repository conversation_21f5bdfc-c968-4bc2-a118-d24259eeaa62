import { AttendClassType, ClassTimeEditEnums } from '../../enums/eduEnums';

export type Week = '周一' | '周二' | '周三' | '周四' | '周五' | '周六' | '周日';
export type Weeks = {
    id: number;
    weekname: Week;
    disabled: boolean;
};
export type ClassTime = {
    date: string;
    booksName: string;
    weekName: string;
    bookId?: string;
} & Record<string, unknown>;
export type TableData = {
    id: number;
    tWeeks: number[];
    tStartDate: string;
    tEndDate: string;
    tClassRoom: string;
    tAction: string;
    isEdit: boolean;
    dateValue: [Date, Date] | undefined;
    dateList: Array<ClassTime>;
    batchName: string;
    showBatch: boolean;
};

export type ClassCourseScheduleVoType = {
    attendClassDate: string;
    attendClassStartTime: string;
    attendClassEndTime: string;
    booksId?: string;
    booksName: string;
    classRoomId: string;
};

export type CourseSchedulingProps = {
    attendClassType: number;
    classId?: string;
};

//所有周
export const weeks: Weeks[] = [
    { id: 1, weekname: '周一', disabled: false },
    { id: 2, weekname: '周二', disabled: false },
    { id: 3, weekname: '周三', disabled: false },
    { id: 4, weekname: '周四', disabled: false },
    { id: 5, weekname: '周五', disabled: false },
    { id: 6, weekname: '周六', disabled: false },
    { id: 7, weekname: '周日', disabled: false },
];

export type ClassTimeEditType = {
    type: AttendClassType; //课程类型
    editType: ClassTimeEditEnums; //编辑类型
    id: string; //课次id
};
export interface ClassTimeEditExpose {
    openDialog: (attr: ClassTimeEditType) => void;
}
