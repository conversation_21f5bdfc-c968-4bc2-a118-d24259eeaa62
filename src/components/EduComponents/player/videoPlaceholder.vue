<template>
    <div class="cover" :style="style">
        <slot>
            <div class="mask">
                <img class="playIcon" src="https://image.yuedushufang.com/resources/ss/admin/play.png" />
            </div>
        </slot>
    </div>
</template>
<script setup lang="ts">
import { computed } from 'vue';

const props = defineProps({
    cover: {
        type: String,
        default: 'https://image.yuedushufang.com/resources/ss/admin/video_default.png',
    },
});

const style = computed(() => {
    return {
        backgroundImage: `url("${props.cover}")`,
    };
});
</script>
<style scoped lang="scss">
.cover {
    width: 100px;
    height: 60px;
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
    //background-image: url("");
    border-radius: 3px;

    .mask {
        position: relative;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.2);
    }
    .playIcon {
        position: absolute;
        width: 40px;
        height: 40px;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        cursor: pointer;
    }
}
</style>
