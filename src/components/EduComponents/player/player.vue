<template>
    <div class="player-wrap" ref="playerWrapRef">
        <div class="player" :id="playerId"></div>
    </div>
</template>
<script setup lang="ts">
import { usePlayer, PlayerConfig } from '/@/hooks/player';

const { createPlayer, destroyPlayer } = usePlayer();
const props = defineProps({
    // 资源路径
    source: {
        type: String,
        require: true,
    },
    // 宽高比
    aspectRatio: {
        type: Number,
        default: 16 / 9,
    },
});

const emit = defineEmits(['mounted']);

const playerWrapRef = ref();

const playerId = `playerId_${Date.now()}`;
let player: any;
watch(
    () => props.source,
    (source) => {
        if (source) {
            player = initPlayer();
        }
    }
);

const initPlayer = () => {
    if (player) {
        destroyPlayer(player);
    }
    const { offsetWidth: width } = playerWrapRef.value;
    const height = Math.ceil(width / props.aspectRatio);
    console.log('### 初始化视频播放器宽高', width, height, width / height);
    const options: PlayerConfig = Object.assign(
        { id: playerId, source: props.source as string },
        {
            width: width + 'px',
            height: height + 'px',
        }
    );
    player = createPlayer(options);

    // 监听播放器准备就绪
    player.on('ready', () => {
        console.log('播放器准备就绪');
        emit('mounted');
    });

    return player;
};
onMounted(() => {
    initPlayer();
});

onUnmounted(() => {
    if (player) destroyPlayer(player);
});
</script>
<style scoped lang="scss">
.player-wrap {
    overflow: hidden;
    //border: 1px solid red;
    //box-sizing: border-box;
}
</style>
