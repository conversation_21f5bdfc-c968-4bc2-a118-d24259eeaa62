<template>
    <el-dialog title="预览" v-model="visible" :close-on-click-modal="false" width="1000px">
        <Player v-if="source" :source="source"></Player>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="visible = false">退出预览</el-button>
                <!--				<el-button type="primary" @click="onSubmit" :disabled="loading">确认</el-button>-->
            </span>
        </template>
    </el-dialog>
</template>

<script setup lang="ts" name="SsRecordingDialog">
// import { useMessage } from '/@/hooks/message';
import Player from '/@/components/EduComponents/player/player.vue';
// 定义变量内容
const source = ref('');
const visible = ref(false); // 可见性
watch(visible, (visible) => {
    if (!visible) source.value = '';
});
// const message = useMessage();
// 打开弹窗
const openDialog = async (src: any) => {
    if (!source) return;
    visible.value = true;
    nextTick(() => {
        source.value = src;
    });
};
// 暴露变量
defineExpose({
    openDialog,
});
</script>
