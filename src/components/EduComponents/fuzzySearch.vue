<template>
    <el-select
        v-bind="$attrs"
        v-model="_modelValue"
        clearable
        filterable
        :remote="remote"
        remote-show-suffix
        :loading="loading"
        :remote-method="remoteMethodRequest"
        @change="onChange"
    >
        <el-option
            v-for="item in options"
            :key="item[props.dataKey as string]"
            :label="getLabel ? getLabel(item) : item[props.dataLabel as string]"
            :value="item[props.dataKey as string]"
        >
            <span v-if="getLabel">{{ getLabel(item) }}</span>
        </el-option>
    </el-select>
</template>

<script lang="ts" setup>
import { debounce } from 'lodash';
import { useMessage } from '/@/hooks/message';

const emit = defineEmits(['update:modelValue', 'change']);
interface SearchPropsType {
    modelValue: any;
    requestUrl: (arg?: any) => Promise<any>;
    echoContent?: string;
    requestName?: string;
    dataKey?: string;
    dataLabel?: string;
    getLabel?: (arg?: any) => string;
    debounceTime?: number;
    remote?: boolean;
}
const props = withDefaults(defineProps<SearchPropsType>(), {
    value: '',
    requestName: 'name', //接口请求参数
    dataKey: 'id', //数组key
    dataLabel: 'label', //数组label
    debounceTime: 300, //防抖时间
    echoContent: '',
    remote: true,
    getLabel: undefined,
});
const loading = ref(false);
const options = ref<any[]>([]);
const _modelValue = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value),
});
// watch(
// 	() => _modelValue.value,
// 	() => {
// 		emit('update:modelValue', _modelValue.value);
// 	}
// );
watch(
    () => props.echoContent,
    (val) => {
        //解决部分使用if场景回显问题
        nextTick(() => {
            if (val) {
                remoteMethod(val);
            }
        });
    },
    {
        immediate: true,
    }
);

onMounted(() => {
    if (!props.remote) {
        remoteMethodRequest();
    }
});

const setLoading = (status: boolean) => {
    loading.value = status;
};
const remoteMethod = (name: string) => {
    //解决props.modelValue赋值问题
    setTimeout(() => {
        options.value = [
            {
                [props.dataKey]: props.modelValue,
                [props.dataLabel]: name,
            },
        ];
    });
};
const remoteMethodRequest = debounce((query?: string) => {
    if (query || !props.remote) {
        setLoading(true);
        const validatedQuery = query?.trim().slice(0, 100); // 限制长度，防止过长的查询
        props
            .requestUrl(props.remote ? { [props.requestName]: validatedQuery } : {})
            .then((res: any) => {
                options.value = res.data;
            })
            .catch((error: any) => {
                useMessage().error(error);
            })
            .finally(() => {
                setLoading(false);
            });
    }
}, props.debounceTime);

const clearOptions = () => {
    options.value = [];
};

const onChange = (val: any) => {
    emit(
        'change',
        options.value.find((item: any) => item[props.dataKey] === val)
    );
};

//为了数据回显
defineExpose({ remoteMethod, clearOptions });
</script>
