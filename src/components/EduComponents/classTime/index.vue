<template>
    <el-dialog
        :title="isEdit ? '编辑课次' : '新增课次'"
        v-model="visible"
        :close-on-click-modal="false"
        draggable
        width="600"
    >
        <el-form
            ref="dataFormRef"
            :model="form"
            :rules="dataRules"
            formDialogRef
            label-width="90px"
            v-loading="loading"
        >
            <el-row :gutter="24">
                <el-col :span="24" class="mb20">
                    <el-form-item label="班级名称" prop="classId">
                        <fuzzy-search
                            class="w-96"
                            :echo-content="form.className"
                            placeholder="输入搜索班级信息"
                            v-model="form.classId"
                            :requestUrl="getSsClassList"
                            request-name="className"
                            data-label="className"
                            :disabled="!!form.id || props.disabled"
                        ></fuzzy-search>
                    </el-form-item>
                </el-col>
                <el-col :span="24" class="mb20" v-if="isEdit">
                    <el-form-item label="主讲老师" prop="lecturerId">
                        <fuzzy-search
                            class="w-96"
                            :echoContent="form.lecturerName"
                            placeholder="输入搜索主讲老师"
                            v-model="form.lecturerId"
                            :requestUrl="getLecturerList"
                            request-name="query"
                            data-label="lecturerName"
                            :disabled="props.disabled"
                        ></fuzzy-search>

                        <el-tooltip class="box-item" effect="dark" content="查看讲师空闲时间" placement="top-start">
                            <el-button
                                circle
                                icon="View"
                                class="ml-2"
                                @click="teacherTimeRef.openDialog(form.lecturerId)"
                            ></el-button>
                        </el-tooltip>
                    </el-form-item>
                </el-col>
                <el-col :span="24" class="mb20">
                    <el-form-item label="关联书籍" prop="booksName">
                        <el-input
                            style="width: 280px"
                            v-model="form.booksName"
                            maxlength="64"
                            placeholder="请输入关联书籍"
                            clearable
                            :disabled="props.disabled"
                        />
                        <span class="ml-2">{{ form.booksName.length }}/64</span>
                    </el-form-item>
                </el-col>
                <el-col :span="24" class="mb20">
                    <el-form-item label="上课日期" prop="attendClassDate">
                        <el-date-picker
                            :disabled="props.disabled"
                            v-model="form.attendClassDate"
                            type="date"
                            placeholder="请选择上课日期"
                            format="YYYY-MM-DD"
                            value-format="YYYY-MM-DD"
                        />
                    </el-form-item>
                </el-col>
                <el-col :span="24" class="mb20">
                    <el-form-item label="上课时间" prop="attendClassTime">
                        <el-time-picker
                            :disabled="props.disabled"
                            v-model="form.attendClassTime"
                            format="HH:mm"
                            is-range
                            range-separator="至"
                            start-placeholder="开始时间"
                            end-placeholder="结束时间"
                            class="max-w-96"
                        />
                    </el-form-item>
                </el-col>
                <el-col :span="24" class="mb20" v-if="isEdit && attendClassType === AttendClassType.liveType">
                    <el-form-item label="上课教室" prop="classRoomId">
                        <el-select
                            :disabled="props.disabled"
                            v-model="form.classRoomId"
                            placeholder="请选择上课教室"
                            class="w-96"
                        >
                            <el-option
                                v-for="item in live_room"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            />
                        </el-select>
                        <el-tooltip class="box-item" effect="dark" content="查看教室空闲时间" placement="top-start">
                            <el-button circle icon="View" class="ml-2" @click="roomTimeRef.openDialog()"></el-button>
                        </el-tooltip>
                    </el-form-item>
                </el-col>
                <el-col :span="24" class="mb20" v-if="attendClassType === AttendClassType.pointType">
                    <el-form-item label="课程视频" prop="recordingId">
                        <vod-selector :disabled="props.disabled" v-model="form.recordingId"></vod-selector>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <template #footer>
            <span class="dialog-footer" v-if="!props.disabled">
                <el-button @click="visible = false">取 消</el-button>
                <el-button type="primary" @click="onSubmit" :disabled="loading">确 认</el-button>
            </span>
            <span v-else>
                <el-button @click="visible = false">关 闭</el-button>
            </span>
        </template>
        <teacher-time ref="teacherTimeRef" @change="changeTeacher"></teacher-time>

        <room-time ref="roomTimeRef"></room-time>
    </el-dialog>
</template>

<script setup lang="ts" name="editForm">
import { useDict } from '/@/hooks/dict';
import { useMessage } from '/@/hooks/message';
import { getClassTimeInfo, addClassTime, updateClassTime } from '/@/api/eduConnect/ssClassTime';
import { getSsClassList, getLecturerList } from '/@/api/eduConnect/ssQuery';
import fuzzySearch from '/@/components/EduComponents/fuzzySearch.vue';
import { formatDate } from '/@/utils/formatTime';
import { AttendClassType, ClassTimeEditEnums } from '../enums/eduEnums';
import { ClassTimeEditExpose, ClassTimeEditType } from '../types/classTimeType';
import teacherTime from '../freeTime/teacherTime.vue';
import roomTime from '../freeTime/roomTime.vue';
import vodSelector from '../vodSelector/index.vue';
import { ElMessageBox } from 'element-plus';
const emit = defineEmits(['refresh']);
const props = defineProps({
    disabled: {
        type: Boolean,
        default: false,
    },
});
// 定义变量内容
const dataFormRef = ref();
const visible = ref(false);
const loading = ref(false);
const isEdit = ref(false);
const attendClassType = ref(AttendClassType.liveType);
const teacherTimeRef = ref();
const roomTimeRef = ref();
// 定义字典
const { live_room } = useDict('live_room');

// 提交表单数据
const form = reactive<any>({
    id: '',
    classId: '',
    lecturerId: '',
    booksName: '',
    attendClassDate: '',
    attendClassTime: [] as any,
    classRoomId: '',
    recordingId: '',
    isOutCollide: 0,
});
const changeTeacher = (id: string, name: string) => {
    if (props.disabled) {
        return useMessage().warning('该课次状态无法修改主讲老师信息');
    }
    form.lecturerId = id;
    form.lecturerName = name;
};

// 定义校验规则
const dataRules = ref({
    classId: [{ required: true, message: '请选择班级名称', trigger: 'change' }],
    lecturerId: [{ required: true, message: '请选择主讲老师', trigger: 'change' }],
    booksName: [{ required: true, message: '请输入关联书籍', trigger: 'blur' }],
    attendClassDate: [{ required: true, message: '请选择上课日期', trigger: 'change' }],
    attendClassTime: [{ required: true, message: '请选择上课时间', trigger: 'change' }],
    classRoomId: [{ required: true, message: '请选择上课教室', trigger: 'change' }],
    recordingId: [{ required: true, message: '请选择视频课程', trigger: 'change' }],
});

// 打开弹窗
const openDialog = (attr: ClassTimeEditType) => {
    const { type, editType, id } = attr;
    attendClassType.value = type;
    visible.value = true;
    form.id = '';
    isEdit.value = editType === ClassTimeEditEnums.EDIT;

    // 重置表单数据
    nextTick(async () => {
        await dataFormRef.value?.resetFields();
        if (id) {
            form.id = id;
            getSsClassTimeData(id);
        }
    });
};

// 提交
const onSubmit = async () => {
    const valid = await dataFormRef.value.validate().catch(() => {});
    if (!valid) return false;

    try {
        loading.value = true;
        let params: any = Object.assign({}, form);
        params.attendClassStartTime = formatDate(params.attendClassTime?.[0] as Date, 'HH:MM') + ':00';
        params.attendClassEndTime = formatDate(params.attendClassTime?.[1] as Date, 'HH:MM') + ':00';
        params.booksId = params.booksId ?? 1;
        delete params.attendClassTime;
        const res = unref(isEdit) ? await updateClassTime(params) : await addClassTime(params);
        if (res?.code === 0) {
            useMessage().success(unref(isEdit) ? '修改成功' : '新增课次成功');
            visible.value = false;
            emit('refresh');
        } else {
            throw res;
        }
    } catch (err: any) {
        loading.value = false;
        if (err.data === 'COURSE_SCHEDULE_CONFLICT') {
            ElMessageBox.confirm(err.msg, '提示', {
                confirmButtonText: '继续排课',
                cancelButtonText: '取消',
            }).then(() => {
                form.isOutCollide = 1;
                onSubmit();
            });
        } else {
            useMessage().error(err.msg);
        }
    }
};

// 初始化表单数据
const getSsClassTimeData = (id: string) => {
    // 获取数据
    loading.value = true;
    getClassTimeInfo(id)
        .then((res: any) => {
            Object.assign(form, res.data);
            if (!unref(isEdit)) {
                form.recordingId = '';
            }
            form.attendClassTime = [
                new Date(res.data.attendClassDate + ' ' + res.data.attendClassStartTime),
                new Date(res.data.attendClassDate + ' ' + res.data.attendClassEndTime),
            ];
        })
        .finally(() => {
            loading.value = false;
        });
};

// 暴露变量
defineExpose<ClassTimeEditExpose>({
    openDialog,
});
</script>
