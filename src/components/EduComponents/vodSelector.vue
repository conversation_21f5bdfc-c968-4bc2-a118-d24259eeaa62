<template>
    <el-dialog
        :title="$t(props.title)"
        v-model="show"
        :close-on-click-modal="false"
        draggable
        style="width: 1200px; height: 900px"
    >
        <el-form :inline="true" :model="state.queryForm" class="demo-form-inline" ref="queryRef">
            <el-form-item>
                <el-input v-model="state.queryForm.booksName" placeholder="请输入录课书籍" clearable>
                    <template #prepend>
                        <el-select v-model="state.queryForm.recordingType" placeholder="点播课" style="width: 110px">
                            <el-option
                                v-for="option in recording_type"
                                :key="option.value"
                                :label="option.label"
                                :value="option.value"
                            />
                        </el-select>
                    </template>
                </el-input>
            </el-form-item>
            <el-form-item prop="lecturerId" label="主讲师">
                <fuzzy-search
                    class="w-96"
                    placeholder="输入搜索主讲老师"
                    v-model="state.queryForm.lecturerId"
                    :requestUrl="getLecturerList"
                    request-name="query"
                    data-label="lecturerName"
                    clearable
                ></fuzzy-search>
            </el-form-item>

            <el-form-item label="原上课时间">
                <el-date-picker
                    v-model="dateTimeRange"
                    type="datetimerange"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    :value-format="dateTimeStr"
                />
            </el-form-item>

            <el-form-item style="margin-bottom: 0px !important">
                <el-button icon="search" type="primary" @click="getDataList">
                    {{ $t('common.queryBtn') }}
                </el-button>
            </el-form-item>
            <!--        <el-button icon="Refresh" type="primary" @click="resetQuery">{{$t('common.resetBtn')}}</el-button>-->
        </el-form>
        <el-row :gutter="20">
            <el-col :span="12" v-for="video in state.dataList" :key="video.id">
                <div
                    v-on:click="handleSelect(video)"
                    :class="['video-container', selectedVideo.id === video.id ? 'video-container-selected' : '']"
                >
                    <videoPlaceholder style="height: 100px; width: 150px" @click="handlePreview(video)" />
                    <div class="info-box">
                        <div>阶段： {{ setGrade(video.grade) }}</div>
                        <div>录课书籍：{{ video.booksName }}</div>
                        <div>主讲师： {{ video.lecturerName }}</div>
                        <div>原上课时间：{{ video.originalCourseStartDate }} - {{ video.originalCourseEndDate }}</div>
                    </div>
                </div>
            </el-col>
        </el-row>

        <pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" v-bind="state.pagination" />
        <player-dialog ref="playerDialogRef"></player-dialog>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="show = false">取 消</el-button>
                <el-button type="primary" @click="onConfirm" :disabled="loading || !selectedVideo.id">确 认</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue';
import videoPlaceholder from '/@/components/EduComponents/player/videoPlaceholder.vue';
import { BasicTableProps, useTable } from '/@/hooks/table';
import { fetchList } from '/@/api/EduComponents/videoSelector';
import { useMessage } from '/@/hooks/message';
import { getLecturerList } from '/@/api/eduConnect/ssQuery';
import fuzzySearch from '/@/components/EduComponents/fuzzySearch.vue';
// import { styleType } from 'element-plus/es/components/table-v2/src/common';
import { dateTimeStr } from '/@/utils/formatTime';
import { useDict } from '/@/hooks/dict';
import PlayerDialog from '/@/components/EduComponents/player/playerDialog.vue';

const props = defineProps({
    title: {
        type: String,
        default: '选择课程',
    },
});

const { recording_type } = useDict('recording_type');
// 是否显示
const show = ref(false);
// loading状态
const loading = ref(false);
// 用户选择到的vod
const selectedVideo = ref({ id: '' });

// ref
const queryRef = ref();
const playerDialogRef = ref();

const state: BasicTableProps = reactive<BasicTableProps>({
    queryForm: {
        recordingType: '0', // 录制类型
        booksName: '', // 书名
        lecturerId: '', // 讲师ID
        originalCourseStartDate: '', // 原考勤班级上课开始时间
        originalCourseEndDate: '', // 原考勤班级上课结束时间
    }, // 查询表单数据
    pageList: fetchList, // 请求方法
});
const dateTimeRange = ref(''); // 日期选择器范围
watch(
    dateTimeRange,
    (v) => {
        // console.log('### dateTimeRange', v);
        if (Array.isArray(v)) {
            const [originalCourseStartDate, originalCourseEndDate] = v;
            Object.assign(state.queryForm, { originalCourseStartDate, originalCourseEndDate });
        } else {
            Object.assign(state.queryForm, { originalCourseStartDate: '', originalCourseEndDate: '' });
        }
    },
    { flush: 'sync' }
);
const { getDataList, currentChangeHandle, sizeChangeHandle } = useTable(state);
// 清空搜索条件
// const resetQuery = () => {
//   queryRef.value?.resetFields();
//   getDataList();
// };
const { grade } = useDict('grade');

const setGrade = (value: string) => {
    return grade.value.find((item: any) => item.value === value + '')?.label || '/';
};

// 打开
const open = async () => {
    show.value = true;
    selectedVideo.value = { id: '' }; // 清空原选择内容
    queryRef.value?.resetFields();
};
// 用户选择
const handleSelect = (video: any) => {
    selectedVideo.value = video;
};

// 视频预览

const handlePreview = (row: any) => {
    if (row?.recordingResources) {
        playerDialogRef.value?.openDialog(row.recordingResources);
    } else {
        useMessage().warning('无法预览,视频未上传');
    }
};

// 用户确认选择，向上emit selected事件
const emits = defineEmits(['selected']);
const onConfirm = () => {
    if (selectedVideo.value.id) {
        show.value = false;
        emits('selected', selectedVideo.value);
    }
};

defineExpose({
    open,
});
</script>

<style scoped lang="scss">
.video-container {
    margin-top: 10px;
    border: 2px solid #ccc;
    display: flex;
    padding: 8px;

    //align-items: center;
    &-selected {
        border-color: var(--el-color-primary);
    }

    .info-box {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        //background: red;
        line-height: 1.2;
        padding-left: 10px;
    }
}

.block .demonstration {
    display: block;
    color: var(--el-text-color-secondary);
    font-size: 14px;
    margin-bottom: 20px;
}

li {
    margin-left: 20px;
    margin-top: 5px;
    cursor: default;
}
</style>
