<template>
    <div class="dict-tag-container">
        <template v-for="(item, index) in props.options">
            <template v-if="values.includes(item.value || item)">
                <!-- 文本模式 -->
                <span
                    v-if="isTextMode(item)"
                    :key="`span-${index}`"
                    :class="[sizeClass, getThemeClass(item.elTagType || type), item.elTagClass]"
                >
                    {{ item.label || item }}
                </span>

                <!-- 标签模式 -->
                <el-tag
                    v-else-if="mode === 'tag'"
                    :disable-transitions="true"
                    :key="`tag-${index}`"
                    :size="size"
                    :type="item.elTagType || type"
                    :class="[getThemeClass(item.elTagType || type), item.elTagClass, `theme-${theme}`]"
                    :effect="effect"
                    >{{ item.label || item }}</el-tag
                >

                <!-- 卡片模式 -->
                <div
                    v-else-if="mode === 'card'"
                    :key="`card-${index}`"
                    class="dict-card"
                    :class="[
                        `dict-card--${item.elTagType || type}`,
                        getThemeClass(item.elTagType || type),
                        item.elTagClass,
                        `theme-${theme}`,
                        sizeClass,
                    ]"
                >
                    <span class="dict-card__icon" v-if="showIcon">
                        <i :class="getIconClass(item.elTagType || type)"></i>
                    </span>
                    <span class="dict-card__content">{{ item.label || item }}</span>
                </div>

                <!-- 胶囊模式 -->
                <div
                    v-else-if="mode === 'pill'"
                    :key="`pill-${index}`"
                    class="dict-pill"
                    :class="[
                        `dict-pill--${item.elTagType || type}`,
                        getThemeClass(item.elTagType || type),
                        item.elTagClass,
                        `theme-${theme}`,
                        sizeClass,
                    ]"
                >
                    <span class="dict-pill__icon" v-if="showIcon">
                        <i :class="getIconClass(item.elTagType || type)"></i>
                    </span>
                    <span class="dict-pill__content">{{ item.label || item }}</span>
                </div>

                <!-- 徽章模式 -->
                <div
                    v-else-if="mode === 'badge'"
                    :key="`badge-${index}`"
                    class="dict-badge"
                    :class="[
                        `dict-badge--${item.elTagType || type}`,
                        getThemeClass(item.elTagType || type),
                        item.elTagClass,
                        `theme-${theme}`,
                        sizeClass,
                    ]"
                >
                    {{ item.label || item }}
                </div>

                <!-- 默认使用标签模式 -->
                <el-tag
                    v-else
                    :disable-transitions="true"
                    :key="`default-tag-${index}`"
                    :size="size"
                    :type="item.elTagType || type"
                    :class="[getThemeClass(item.elTagType || type), item.elTagClass, `theme-${theme}`]"
                    :effect="effect"
                    >{{ item.label || item }}</el-tag
                >
            </template>
        </template>
    </div>
</template>

<script setup lang="ts" name="dict-tag">
import { computed } from 'vue';

const props = defineProps({
    // 数据选项
    options: {
        type: Array as any,
        default: () => [],
        required: true,
    },
    // 当前的值
    value: {
        type: [Number, String, Array],
        default: null,
    },
    // 标签类型
    type: {
        type: String,
        values: ['primary', 'success', 'info', 'warning', 'danger', 'default'],
        default: 'primary',
    },
    // 渲染模式
    mode: {
        type: String,
        values: ['tag', 'card', 'pill', 'badge', 'text'],
        default: 'tag',
    },
    // 尺寸选项
    size: {
        type: String,
        values: ['small', 'default', 'large'],
        default: 'default',
    },
    // 主题选项
    theme: {
        type: String,
        values: ['default', 'eco', 'soft', 'vivid'],
        default: 'eco', // 以您的主题色为基础的生态主题
    },
    // 是否显示图标
    showIcon: {
        type: Boolean,
        default: false,
    },
    // 边框风格
    effect: {
        type: String,
        values: ['light', 'dark', 'plain'],
        default: 'light',
    },
});

// 计算实际值数组
const values = computed(() => {
    if (props.value !== null && typeof props.value !== 'undefined') {
        return Array.isArray(props.value) ? props.value : [String(props.value)];
    } else {
        return [];
    }
});

// 根据尺寸计算大小类
const sizeClass = computed(() => {
    return props.size === 'large' ? 'size-large' : props.size === 'small' ? 'size-small' : 'size-default';
});

// 检查是否使用文本模式
const isTextMode = (item: any) => {
    return props.mode === 'text' || item.elTagType === 'default' || item.elTagType === '';
};

// 根据类型返回对应的主题类名
const getThemeClass = (type: string) => {
    return `theme-${props.theme}-${type}`;
};

// 根据类型返回对应的图标类名
const getIconClass = (type: string) => {
    switch (type) {
        case 'primary':
            return 'el-icon-info';
        case 'success':
            return 'el-icon-check';
        case 'info':
            return 'el-icon-message';
        case 'warning':
            return 'el-icon-warning';
        case 'danger':
            return 'el-icon-close';
        default:
            return 'el-icon-info';
    }
};
</script>

<style scoped>
/* ===== 容器样式 ===== */
.dict-tag-container {
    display: inline-flex;
    flex-wrap: wrap;
    gap: 10px;
    align-items: center;
}

/* ===== 基础尺寸类 ===== */
.size-small {
    font-size: 12px;
}

.size-default {
    font-size: 14px;
}

.size-large {
    font-size: 16px;
}

/* ===== 卡片模式基础样式 ===== */
.dict-card {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 6px 12px;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

.dict-card__icon {
    margin-right: 5px;
    display: inline-flex;
    align-items: center;
}

.dict-card.size-small {
    padding: 4px 8px;
}

.dict-card.size-large {
    padding: 8px 16px;
}

/* ===== 胶囊模式基础样式 ===== */
.dict-pill {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 5px 15px;
    border-radius: 20px;
    transition: all 0.3s ease;
}

.dict-pill__icon {
    margin-right: 5px;
    display: inline-flex;
    align-items: center;
}

.dict-pill.size-small {
    padding: 3px 10px;
    border-radius: 12px;
}

.dict-pill.size-large {
    padding: 7px 20px;
    border-radius: 24px;
}

/* ===== 徽章模式基础样式 ===== */
.dict-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 20px;
    height: 20px;
    padding: 0 6px;
    border-radius: 10px;
    font-weight: 500;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    transition: all 0.3s ease;
}

.dict-badge.size-small {
    min-width: 16px;
    height: 16px;
    padding: 0 4px;
    font-size: 10px;
    border-radius: 8px;
}

.dict-badge.size-large {
    min-width: 24px;
    height: 24px;
    padding: 0 8px;
    font-size: 14px;
    border-radius: 12px;
}

/* =============== 基于主题色的生态主题 (eco) =============== */
/* 主色: #4ea259 (您的主题色) */

/* 卡片模式 - 生态主题 */
.dict-card.theme-eco-primary {
    background-color: rgba(78, 162, 89, 0.1);
    color: #4ea259;
    border: 1px solid rgba(78, 162, 89, 0.2);
}

.dict-card.theme-eco-success {
    background-color: rgba(78, 162, 89, 0.1);
    color: #4ea259;
    border: 1px solid rgba(78, 162, 89, 0.2);
}

.dict-card.theme-eco-info {
    background-color: rgba(84, 112, 126, 0.1);
    color: #54707e;
    border: 1px solid rgba(84, 112, 126, 0.2);
}

.dict-card.theme-eco-warning {
    background-color: rgba(230, 162, 60, 0.1);
    color: #e6a23c;
    border: 1px solid rgba(230, 162, 60, 0.2);
}

.dict-card.theme-eco-danger {
    background-color: rgba(245, 108, 108, 0.1);
    color: #f56c6c;
    border: 1px solid rgba(245, 108, 108, 0.2);
}

.dict-card.theme-eco-default {
    background-color: rgba(144, 147, 153, 0.1);
    color: #909399;
    border: 1px solid rgba(144, 147, 153, 0.2);
}

/* 胶囊模式 - 生态主题 */
.dict-pill.theme-eco-primary {
    background-color: #4ea259;
    color: white;
}

.dict-pill.theme-eco-success {
    background-color: #4ea259;
    color: white;
}

.dict-pill.theme-eco-info {
    background-color: #54707e;
    color: white;
}

.dict-pill.theme-eco-warning {
    background-color: #e6a23c;
    color: white;
}

.dict-pill.theme-eco-danger {
    background-color: #f56c6c;
    color: white;
}

.dict-pill.theme-eco-default {
    background-color: #909399;
    color: white;
}

/* 徽章模式 - 生态主题 */
.dict-badge.theme-eco-primary {
    background-color: #4ea259;
    color: white;
}

.dict-badge.theme-eco-success {
    background-color: #4ea259;
    color: white;
}

.dict-badge.theme-eco-info {
    background-color: #54707e;
    color: white;
}

.dict-badge.theme-eco-warning {
    background-color: #e6a23c;
    color: white;
}

.dict-badge.theme-eco-danger {
    background-color: #f56c6c;
    color: white;
}

.dict-badge.theme-eco-default {
    background-color: #909399;
    color: white;
}

/* 文本模式 - 生态主题 */
span.theme-eco-primary {
    color: #4ea259;
}

span.theme-eco-success {
    color: #4ea259;
}

span.theme-eco-info {
    color: #606266;
}

span.theme-eco-warning {
    color: #e6a23c;
}

span.theme-eco-danger {
    color: #f56c6c;
}

span.theme-eco-default {
    color: #909399;
}

/* =============== 柔和主题 (soft) =============== */

/* 卡片模式 - 柔和主题 */
.dict-card.theme-soft-primary {
    background-color: #edf6f0;
    color: #3d8a47;
    border: 1px solid #d7e9db;
    border-radius: 6px;
}

.dict-card.theme-soft-success {
    background-color: #edf6f0;
    color: #3d8a47;
    border: 1px solid #d7e9db;
    border-radius: 6px;
}

.dict-card.theme-soft-info {
    background-color: #eff3f5;
    color: #4a6270;
    border: 1px solid #dbe3e7;
    border-radius: 6px;
}

.dict-card.theme-soft-warning {
    background-color: #fdf6ec;
    color: #c88a30;
    border: 1px solid #faecd8;
    border-radius: 6px;
}

.dict-card.theme-soft-danger {
    background-color: #fef0f0;
    color: #d15b5b;
    border: 1px solid #fde2e2;
    border-radius: 6px;
}

.dict-card.theme-soft-default {
    background-color: #f4f4f5;
    color: #7d7d82;
    border: 1px solid #e9e9eb;
    border-radius: 6px;
}

/* 胶囊模式 - 柔和主题 */
.dict-pill.theme-soft-primary {
    background-color: #edf6f0;
    color: #3d8a47;
    border: 1px solid #d7e9db;
}

.dict-pill.theme-soft-success {
    background-color: #edf6f0;
    color: #3d8a47;
    border: 1px solid #d7e9db;
}

.dict-pill.theme-soft-info {
    background-color: #eff3f5;
    color: #4a6270;
    border: 1px solid #dbe3e7;
}

.dict-pill.theme-soft-warning {
    background-color: #fdf6ec;
    color: #c88a30;
    border: 1px solid #faecd8;
}

.dict-pill.theme-soft-danger {
    background-color: #fef0f0;
    color: #d15b5b;
    border: 1px solid #fde2e2;
}

.dict-pill.theme-soft-default {
    background-color: #f4f4f5;
    color: #7d7d82;
    border: 1px solid #e9e9eb;
}

/* 徽章模式 - 柔和主题 */
.dict-badge.theme-soft-primary {
    background-color: #edf6f0;
    color: #3d8a47;
    border: 1px solid #d7e9db;
}

.dict-badge.theme-soft-success {
    background-color: #edf6f0;
    color: #3d8a47;
    border: 1px solid #d7e9db;
}

.dict-badge.theme-soft-info {
    background-color: #eff3f5;
    color: #4a6270;
    border: 1px solid #dbe3e7;
}

.dict-badge.theme-soft-warning {
    background-color: #fdf6ec;
    color: #c88a30;
    border: 1px solid #faecd8;
}

.dict-badge.theme-soft-danger {
    background-color: #fef0f0;
    color: #d15b5b;
    border: 1px solid #fde2e2;
}

.dict-badge.theme-soft-default {
    background-color: #f4f4f5;
    color: #7d7d82;
    border: 1px solid #e9e9eb;
}

/* 文本模式 - 柔和主题 */
span.theme-soft-primary {
    color: #3d8a47;
}

span.theme-soft-success {
    color: #3d8a47;
}

span.theme-soft-info {
    color: #4a6270;
}

span.theme-soft-warning {
    color: #c88a30;
}

span.theme-soft-danger {
    color: #d15b5b;
}

span.theme-soft-default {
    color: #7d7d82;
}

/* =============== 鲜明主题 (vivid) =============== */

/* 卡片模式 - 鲜明主题 */
.dict-card.theme-vivid-primary {
    background-color: #4ea259;
    color: white;
    border-radius: 4px;
    box-shadow: 0 3px 6px rgba(78, 162, 89, 0.3);
    border: none;
}

.dict-card.theme-vivid-success {
    background-color: #4ea259;
    color: white;
    border-radius: 4px;
    box-shadow: 0 3px 6px rgba(78, 162, 89, 0.3);
    border: none;
}

.dict-card.theme-vivid-info {
    background-color: #54707e;
    color: white;
    border-radius: 4px;
    box-shadow: 0 3px 6px rgba(84, 112, 126, 0.3);
    border: none;
}

.dict-card.theme-vivid-warning {
    background-color: #e6a23c;
    color: white;
    border-radius: 4px;
    box-shadow: 0 3px 6px rgba(230, 162, 60, 0.3);
    border: none;
}

.dict-card.theme-vivid-danger {
    background-color: #f56c6c;
    color: white;
    border-radius: 4px;
    box-shadow: 0 3px 6px rgba(245, 108, 108, 0.3);
    border: none;
}

.dict-card.theme-vivid-default {
    background-color: #909399;
    color: white;
    border-radius: 4px;
    box-shadow: 0 3px 6px rgba(144, 147, 153, 0.3);
    border: none;
}

/* 胶囊模式 - 鲜明主题 */
.dict-pill.theme-vivid-primary {
    background-color: #4ea259;
    color: white;
    box-shadow: 0 3px 6px rgba(78, 162, 89, 0.3);
    border: none;
    font-weight: 500;
}

.dict-pill.theme-vivid-success {
    background-color: #4ea259;
    color: white;
    box-shadow: 0 3px 6px rgba(78, 162, 89, 0.3);
    border: none;
    font-weight: 500;
}

.dict-pill.theme-vivid-info {
    background-color: #54707e;
    color: white;
    box-shadow: 0 3px 6px rgba(84, 112, 126, 0.3);
    border: none;
    font-weight: 500;
}

.dict-pill.theme-vivid-warning {
    background-color: #e6a23c;
    color: white;
    box-shadow: 0 3px 6px rgba(230, 162, 60, 0.3);
    border: none;
    font-weight: 500;
}

.dict-pill.theme-vivid-danger {
    background-color: #f56c6c;
    color: white;
    box-shadow: 0 3px 6px rgba(245, 108, 108, 0.3);
    border: none;
    font-weight: 500;
}

.dict-pill.theme-vivid-default {
    background-color: #909399;
    color: white;
    box-shadow: 0 3px 6px rgba(144, 147, 153, 0.3);
    border: none;
    font-weight: 500;
}

/* 徽章模式 - 鲜明主题 */
.dict-badge.theme-vivid-primary {
    background-color: #4ea259;
    color: white;
    box-shadow: 0 2px 4px rgba(78, 162, 89, 0.3);
    border: none;
    font-weight: 600;
}

.dict-badge.theme-vivid-success {
    background-color: #4ea259;
    color: white;
    box-shadow: 0 2px 4px rgba(78, 162, 89, 0.3);
    border: none;
    font-weight: 600;
}

.dict-badge.theme-vivid-info {
    background-color: #54707e;
    color: white;
    box-shadow: 0 2px 4px rgba(84, 112, 126, 0.3);
    border: none;
    font-weight: 600;
}

.dict-badge.theme-vivid-warning {
    background-color: #e6a23c;
    color: white;
    box-shadow: 0 2px 4px rgba(230, 162, 60, 0.3);
    border: none;
    font-weight: 600;
}

.dict-badge.theme-vivid-danger {
    background-color: #f56c6c;
    color: white;
    box-shadow: 0 2px 4px rgba(245, 108, 108, 0.3);
    border: none;
    font-weight: 600;
}

.dict-badge.theme-vivid-default {
    background-color: #909399;
    color: white;
    box-shadow: 0 2px 4px rgba(144, 147, 153, 0.3);
    border: none;
    font-weight: 600;
}

/* 文本模式 - 鲜明主题 */
span.theme-vivid-primary {
    color: #4ea259;
    font-weight: 500;
}

span.theme-vivid-success {
    color: #4ea259;
    font-weight: 500;
}

span.theme-vivid-info {
    color: #54707e;
    font-weight: 500;
}

span.theme-vivid-warning {
    color: #e6a23c;
    font-weight: 500;
}

span.theme-vivid-danger {
    color: #f56c6c;
    font-weight: 500;
}

span.theme-vivid-default {
    color: #909399;
    font-weight: 500;
}

/* =============== El-Tag 主题覆盖 =============== */
:deep(.el-tag.theme-eco-primary.el-tag--primary) {
    --el-tag-bg-color: rgba(78, 162, 89, 0.1);
    --el-tag-border-color: rgba(78, 162, 89, 0.2);
    --el-tag-hover-color: #4ea259;
    --el-tag-text-color: #4ea259;
}

:deep(.el-tag.theme-eco-success.el-tag--success) {
    --el-tag-bg-color: rgba(78, 162, 89, 0.1);
    --el-tag-border-color: rgba(78, 162, 89, 0.2);
    --el-tag-hover-color: #4ea259;
    --el-tag-text-color: #4ea259;
}

:deep(.el-tag.theme-soft-primary.el-tag--primary) {
    --el-tag-bg-color: #edf6f0;
    --el-tag-border-color: #d7e9db;
    --el-tag-hover-color: #3d8a47;
    --el-tag-text-color: #3d8a47;
}

:deep(.el-tag.theme-vivid-primary.el-tag--primary) {
    --el-tag-bg-color: #4ea259;
    --el-tag-border-color: #4ea259;
    --el-tag-hover-color: white;
    --el-tag-text-color: white;
}
</style>
