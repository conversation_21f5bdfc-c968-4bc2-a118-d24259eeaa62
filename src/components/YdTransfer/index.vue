<template>
    <div class="high-performance-transfer">
        <!-- 操作工具栏 -->
        <div class="transfer-operations">
            <el-button
                size="small"
                type="primary"
                @click="handleOperations('selectAllLeft')"
                :disabled="loading.left || !filteredLeftData.length"
            >
                全选左侧
            </el-button>
            <el-button
                size="small"
                type="primary"
                @click="handleOperations('clearRight')"
                :disabled="loading.right || !selectedKeys.length"
            >
                清空右侧
            </el-button>
        </div>

        <!-- 主体内容 -->
        <div class="transfer-container">
            <!-- 左侧面板 -->
            <div class="transfer-panel">
                <div class="panel-header">
                    <div class="panel-title">{{ props.titles?.[0] || '未选项' }}</div>
                    <el-checkbox
                        v-if="!props.remote || filteredLeftData.length"
                        v-model="leftAllChecked"
                        :indeterminate="leftIndeterminate"
                        @change="handleCheckAllChange('left')"
                    >
                        {{ leftCheckedCount }}/{{ filteredLeftData.length }}
                    </el-checkbox>
                </div>

                <!-- 搜索过滤 -->
                <div v-if="props.filterable" class="panel-filter">
                    <el-input
                        v-model="leftFilter"
                        :placeholder="props.filterPlaceholder?.[0] || '请输入搜索内容'"
                        clearable
                        @input="handleFilter('left')"
                    >
                        <template #prefix>
                            <el-icon><Search /></el-icon>
                        </template>
                    </el-input>
                </div>

                <!-- 虚拟列表，高性能渲染 -->
                <div class="panel-body">
                    <div v-if="loading.left" class="loading-container">
                        <el-icon class="is-loading"><Loading /></el-icon>
                        <span>数据加载中...</span>
                    </div>

                    <div v-else-if="!filteredLeftData.length" class="empty-data">暂无数据</div>

                    <VirtualList
                        v-else
                        :data="filteredLeftData"
                        :item-height="props.itemHeight || 40"
                        :buffer="5"
                        :key="`left-${leftRefreshKey}`"
                        ref="leftListRef"
                    >
                        <template #default="{ item }">
                            <div class="transfer-item">
                                <el-checkbox
                                    :model-value="!!leftCheckedMap[getItemKey(item as TransferItem)]"
                                    style="width: 100%"
                                    @update:model-value="(val: boolean) => updateCheckedState('left', item as TransferItem, val)"
                                >
                                    <template v-if="hasCustomRender">
                                        <component :is="renderItemContent(item as TransferItem)" />
                                    </template>
                                    <template v-else>
                                        {{
                                            (item as TransferItem).label ||
                                            (item as TransferItem).name ||
                                            (item as TransferItem)[props.rowKey as string]
                                        }}
                                    </template>
                                </el-checkbox>
                            </div>
                        </template>
                    </VirtualList>

                    <!-- 分页控件 -->
                    <div v-if="props.pagination" class="panel-pagination">
                        <el-pagination
                            v-model:current-page="leftPagination.current"
                            v-model:page-size="leftPagination.pageSize"
                            :total="leftPagination.total"
                            @current-change="handlePageChange('left')"
                            layout="prev, pager, next"
                            small
                        />
                    </div>
                </div>
            </div>

            <!-- 中间操作区 -->
            <div class="transfer-buttons">
                <el-button
                    type="primary"
                    :disabled="!leftCheckedCount || loading.processing"
                    @click="handleTransfer('right')"
                >
                    <el-icon><ArrowRight /></el-icon>
                </el-button>
                <el-button
                    type="primary"
                    :disabled="!rightCheckedCount || loading.processing"
                    @click="handleTransfer('left')"
                >
                    <el-icon><ArrowLeft /></el-icon>
                </el-button>
            </div>

            <!-- 右侧面板 -->
            <div class="transfer-panel">
                <div class="panel-header">
                    <div class="panel-title">{{ props.titles?.[1] || '已选项' }}</div>
                    <el-checkbox
                        v-model="rightAllChecked"
                        :indeterminate="rightIndeterminate"
                        @change="handleCheckAllChange('right')"
                    >
                        {{ rightCheckedCount }}/{{ filteredRightData.length }}
                    </el-checkbox>
                </div>

                <!-- 搜索过滤 -->
                <div v-if="props.filterable" class="panel-filter">
                    <el-input
                        v-model="rightFilter"
                        :placeholder="props.filterPlaceholder?.[1] || '请输入搜索内容'"
                        clearable
                        @input="handleFilter('right')"
                    >
                        <template #prefix>
                            <el-icon><Search /></el-icon>
                        </template>
                    </el-input>
                </div>

                <!-- 右侧内容区 -->
                <div class="panel-body">
                    <div v-if="loading.right" class="loading-container">
                        <el-icon class="is-loading"><Loading /></el-icon>
                        <span>数据加载中...</span>
                    </div>

                    <div v-else-if="!filteredRightData.length" class="empty-data">暂无数据</div>

                    <VirtualList
                        v-else
                        :data="filteredRightData"
                        :item-height="props.itemHeight || 40"
                        :buffer="5"
                        :key="`right-${rightRefreshKey}`"
                        ref="rightListRef"
                    >
                        <template #default="{ item }">
                            <div class="transfer-item">
                                <el-checkbox
                                    :model-value="!!rightCheckedMap[getItemKey(item as TransferItem)]"
                                    @update:model-value="(val: boolean) => updateCheckedState('right', item as TransferItem, val)"
                                >
                                    <template v-if="hasCustomRender">
                                        <component :is="renderItemContent(item as TransferItem)" />
                                    </template>
                                    <template v-else>
                                        {{
                                            (item as TransferItem).label ||
                                            (item as TransferItem).name ||
                                            (item as TransferItem)[props.rowKey as string]
                                        }}
                                    </template>
                                </el-checkbox>
                            </div>
                        </template>
                    </VirtualList>

                    <!-- 分页控件 -->
                    <div v-if="props.pagination" class="panel-pagination">
                        <el-pagination
                            v-model:current-page="rightPagination.current"
                            v-model:page-size="rightPagination.pageSize"
                            :total="rightPagination.total"
                            @current-change="handlePageChange('right')"
                            layout="prev, pager, next"
                            small
                        />
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick, watch, PropType } from 'vue';
import { Search, Loading, ArrowLeft, ArrowRight } from '@element-plus/icons-vue';
import VirtualList from '../VirtualList/index.vue'; // 虚拟滚动组件

// 定义TransferItem接口
interface TransferItem {
    [key: string]: any;
    label?: string;
    name?: string;
}

// 定义组件属性
const props = defineProps({
    // 数据源
    dataSource: {
        type: Array as PropType<TransferItem[]>,
        required: true,
    },
    // v-model绑定的值
    modelValue: {
        type: Array as PropType<TransferItem[]>,
        default: () => [],
    },
    // 唯一标识
    rowKey: {
        type: [String, Function] as PropType<string | ((item: TransferItem) => string)>,
        required: true,
    },
    // 是否启用虚拟滚动
    virtualScroll: {
        type: Boolean,
        default: true,
    },
    // 项目高度(虚拟滚动需要)
    itemHeight: {
        type: Number,
        default: 40,
    },
    // 标题
    titles: {
        type: Array as PropType<string[]>,
        default: () => ['未选项', '已选项'],
    },
    // 是否可过滤
    filterable: {
        type: Boolean,
        default: true,
    },
    // 过滤方法
    filterMethod: {
        type: Function as PropType<(query: string, item: TransferItem) => boolean>,
        default: (query: string, item: TransferItem) => {
            if (!query) return true;
            return String(item.label || item.name || '')
                .toLowerCase()
                .includes(query.toLowerCase());
        },
    },
    // 过滤框占位符
    filterPlaceholder: {
        type: Array as PropType<string[]>,
        default: () => ['请输入搜索内容', '请输入搜索内容'],
    },
    // 是否启用分页
    pagination: {
        type: Boolean,
        default: false,
    },
    // 是否远程加载
    remote: {
        type: Boolean,
        default: false,
    },
    // 远程加载方法
    remoteMethod: {
        type: Function as PropType<(params: Record<string, any>) => Promise<Record<string, any>>>,
        default: null,
    },
    // 自定义渲染项
    renderItem: {
        type: Function as PropType<(item: TransferItem) => any>,
        default: null,
    },
});

const emit = defineEmits(['update:modelValue', 'change', 'select-change']);

// 虚拟列表引用
const leftListRef = ref(null);
const rightListRef = ref(null);

// 刷新键，用于强制重新渲染虚拟列表
const leftRefreshKey = ref(0);
const rightRefreshKey = ref(0);

// 状态管理
const leftFilter = ref('');
const rightFilter = ref('');
const leftCheckedMap = ref<Record<string, boolean>>({});
const rightCheckedMap = ref<Record<string, boolean>>({});
const loading = ref({
    left: false,
    right: false,
    processing: false,
});
const isLocked = ref(false); // 操作锁，防止用户在处理中进行操作

// 分页状态
const leftPagination = ref({
    current: 1,
    pageSize: 50,
    total: 0,
});

const rightPagination = ref({
    current: 1,
    pageSize: 50,
    total: 0,
});

// 检查是否有自定义渲染函数
const hasCustomRender = computed(() => {
    return props.renderItem !== null;
});

const renderItemContent = (item: TransferItem) => {
    if (props.renderItem && typeof props.renderItem === 'function') {
        return props.renderItem(item);
    }
    return undefined;
};

// 获取项目的唯一键
const getItemKey = (item: TransferItem): string => {
    if (typeof props.rowKey === 'function') {
        return props.rowKey(item);
    }
    return String(item[props.rowKey as string]);
};

// 已选中的键集合
const selectedKeys = computed(() => {
    return props.modelValue.map((item) => getItemKey(item));
});

// 构建映射表以提高查找效率
const dataMap = computed(() => {
    const map = new Map<string, TransferItem>();
    props.dataSource.forEach((item) => {
        map.set(getItemKey(item), item);
    });
    return map;
});

// 左侧数据
const leftData = computed(() => {
    if (props.remote) {
        // 远程模式下直接使用当前加载的数据
        return props.dataSource.filter((item) => !selectedKeys.value.includes(getItemKey(item)));
    } else {
        // 本地模式下过滤出未选中的数据
        return props.dataSource.filter((item) => !selectedKeys.value.includes(getItemKey(item)));
    }
});

// 右侧数据
const rightData = computed(() => {
    if (props.remote) {
        return props.modelValue;
    } else {
        return props.modelValue.map((key) => {
            const keyStr = typeof key === 'object' ? getItemKey(key) : String(key);
            return dataMap.value.get(keyStr) || key;
        });
    }
});

// 过滤后的左侧数据
const filteredLeftData = computed(() => {
    if (!leftFilter.value) return leftData.value;
    return leftData.value.filter((item) => props.filterMethod(leftFilter.value, item));
});

// 过滤后的右侧数据
const filteredRightData = computed(() => {
    if (!rightFilter.value) return rightData.value;
    return rightData.value.filter((item) => props.filterMethod(rightFilter.value, item));
});

// 左侧选中状态计算
const leftCheckedCount = computed(() => {
    return Object.values(leftCheckedMap.value).filter(Boolean).length;
});

// 右侧选中状态计算
const rightCheckedCount = computed(() => {
    return Object.values(rightCheckedMap.value).filter(Boolean).length;
});

// 全选状态计算
const leftAllChecked = computed({
    get: () => {
        return filteredLeftData.value.length > 0 && leftCheckedCount.value === filteredLeftData.value.length;
    },
    set: (val) => {
        const newMap: Record<string, boolean> = {};
        filteredLeftData.value.forEach((item) => {
            newMap[getItemKey(item)] = val;
        });
        leftCheckedMap.value = newMap;
    },
});

const rightAllChecked = computed({
    get: () => {
        return filteredRightData.value.length > 0 && rightCheckedCount.value === filteredRightData.value.length;
    },
    set: (val) => {
        const newMap: Record<string, boolean> = {};
        filteredRightData.value.forEach((item) => {
            newMap[getItemKey(item)] = val;
        });
        rightCheckedMap.value = newMap;
    },
});

// 部分选中状态
const leftIndeterminate = computed(() => {
    return leftCheckedCount.value > 0 && leftCheckedCount.value < filteredLeftData.value.length;
});

const rightIndeterminate = computed(() => {
    return rightCheckedCount.value > 0 && rightCheckedCount.value < filteredRightData.value.length;
});

// 更新单个选中状态
const updateCheckedState = (direction: 'left' | 'right', item: TransferItem, value: boolean) => {
    const key = getItemKey(item);

    if (direction === 'left') {
        // 创建新对象以确保响应性
        const newMap: Record<string, boolean> = { ...leftCheckedMap.value };
        newMap[key] = value;
        leftCheckedMap.value = newMap;
    } else {
        const newMap: Record<string, boolean> = { ...rightCheckedMap.value };
        newMap[key] = value;
        rightCheckedMap.value = newMap;
    }

    // 发出选中变化事件
    emitSelectChange(direction);
};

// 发出选中变化事件
const emitSelectChange = (direction: 'left' | 'right', item?: TransferItem) => {
    emit(
        'select-change',
        direction === 'left'
            ? filteredLeftData.value.filter((listItem) => leftCheckedMap.value[getItemKey(listItem)])
            : filteredRightData.value.filter((listItem) => rightCheckedMap.value[getItemKey(listItem)]),
        direction,
        item
    );
};

// 处理过滤操作
const handleFilter = (direction: 'left' | 'right') => {
    // 重置选中状态
    if (direction === 'left') {
        leftCheckedMap.value = {};
    } else {
        rightCheckedMap.value = {};
    }
};

// 处理全选/取消全选
const handleCheckAllChange = (direction: 'left' | 'right') => {
    // 通过计算属性自动处理了勾选状态
    emitSelectChange(direction);
};

// 强制刷新一侧的列表
const forceRefreshList = (direction: 'left' | 'right') => {
    if (direction === 'left') {
        leftRefreshKey.value++;
    } else {
        rightRefreshKey.value++;
    }
};

// 重置选中状态
const resetCheckedState = (direction: 'left' | 'right') => {
    if (direction === 'left') {
        leftCheckedMap.value = Object.create(null) as Record<string, boolean>; // 使用Object.create(null)创建无原型对象
    } else {
        rightCheckedMap.value = Object.create(null) as Record<string, boolean>;
    }

    // 强制刷新列表
    forceRefreshList(direction);
};

// 处理快捷操作
const handleOperations = (operation: string) => {
    if (loading.value.processing || isLocked.value) return;

    isLocked.value = true;
    loading.value.processing = true;

    if (operation === 'selectAllLeft') {
        // 全选左侧
        leftAllChecked.value = true;

        // 使用setTimeout确保UI更新
        setTimeout(() => {
            handleTransfer('right');
        }, 50);
    } else if (operation === 'clearRight') {
        // 清空右侧

        // 使用延迟执行优化大量数据操作
        setTimeout(() => {
            const newValue: TransferItem[] = [];
            emit('update:modelValue', newValue);
            emit('change', newValue, 'left', selectedKeys.value);

            // 清空右侧选中状态
            resetCheckedState('right');

            setTimeout(() => {
                isLocked.value = false;
                loading.value.processing = false;
            }, 100);
        }, 50);
    }
};

// 处理穿梭操作
const handleTransfer = (direction: 'left' | 'right') => {
    if (loading.value.processing && !isLocked.value) return; // 防止重复点击
    loading.value.processing = true;

    // 设置要操作的面板的加载状态
    loading.value[direction === 'right' ? 'left' : 'right'] = true;

    // 延迟执行以让UI先更新
    setTimeout(() => {
        // 准备数据
        if (direction === 'right') {
            // 获取左侧选中的项目
            const leftCheckedItems = filteredLeftData.value.filter((item) => leftCheckedMap.value[getItemKey(item)]);
            const selectedItemKeys = leftCheckedItems.map((item) => getItemKey(item));

            // 清空左侧选中状态
            resetCheckedState('left');

            // 更新模型
            const newValue = [...props.modelValue, ...leftCheckedItems];
            emit('update:modelValue', newValue);
            emit('change', newValue, direction, selectedItemKeys);

            // 强制刷新左侧列表
            forceRefreshList('left');
        } else {
            // 处理从右向左的移动
            const rightCheckedItems = filteredRightData.value.filter((item) => rightCheckedMap.value[getItemKey(item)]);
            const selectedItemKeys = rightCheckedItems.map((item) => getItemKey(item));

            // 清空右侧选中状态
            resetCheckedState('right');

            // 从右侧移除选中项
            const newValue = props.modelValue.filter((item) => !selectedItemKeys.includes(getItemKey(item)));

            // 更新模型
            emit('update:modelValue', newValue);
            emit('change', newValue, direction, selectedItemKeys);

            // 强制刷新右侧列表
            forceRefreshList('right');
        }

        // 延迟关闭加载状态，给UI足够的时间更新
        setTimeout(() => {
            loading.value[direction === 'right' ? 'left' : 'right'] = false;
            loading.value.processing = false;
            isLocked.value = false;
        }, 200);
    }, 50);
};

// 处理分页变化
const handlePageChange = (direction: 'left' | 'right') => {
    if (props.remote && props.remoteMethod) {
        const params = {
            page: direction === 'left' ? leftPagination.value.current : rightPagination.value.current,
            pageSize: direction === 'left' ? leftPagination.value.pageSize : rightPagination.value.pageSize,
            filter: direction === 'left' ? leftFilter.value : rightFilter.value,
        };

        loading.value[direction] = true;

        props
            .remoteMethod(params)
            .then((result: Record<string, any>) => {
                // 处理远程加载返回的数据
                if (direction === 'left') {
                    leftPagination.value.total = result.total || 0;
                } else {
                    rightPagination.value.total = result.total || 0;
                }

                loading.value[direction] = false;
            })
            .catch(() => {
                loading.value[direction] = false;
            });
    }
};

// 清理无效的选中状态
const cleanCheckedState = () => {
    // 清理左侧选中状态
    const newLeftCheckedMap: Record<string, boolean> = {};
    filteredLeftData.value.forEach((item) => {
        const key = getItemKey(item);
        if (leftCheckedMap.value[key]) {
            newLeftCheckedMap[key] = true;
        }
    });
    leftCheckedMap.value = newLeftCheckedMap;

    // 清理右侧选中状态
    const newRightCheckedMap: Record<string, boolean> = {};
    filteredRightData.value.forEach((item) => {
        const key = getItemKey(item);
        if (rightCheckedMap.value[key]) {
            newRightCheckedMap[key] = true;
        }
    });
    rightCheckedMap.value = newRightCheckedMap;
};

// 监听数据源和过滤后的数据变化
watch(
    [() => props.dataSource, () => props.modelValue],
    () => {
        // 延迟执行清理，确保计算属性已更新
        nextTick(cleanCheckedState);
    },
    { deep: true }
);

// 监听过滤器变化
watch([leftFilter, rightFilter], () => {
    // 当过滤器变化时，强制刷新对应的列表
    if (leftFilter.value !== '') {
        forceRefreshList('left');
    }
    if (rightFilter.value !== '') {
        forceRefreshList('right');
    }
});
const clearSearch = () => {
    leftFilter.value = '';
    rightFilter.value = '';
};

// 初始化
onMounted(() => {
    // 如果是远程模式，加载初始数据
    if (props.remote && props.remoteMethod) {
        loading.value.left = true;
        loading.value.right = true;

        // 加载左侧数据
        props
            .remoteMethod({
                page: 1,
                pageSize: leftPagination.value.pageSize,
                filter: '',
            })
            .then((result: Record<string, any>) => {
                leftPagination.value.total = result.total || 0;
                loading.value.left = false;
            })
            .catch(() => {
                loading.value.left = false;
            });

        // 加载右侧数据（如果需要）
        if (props.modelValue.length) {
            props
                .remoteMethod({
                    page: 1,
                    pageSize: rightPagination.value.pageSize,
                    filter: '',
                    selected: true,
                })
                .then((result: Record<string, any>) => {
                    rightPagination.value.total = result.total || 0;
                    loading.value.right = false;
                })
                .catch(() => {
                    loading.value.right = false;
                });
        } else {
            loading.value.right = false;
        }
    }
});
defineExpose({
    clearSearch,
});
</script>

<style scoped>
.high-performance-transfer {
    display: flex;
    flex-direction: column;
    width: 100%;
}

.transfer-operations {
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;
}

.transfer-container {
    display: flex;
    width: 100%;
    height: 400px;
}

.transfer-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    overflow: hidden;
}

.transfer-buttons {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 0 10px;
}

.transfer-buttons .el-button {
    margin: 5px 0;
}

.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 40px;
    padding: 0 15px;
    border-bottom: 1px solid #e4e7ed;
    background-color: #f5f7fa;
}

.panel-filter {
    padding: 8px;
    background-color: #f5f7fa;
    border-bottom: 1px solid #e4e7ed;
}

.panel-body {
    flex: 1;
    overflow: auto;
    position: relative;
}

.transfer-item {
    padding: 8px 15px;
    display: flex;
    align-items: center;
}

.transfer-item:hover {
    background-color: #f5f7fa;
}

.loading-container,
.empty-data {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #909399;
}

.loading-container .el-icon {
    font-size: 24px;
    margin-bottom: 8px;
}

.panel-pagination {
    padding: 8px;
    display: flex;
    justify-content: center;
    border-top: 1px solid #e4e7ed;
    background-color: #f5f7fa;
}

.processing-indicator {
    display: flex;
    align-items: center;
    margin-top: 10px;
    padding: 8px 12px;
    background-color: #ecf5ff;
    border-radius: 4px;
    color: #409eff;
}

.processing-indicator .el-icon {
    margin-right: 8px;
    font-size: 16px;
}
</style>
