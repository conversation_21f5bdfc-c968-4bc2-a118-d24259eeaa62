<template>
    <div class="demo-container">
        <remote-select
            v-model="selectedValue"
            :fetch-options="fetchOptions"
            :fetch-detail="fetchDetail"
            value-key="id"
            label-key="name"
            placeholder="请选择用户"
            @change="handleChange"
        />
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import RemoteSelect from '../index';

const selectedValue = ref();

interface BaseOption {
    [key: string]: any;
}

const fetchOptions = async (query: string): Promise<BaseOption[]> => {
    // 模拟API请求
    const response = await fetch(`/api/users?query=${query}`);
    const data = await response.json();
    return data;
};

const fetchDetail = async (id: string): Promise<BaseOption> => {
    // 模拟API请求
    const response = await fetch(`/api/users/${id}`);
    const data = await response.json();
    return data;
};

const handleChange = (value: any, option: BaseOption) => {
    // eslint-disable-next-line no-console
    console.log('Selected:', value, option);
};
</script>
