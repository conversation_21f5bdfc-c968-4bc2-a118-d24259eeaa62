<template>
    <el-select
        ref="selectRef"
        v-model="innerValue"
        :loading="isLoading"
        :disabled="disabled"
        :clearable="clearable"
        :multiple="multiple"
        :placeholder="placeholder"
        :filterable="searchable"
        :remote="searchable"
        :remote-method="handleSearch"
        :size="size"
        @change="handleChange"
        @clear="handleClear"
        @focus="handleFocus"
        @blur="handleBlur"
    >
        <el-option
            v-for="option in options"
            :key="option[valueKey]"
            :label="option[labelKey]"
            :value="option[valueKey]"
        >
            <slot name="option" :option="option">
                <template v-if="optionRender">
                    <component :is="optionRender" :option="option" />
                </template>
                <template v-else>
                    {{ option[labelKey] }}
                </template>
            </slot>
        </el-option>
    </el-select>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { debounce } from 'lodash-es';
import type { ElSelect } from 'element-plus';
import type { VNode } from 'vue';

// 直接在组件中定义类型
interface Option {
    [key: string]: any;
}

interface Props {
    modelValue: any;
    fetchOptions: (query: string) => Promise<Option[]>;
    fetchDetail?: (value: any) => Promise<Option>;
    valueKey?: string;
    labelKey?: string;
    searchable?: boolean;
    clearable?: boolean;
    multiple?: boolean;
    minQueryLength?: number;
    debounceTime?: number;
    placeholder?: string;
    loading?: boolean;
    disabled?: boolean;
    size?: 'small' | 'default' | 'large';
    optionRender?: (option: Option) => VNode;
}

type Emits = {
    'update:modelValue': [value: any];
    change: [value: any, option: Option | Option[]];
    search: [query: string];
    focus: [event: FocusEvent];
    blur: [event: FocusEvent];
    clear: [];
};

const props = withDefaults(defineProps<Props>(), {
    valueKey: 'value',
    labelKey: 'label',
    searchable: true,
    clearable: true,
    multiple: false,
    minQueryLength: 1,
    debounceTime: 300,
    size: 'default',
});

const emit = defineEmits<Emits>();

// refs
const selectRef = ref<InstanceType<typeof ElSelect>>();
const options = ref<Option[]>([]);
const isLoading = ref(false);
const lastQuery = ref('');

// computed
const innerValue = computed({
    get: () => props.modelValue,
    set: (val) => emit('update:modelValue', val),
});

// methods
const setLoading = (status: boolean) => {
    isLoading.value = status;
};

const fetchData = async (query: string) => {
    if (query.length < props.minQueryLength) {
        options.value = [];
        return;
    }

    try {
        setLoading(true);
        const data = await props.fetchOptions(query);
        options.value = data;
        lastQuery.value = query;
    } catch (error) {
        console.error('Failed to fetch options:', error);
        options.value = [];
    } finally {
        setLoading(false);
    }
};

const handleSearch = debounce(async (query: string) => {
    emit('search', query);
    await fetchData(query);
}, props.debounceTime);

const fetchDetailData = async (value: any) => {
    if (!props.fetchDetail || !value) return;

    try {
        setLoading(true);
        const detail = await props.fetchDetail(value);
        if (detail) {
            options.value = [detail];
        }
    } catch (error) {
        console.error('Failed to fetch detail:', error);
    } finally {
        setLoading(false);
    }
};

const handleChange = (value: any) => {
    const selected = props.multiple
        ? options.value.filter((opt) => value.includes(opt[props.valueKey]))
        : options.value.find((opt) => opt[props.valueKey] === value);

    if (selected) {
        emit('change', value, selected);
    }
};

const handleClear = () => {
    emit('clear');
    options.value = [];
};

const handleFocus = (e: FocusEvent) => {
    emit('focus', e);
    if (lastQuery.value) {
        fetchData(lastQuery.value);
    }
};

const handleBlur = (e: FocusEvent) => {
    emit('blur', e);
};

// exposed methods
const focus = () => {
    selectRef.value?.focus();
};

const blur = () => {
    selectRef.value?.blur();
};

const clearValue = () => {
    innerValue.value = props.multiple ? [] : undefined;
    handleClear();
};

const refreshOptions = async () => {
    if (lastQuery.value) {
        await fetchData(lastQuery.value);
    } else if (props.modelValue && props.fetchDetail) {
        await fetchDetailData(props.modelValue);
    }
};

// watch
watch(
    () => props.modelValue,
    async (newValue) => {
        if (newValue && props.fetchDetail && options.value.length === 0) {
            await fetchDetailData(newValue);
        }
    },
    { immediate: true }
);

// expose
defineExpose({
    focus,
    blur,
    clearValue,
    refreshOptions,
});
</script>
