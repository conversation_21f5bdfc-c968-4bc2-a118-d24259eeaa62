<template>
    <el-cascader
        :options="areas"
        :disabled="disabled"
        :props="optionProps"
        v-model="selectedOptions"
        filterable
        @change="handleChange"
    >
        <template #default="{ data }">
            <span>{{ data.name }}</span>
            <svg
                v-if="data.hot === '1'"
                t="1708145002710"
                class="icon ml-4"
                viewBox="0 0 1024 1024"
                version="1.1"
                xmlns="http://www.w3.org/2000/svg"
                p-id="6040"
                width="12"
                height="12"
            >
                <path
                    d="M760.591059 332.739765L681.682824 409.6s0-307.2-263.107765-409.539765c0 0-26.262588 281.6-157.816471 383.939765-131.553882 102.460235-394.721882 409.6 131.433412 639.939765 0 0-263.168-281.6 78.908235-486.27953 0 0-26.322824 102.339765 105.231059 204.8 131.614118 102.4 0 281.6 0 281.6s631.506824-153.6 184.32-691.260235z"
                    fill="#EA322B"
                    p-id="6041"
                ></path>
            </svg>
        </template>
    </el-cascader>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue';
import { fetchTree } from '/@/api/admin/sysArea';
import type { CascaderProps, CascaderNode, CascaderOption } from 'element-plus';

interface AreaData extends CascaderOption {
    id?: number;
    name: string;
    adcode: string;
    hot?: string;
    children?: AreaData[];
}

const emit = defineEmits<{
    'update:modelValue': [value: string];
    change: [value: string];
    'change-with-detail': [value: { provinceCode: string; cityCode: string; districtCode: string }];
}>();

const props = withDefaults(
    defineProps<{
        modelValue?: string;
        type?: number;
        disabled?: boolean;
        plus?: boolean;
        lazy?: boolean;
    }>(),
    {
        type: 4,
        disabled: false,
        plus: false,
        lazy: true,
    }
);

// 所有省市区的数据
const areas = ref<AreaData[]>();

// 定义optionProps
const optionProps = {
    checkStrictly: props.plus,
    label: 'name',
    value: 'adcode',
    lazy: props.lazy,
    lazyLoad: async (node: CascaderNode, resolve: (data: AreaData[]) => void) => {
        // 初始化数据
        if (node.level < props.type) {
            const { data } = await fetchTree({
                pid: (node.data as AreaData)?.id || 100000,
            });
            resolve(data);
        } else {
            resolve([]);
        }
    },
} satisfies CascaderProps;

// 计算属性selectedOptions
const selectedOptions = computed({
    get: () => {
        return props.modelValue?.split(',');
    },
    set: (val: string[] | undefined) => {
        emit('update:modelValue', val?.join(',') || '');
    },
});

// 处理change事件的函数
const handleChange = (value: string[]) => {
    // 原有的change事件
    emit('change', value?.join(',') || '');

    // 构建地区编码对象
    const areaCodes = {
        provinceCode: value[0] || '',
        cityCode: value[1] || '',
        districtCode: value[2] || '',
    };

    emit('change-with-detail', areaCodes);
};

// 监听modelValue变化，处理外部清空
watch(
    () => props.modelValue,
    (newVal) => {
        if (!newVal) {
            selectedOptions.value = [];
            areas.value = [];
        }
    }
);

// 初始化数据
onMounted(async () => {
    if (!props.lazy) {
        const { data } = await fetchTree({ type: props.type });
        areas.value = data;
    }
});
</script>
