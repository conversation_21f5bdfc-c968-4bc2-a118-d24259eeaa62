# YdAutocomplete 组件使用文档

## 概述

`YdAutocomplete` 是一个功能强大的自动完成输入组件，基于 Element Plus 的 el-autocomplete 封装，提供了更为灵活的数据处理方式和多种行为模式。该组件支持远程数据搜索、数据回显、自定义渲染等功能，适用于各种需要智能提示输入的场景。

## 特性

-   支持三种行为模式：严格选择、自由输入、ID 列表
-   支持远程搜索和本地数据过滤
-   支持自定义数据键值映射
-   支持数据回显功能
-   支持自定义选项渲染
-   支持创建不存在的选项
-   防抖输入优化性能

## 安装

确保您的项目中已安装以下依赖：

```bash
npm install vue element-plus lodash-es
```

## 基本用法

### 引入组件

```vue
<script setup>
import YdAutocomplete from '@/components/YdAutocomplete/index.vue';
</script>

<template>
    <YdAutocomplete v-model="selected" :request-url="fetchData" placeholder="请输入搜索关键词" />
</template>
```

### 数据请求方法示例

```javascript
// 请求数据的方法必须返回一个Promise，且结果包含data字段
const fetchData = async (params) => {
    try {
        const response = await fetch(`/api/search?keyword=${params.keyword}`);
        const result = await response.json();
        return { data: result.items };
    } catch (error) {
        console.error('搜索失败', error);
        return { data: [] };
    }
};
```

## 行为模式

### 严格选择模式 (strict)

用户只能从下拉菜单中选择一个选项，不能输入自定义内容。选择后返回完整的数据对象。

```vue
<YdAutocomplete v-model="selected" behavior="strict" :request-url="fetchData" />
```

### 自由输入模式 (free)

用户可以选择下拉菜单中的选项，也可以输入自定义内容。可根据设置决定是否创建新选项。

```vue
<YdAutocomplete v-model="inputText" behavior="free" :request-url="fetchData" :allow-create="true" />
```

### ID 列表模式 (idList)

专为需要返回 ID 或 ID 列表的场景设计。选择后返回包含 ID 的数组。

```vue
<YdAutocomplete v-model="selectedIds" behavior="idList" :request-url="fetchData" />
```

## 属性

| 属性名                  | 类型                       | 默认值         | 说明                                                  |
| ----------------------- | -------------------------- | -------------- | ----------------------------------------------------- |
| behavior                | String                     | 'strict'       | 组件行为模式，可选值：'strict'、'free'、'idList'      |
| modelValue              | String/Number/Array/Object | ''             | 组件的值，根据 behavior 类型会有不同数据类型          |
| requestUrl              | Function                   | -              | 获取数据的 API 函数，必须返回包含 data 字段的 Promise |
| requestName             | String                     | 'keyword'      | 发送给 API 的搜索参数名                               |
| initialOptions          | Array                      | []             | 初始候选项数据                                        |
| echoContent             | String                     | ''             | 回显内容，用于显示已选择的项                          |
| placeholder             | String                     | '请输入关键词' | 输入框占位文本                                        |
| disabled                | Boolean                    | false          | 是否禁用组件                                          |
| clearable               | Boolean                    | true           | 是否可清空                                            |
| popperClass             | String                     | ''             | 下拉菜单的自定义类名                                  |
| clearNonSelectionOnBlur | Boolean                    | false          | 当失去焦点且未选择项时是否清空输入(仅 strict 模式)    |
| dataKey                 | String                     | 'id'           | 数据项中用作值的属性名                                |
| dataLabel               | String                     | 'label'        | 数据项中用作显示文本的属性名                          |
| getLabel                | Function                   | null           | 自定义标签生成函数                                    |
| allowCreate             | Boolean                    | false          | 是否允许创建新项(仅 free 模式)                        |
| debounceTime            | Number                     | 300            | 延迟获取建议的时间(毫秒)                              |
| remote                  | Boolean                    | true           | 是否使用远程搜索                                      |

## 事件

| 事件名            | 说明                   | 回调参数           |
| ----------------- | ---------------------- | ------------------ |
| update:modelValue | 当值改变时触发         | (value) 新值       |
| select            | 当选中一个选项时触发   | (item) 选中项      |
| change            | 当选择变更时触发       | (value) 新值       |
| blur              | 当输入框失去焦点时触发 | (value) 输入框的值 |
| input             | 当输入框值变化时触发   | (value) 输入框的值 |
| fetch-error       | 当获取数据失败时触发   | (error) 错误对象   |
| clear             | 当点击清空按钮时触发   | -                  |
| focus             | 当输入框获得焦点时触发 | (event) 原生事件   |

## 插槽

| 插槽名 | 说明           | 插槽作用域      |
| ------ | -------------- | --------------- |
| item   | 自定义选项内容 | { item } 数据项 |
| prefix | 输入框头部内容 | -               |
| suffix | 输入框尾部内容 | -               |

## 高级用法

### 自定义选项渲染

```vue
<YdAutocomplete v-model="selected" :request-url="fetchData">
  <template #item="{ item }">
    <div class="custom-item">
      <img :src="item.avatar" class="avatar" />
      <div class="info">
        <span class="name">{{ item.name }}</span>
        <span class="description">{{ item.description }}</span>
      </div>
    </div>
  </template>
</YdAutocomplete>
```

### 自定义标签生成函数

```vue
<script setup>
import YdAutocomplete from '@/components/YdAutocomplete/index.vue';

const getCustomLabel = (item) => {
    return `${item.name} (${item.code})`;
};
</script>

<template>
    <YdAutocomplete v-model="selected" :request-url="fetchData" :get-label="getCustomLabel" />
</template>
```

### 回显处理

当需要显示已有值但只有 ID 时：

```vue
<YdAutocomplete v-model="userId" :request-url="fetchUserData" :echo-content="userDisplayName" />
```

### 添加前缀/后缀图标

```vue
<YdAutocomplete v-model="selected" :request-url="fetchData">
  <template #prefix>
    <el-icon><Search /></el-icon>
  </template>
  <template #suffix>
    <el-icon v-if="selected"><Check /></el-icon>
  </template>
</YdAutocomplete>
```

## 数据格式

组件默认期望的数据格式如下：

```javascript
{
    data: [
        { id: '1', label: '选项1' },
        { id: '2', label: '选项2' },
        // ...
    ];
}
```

如果您的数据格式不同，可以通过 `dataKey` 和 `dataLabel` 属性进行配置：

```vue
<YdAutocomplete v-model="selected" :request-url="fetchData" data-key="value" data-label="text" />
```

## 注意事项

1. **数据类型一致性**：

    - `strict` 模式下，modelValue 为对象类型或 null
    - `free` 模式下，modelValue 可以是字符串或对象
    - `idList` 模式下，modelValue 总是数组类型

2. **请求函数返回格式**：

    - requestUrl 方法必须返回包含 data 字段的 Promise

3. **性能优化**：
    - 组件内置防抖功能，可通过 debounceTime 属性调整延迟时间
    - 对于大量本地数据，推荐设置 remote 为 false

## 与其他组件的兼容性

`YdAutocomplete` 组件兼容 fuzzy-search 的 API，支持以下方法：

-   `remoteMethod`：设置回显内容
-   `clearOptions`：清空选项列表
-   `setLoading`：设置加载状态

可以通过模板引用获取这些方法：

```vue
<script setup>
import { ref } from 'vue';

const autocompleteRef = ref(null);

const showExistingValue = () => {
    autocompleteRef.value.remoteMethod('已选项的文本');
};
</script>

<template>
    <YdAutocomplete ref="autocompleteRef" v-model="selected" :request-url="fetchData" />
    <button @click="showExistingValue">显示已有值</button>
</template>
```

## 示例

### 简单搜索

```vue
<template>
    <YdAutocomplete v-model="selectedUser" :request-url="searchUsers" placeholder="请输入用户名" />
</template>

<script setup>
import { ref } from 'vue';
import YdAutocomplete from '@/components/YdAutocomplete/index.vue';

const selectedUser = ref(null);

const searchUsers = async (params) => {
    // 模拟API请求
    const response = await fetch(`/api/users?name=${params.keyword}`);
    const result = await response.json();
    return { data: result.users };
};
</script>
```

### 复杂表单场景

```vue
<template>
    <el-form :model="form" label-width="120px">
        <el-form-item label="选择部门">
            <YdAutocomplete
                v-model="form.department"
                behavior="strict"
                :request-url="searchDepartments"
                placeholder="请输入部门名称"
            />
        </el-form-item>

        <el-form-item label="选择标签">
            <YdAutocomplete
                v-model="form.tags"
                behavior="idList"
                :request-url="searchTags"
                placeholder="请输入标签关键词"
            />
        </el-form-item>

        <el-form-item label="备注">
            <YdAutocomplete
                v-model="form.comment"
                behavior="free"
                :allow-create="true"
                :request-url="searchCommonComments"
                placeholder="请输入或选择常用备注"
            />
        </el-form-item>
    </el-form>
</template>

<script setup>
import { reactive } from 'vue';
import YdAutocomplete from '@/components/YdAutocomplete/index.vue';

const form = reactive({
    department: null,
    tags: [],
    comment: '',
});

// 模拟API请求方法
const searchDepartments = async (params) => {
    // 实际实现...
    return {
        data: [
            /* 部门数据 */
        ],
    };
};

const searchTags = async (params) => {
    // 实际实现...
    return {
        data: [
            /* 标签数据 */
        ],
    };
};

const searchCommonComments = async (params) => {
    // 实际实现...
    return {
        data: [
            /* 常用备注数据 */
        ],
    };
};
</script>
```

## 常见问题

### Q: 如何设置默认值？

A: 根据不同的行为模式，设置对应类型的初始值：

```javascript
// strict 模式
const selectedItem = ref({ id: '1', label: '默认选项' });

// free 模式
const inputText = ref('默认文本');

// idList 模式
const selectedIds = ref(['1', '2']);
```

### Q: 如何使用本地数据而非远程请求？

A: 设置 `remote` 为 `false`，并提供一个返回本地数据的请求函数：

```javascript
const localData = [
    { id: '1', label: '选项1' },
    { id: '2', label: '选项2' },
    // ...
];

const getLocalData = () => {
    return Promise.resolve({ data: localData });
};
```

### Q: 如何自定义过滤逻辑？

A: 在请求函数中实现自定义过滤逻辑：

```javascript
const customFilter = (params) => {
    const keyword = params.keyword?.toLowerCase() || '';

    // 自定义过滤逻辑
    const filtered = localData.filter(
        (item) => item.label.toLowerCase().includes(keyword) || item.code.toLowerCase().includes(keyword)
    );

    return Promise.resolve({ data: filtered });
};
```

## 最佳实践

1. 根据业务需求选择合适的行为模式
2. 对于频繁搜索的场景，适当增加 debounceTime 值减少请求次数
3. 使用 dataKey 和 dataLabel 适配不同的数据结构，而不是在请求函数中转换数据格式
4. 对于已知列表项的场景，考虑设置 remote 为 false 以提高性能
5. 使用 getLabel 函数自定义显示内容，使界面更友好
