<template>
    <div class="yd-autocomplete-container">
        <el-autocomplete
            v-bind="$attrs"
            v-model="inputValue"
            :fetch-suggestions="querySearchWithDebounce"
            :trigger-on-focus="true"
            :placeholder="placeholder"
            :disabled="disabled"
            :clearable="clearable"
            :popper-class="popperClass"
            :value-key="internalValueKey"
            @select="handleSelect"
            @input="handleInput"
            @blur="handleBlur"
            @clear="handleClear"
            @focus="handleFocus"
            class="yd-autocomplete"
        >
            <template #default="{ item }">
                <slot name="item" :item="item">
                    <div class="yd-autocomplete-item">
                        {{ item[internalLabelKey] }}
                    </div>
                </slot>
            </template>
            <template #suffix v-if="$slots.suffix">
                <slot name="suffix"></slot>
            </template>
            <template #prefix v-if="$slots.prefix">
                <slot name="prefix"></slot>
            </template>
        </el-autocomplete>
    </div>
</template>

<script setup>
import { ref, watch, onMounted, computed, nextTick } from 'vue';
import { debounce } from 'lodash-es';

const props = defineProps({
    /**
     * 组件行为类型
     * - strict: 严格选择模式，只能选择候选项中的值
     * - free: 自由输入模式，可以输入任意值
     * - idList: ID列表模式，返回ID数组
     */
    behavior: {
        type: String,
        default: 'strict',
        validator: (value) => ['strict', 'free', 'idList'].includes(value),
    },
    /**
     * 组件的值，根据不同behavior类型会有不同的数据类型
     * - strict: 对象类型
     * - free: 字符串或对象类型
     * - idList: 数组类型
     */
    modelValue: {
        type: [String, Number, Array, Object],
        default: '',
    },
    /**
     * 获取数据的API函数，必须返回Promise
     * @param {Object} params - 查询参数
     * @returns {Promise} - 返回包含data字段的结果
     */
    requestUrl: {
        type: Function,
        required: true,
    },
    /**
     * 发送给API的查询参数名
     */
    requestName: {
        type: String,
        default: 'keyword',
    },
    /**
     * 初始候选项数据
     */
    initialOptions: {
        type: Array,
        default: () => [],
    },
    /**
     * 回显内容，用于显示已选择的项
     */
    echoContent: {
        type: String,
        default: '',
    },
    /**
     * 输入框占位文本
     */
    placeholder: {
        type: String,
        default: '请输入关键词',
    },
    /**
     * 是否禁用组件
     */
    disabled: {
        type: Boolean,
        default: false,
    },
    /**
     * 是否可清空
     */
    clearable: {
        type: Boolean,
        default: true,
    },
    /**
     * 下拉菜单的自定义类名
     */
    popperClass: {
        type: String,
        default: '',
    },
    /**
     * 当失去焦点且未选择项时是否清空输入（仅适用于严格选择行为）
     */
    clearNonSelectionOnBlur: {
        type: Boolean,
        default: false,
    },
    /**
     * 数据项中用作值的属性名
     */
    dataKey: {
        type: String,
        default: 'id',
    },
    /**
     * 数据项中用作显示文本的属性名
     */
    dataLabel: {
        type: String,
        default: 'label',
    },
    /**
     * 自定义标签生成函数
     * @param {Object} item - 数据项
     * @returns {String} - 显示文本
     */
    getLabel: {
        type: Function,
        default: null,
    },
    /**
     * 是否允许创建新项（在自由输入行为中有效）
     */
    allowCreate: {
        type: Boolean,
        default: false,
    },
    /**
     * 延迟获取建议的时间（毫秒）
     */
    debounceTime: {
        type: Number,
        default: 300,
    },
    /**
     * 是否使用远程搜索
     */
    remote: {
        type: Boolean,
        default: true,
    },
});

const emit = defineEmits(['update:modelValue', 'select', 'change', 'blur', 'input', 'fetch-error', 'clear', 'focus']);

// 状态变量
const inputValue = ref('');
const selectedItem = ref(null);
const options = ref(props.initialOptions || []);
const loading = ref(false);
const isInitializing = ref(true);

// 计算属性
const internalValueKey = computed(() => props.dataKey);
const internalLabelKey = computed(() => props.dataLabel);

/**
 * 设置加载状态
 * @param {Boolean} status - 加载状态
 */
const setLoading = (status) => {
    loading.value = status;
};

/**
 * 错误处理函数
 * @param {String} message - 错误消息
 * @param {Error} error - 错误对象
 */
const handleError = (message, error) => {
    if (process.env.NODE_ENV !== 'production') {
        // eslint-disable-next-line no-console
        console.error(`YdAutocomplete Error: ${message}`, error);
    }
    emit('fetch-error', error);
};

/**
 * 根据ID获取完整数据对象
 * @param {String|Number} id - 数据项ID
 * @returns {Promise<Object|null>} - 返回数据项或null
 */
const fetchItemById = async (id) => {
    if (!id) return null;

    try {
        setLoading(true);

        // 如果有回显内容，直接使用
        if (props.echoContent && props.modelValue === id) {
            return {
                [props.dataKey]: id,
                [props.dataLabel]: props.echoContent,
            };
        }

        // 否则请求完整数据
        const res = await props.requestUrl({});
        const items = res.data || [];
        const item = items.find((i) => String(i[props.dataKey]) === String(id));

        return item || null;
    } catch (error) {
        handleError('获取数据详情失败', error);
        return null;
    } finally {
        setLoading(false);
    }
};

/**
 * 确保输出一致类型的值
 * @param {Object|null} item - 选中的项或null
 */
const emitConsistentValue = (item) => {
    if (!item) {
        // 无选中项时，根据行为返回一致的空值
        if (props.behavior === 'idList') {
            emit('update:modelValue', []);
        } else if (props.behavior === 'strict') {
            emit('update:modelValue', null);
        } else if (props.behavior === 'free') {
            emit('update:modelValue', '');
        }
        return;
    }

    // 有选中项时，根据行为返回特定类型
    if (props.behavior === 'idList') {
        // ID列表行为下，始终返回包含单个ID的新数组
        const id = item[props.dataKey];
        emit('update:modelValue', [id]);
    } else if (props.behavior === 'strict') {
        // 严格选择行为下，始终返回完整对象
        emit('update:modelValue', item);
    } else {
        // 自由输入行为下，保持类型一致性
        const isObject =
            typeof props.modelValue === 'object' && props.modelValue !== null && !Array.isArray(props.modelValue);

        emit('update:modelValue', isObject ? item : item[props.dataLabel] || String(item[props.dataKey]));
    }
};

/**
 * 初始化输入框内容
 */
const initInputValue = async () => {
    isInitializing.value = true;

    try {
        if (!props.modelValue) {
            inputValue.value = '';
            selectedItem.value = null;

            // 确保初始空值类型一致
            if (props.behavior === 'idList' && !Array.isArray(props.modelValue)) {
                emit('update:modelValue', []);
            } else if (props.behavior === 'strict' && props.modelValue !== null) {
                emit('update:modelValue', null);
            }

            return;
        }

        // 处理ID列表行为下的数组值
        if (props.behavior === 'idList' && Array.isArray(props.modelValue)) {
            if (props.modelValue.length === 0) {
                inputValue.value = '';
                selectedItem.value = null;
            } else {
                // 使用第一个ID获取显示内容
                const firstId = props.modelValue[0];
                const firstItem = await fetchItemById(firstId);

                if (firstItem) {
                    inputValue.value =
                        props.modelValue.length > 1
                            ? `${firstItem[props.dataLabel]} 等${props.modelValue.length}项`
                            : firstItem[props.dataLabel];
                    selectedItem.value = firstItem;
                } else {
                    inputValue.value = `已选择${props.modelValue.length}项`;
                }
            }
            return;
        }

        // 处理对象类型的值
        if (typeof props.modelValue === 'object' && props.modelValue !== null && !Array.isArray(props.modelValue)) {
            inputValue.value = props.getLabel
                ? props.getLabel(props.modelValue)
                : props.modelValue[props.dataLabel] || '';
            selectedItem.value = props.modelValue;
            return;
        }

        // 处理ID值（字符串或数字）
        if (typeof props.modelValue === 'string' || typeof props.modelValue === 'number') {
            // 如果有回显内容，直接使用
            if (props.echoContent) {
                inputValue.value = props.echoContent;
                selectedItem.value = {
                    [props.dataKey]: props.modelValue,
                    [props.dataLabel]: props.echoContent,
                };
            } else {
                // 否则尝试获取完整数据
                const item = await fetchItemById(props.modelValue);
                if (item) {
                    inputValue.value = props.getLabel ? props.getLabel(item) : item[props.dataLabel] || '';
                    selectedItem.value = item;
                } else {
                    inputValue.value = String(props.modelValue);
                    selectedItem.value = null;
                }
            }
            return;
        }

        // 处理其他情况
        inputValue.value = String(props.modelValue);
        selectedItem.value = null;
    } catch (error) {
        handleError('初始化输入值失败', error);
        inputValue.value = '';
        selectedItem.value = null;
    } finally {
        isInitializing.value = false;
    }
};

/**
 * 创建新项
 * @param {String} inputText - 输入文本
 * @returns {Object} - 新建的数据项
 */
const createNewItem = (inputText) => {
    return {
        [props.dataKey]: inputText,
        [props.dataLabel]: inputText,
        isNew: true,
    };
};

/**
 * 清除当前选择并重置状态
 */
const resetSelection = () => {
    selectedItem.value = null;
    if (props.behavior === 'idList') {
        // 仅在用户手动输入并清除了内容时重置
        if (!inputValue.value) {
            emit('update:modelValue', []);
        }
    }
};

/**
 * 回显方法
 * @param {String} name - 回显的文本内容
 */
const remoteMethod = (name) => {
    setTimeout(() => {
        if (props.modelValue) {
            options.value = [
                {
                    [props.dataKey]: props.modelValue,
                    [props.dataLabel]: name,
                },
            ];
            inputValue.value = name;
        }
    });
};

/**
 * 清空选项列表
 */
const clearOptions = () => {
    options.value = [];
};

/**
 * 远程搜索方法
 * @param {String|Object} query - 查询参数
 * @returns {Promise<Array>} - 搜索结果
 */
const remoteMethodRequest = async (query) => {
    try {
        setLoading(true);
        const params = props.remote ? { [props.requestName]: query } : {};
        const res = await props.requestUrl(params);
        options.value = res.data || [];
        return options.value;
    } catch (error) {
        handleError('远程请求失败', error);
        return [];
    } finally {
        setLoading(false);
    }
};

/**
 * 查询建议项
 * @param {String} queryString - 查询字符串
 * @param {Function} callback - 回调函数，用于返回结果
 */
const querySearch = async (queryString, callback) => {
    if (isInitializing.value) {
        callback([]);
        return;
    }

    if (!props.remote && !queryString) {
        // 非远程模式下，直接返回所有数据
        remoteMethodRequest({});
        callback(options.value);
        return;
    }

    if (queryString || !props.remote) {
        try {
            const validatedQuery = queryString?.trim().slice(0, 100);
            const params = props.remote ? { [props.requestName]: validatedQuery } : {};

            setLoading(true);
            const res = await props.requestUrl(params);
            const data = res.data || [];

            // 应用自定义标签
            if (props.getLabel) {
                data.forEach((item) => {
                    item._displayLabel = props.getLabel(item);
                });
            }

            // 如果允许创建并且有查询文本，添加创建选项
            const shouldAddCreateOption =
                props.allowCreate &&
                queryString &&
                props.behavior === 'free' &&
                !data.some((item) => item[props.dataLabel] === queryString);

            if (shouldAddCreateOption) {
                data.unshift({
                    [props.dataKey]: `create_${queryString}`,
                    [props.dataLabel]: `创建: ${queryString}`,
                    isNew: true,
                    rawValue: queryString,
                });
            }

            options.value = data;
            callback(data);
        } catch (error) {
            handleError('获取建议数据失败', error);
            callback([]);
        } finally {
            setLoading(false);
        }
    } else {
        callback([]);
    }
};

// 防抖包装的查询方法
const querySearchWithDebounce = debounce((queryString, callback) => {
    querySearch(queryString, callback);
}, props.debounceTime);

/**
 * 选择建议项处理
 * @param {Object} item - 选中的建议项
 */
const handleSelect = (item) => {
    selectedItem.value = item;
    inputValue.value = props.getLabel ? props.getLabel(item) : item[props.dataLabel];

    // 处理创建项
    if (item.isNew && item.rawValue) {
        const newItem = createNewItem(item.rawValue);
        emitConsistentValue(newItem);
        emit('select', newItem);
        emit('change', newItem);
        return;
    }

    // 根据不同行为发出一致类型的值
    emitConsistentValue(item);
    emit('select', item);
    emit('change', item);
};

/**
 * 输入变化处理
 * @param {String} value - 输入的值
 */
const handleInput = (value) => {
    if (value !== inputValue.value) {
        inputValue.value = value;
    }

    if (isInitializing.value) return;

    // 检查是否需要重置选择状态
    const hasMultipleSelections =
        props.behavior === 'idList' && Array.isArray(props.modelValue) && props.modelValue.length > 1;

    if (hasMultipleSelections) {
        const userChangedSelection = !value.includes('等') && selectedItem.value;
        if (userChangedSelection) {
            resetSelection();
        }
    }

    // 自由输入行为处理
    if (props.behavior === 'free') {
        const matchesSelectedItem =
            selectedItem.value &&
            (selectedItem.value[props.dataLabel] === value ||
                (props.getLabel && props.getLabel(selectedItem.value) === value));

        if (matchesSelectedItem) {
            // 保持类型一致性
            const isObjectModelValue =
                typeof props.modelValue === 'object' && props.modelValue !== null && !Array.isArray(props.modelValue);

            emit('update:modelValue', isObjectModelValue ? selectedItem.value : value);
        } else {
            // 输入不匹配选中项，清除选中状态
            selectedItem.value = null;
            emit('update:modelValue', value);
        }
    } else if (props.behavior === 'strict' || props.behavior === 'idList') {
        // 输入不匹配选中项，清除选中状态但不更新值
        const inputDoesNotMatchSelection =
            selectedItem.value &&
            selectedItem.value[props.dataLabel] !== value &&
            (!props.getLabel || props.getLabel(selectedItem.value) !== value);

        if (inputDoesNotMatchSelection) {
            selectedItem.value = null;
        }
    }

    emit('input', value);
};

/**
 * 失去焦点处理
 */
const handleBlur = async () => {
    if (isInitializing.value) return;

    // 严格选择行为处理
    if (props.behavior === 'strict' && props.clearNonSelectionOnBlur && !selectedItem.value) {
        inputValue.value = '';
        emit('update:modelValue', null);
    }

    // ID列表行为处理
    if (props.behavior === 'idList' && !selectedItem.value && inputValue.value) {
        try {
            setLoading(true);
            const res = await props.requestUrl({ [props.requestName]: inputValue.value });
            const result = res.data || [];

            if (result.length > 0) {
                // 提取ID列表
                const idList = result.map((item) => item[props.dataKey]);

                // 更新显示文本
                if (idList.length > 1) {
                    inputValue.value = `${result[0][props.dataLabel]} 等${idList.length}项`;
                } else {
                    inputValue.value = result[0][props.dataLabel];
                }

                // 根据数量决定返回单个ID还是ID数组
                if (idList.length === 1) {
                    emit('update:modelValue', [idList[0]]);
                    emit('change', result[0]);
                } else {
                    emit('update:modelValue', idList);
                }
            } else if (props.allowCreate) {
                // 如果允许创建且没有匹配结果
                emit('update:modelValue', inputValue.value);
            } else {
                // 没有匹配结果且不允许创建，清空
                inputValue.value = '';
                emit('update:modelValue', []);
            }
        } catch (error) {
            handleError('获取ID列表失败', error);
            inputValue.value = '';
            emit('update:modelValue', []);
        } finally {
            setLoading(false);
        }
    } else if (props.behavior === 'idList' && !selectedItem.value && !inputValue.value) {
        // 无输入也无选中项时，确保返回空数组
        emit('update:modelValue', []);
    }

    // 自由输入行为处理
    if (props.behavior === 'free' && props.allowCreate && inputValue.value && !selectedItem.value) {
        const newItem = createNewItem(inputValue.value);
        emit('update:modelValue', newItem);
    }

    emit('blur', inputValue.value);
};

/**
 * 聚焦处理
 * @param {Event} event - 聚焦事件
 */
const handleFocus = (event) => {
    emit('focus', event);
};

/**
 * 清空输入处理
 */
const handleClear = () => {
    inputValue.value = '';
    selectedItem.value = null;

    // 根据行为发送一致的空值
    if (props.behavior === 'idList') {
        emit('update:modelValue', []);
    } else if (props.behavior === 'strict') {
        emit('update:modelValue', null);
    } else {
        emit('update:modelValue', '');
    }

    emit('clear');
};

// 监听modelValue变化
watch(
    () => props.modelValue,
    async () => {
        await initInputValue();
    },
    { immediate: true }
);

// 监听回显内容变化
watch(
    () => props.echoContent,
    (val) => {
        if (val && props.modelValue) {
            nextTick(() => {
                remoteMethod(val);
            });
        }
    },
    { immediate: true }
);

// 组件挂载时初始化
onMounted(async () => {
    // 如果不是远程模式，初始加载所有数据
    if (!props.remote) {
        remoteMethodRequest();
    }

    await initInputValue();

    // 确保组件更新后状态已正确设置
    nextTick(() => {
        isInitializing.value = false;
    });
});

// 暴露方法，兼容fuzzy-search的API
defineExpose({
    remoteMethod,
    clearOptions,
    setLoading,
});
</script>

<style scoped>
.yd-autocomplete-container {
    width: 100%;
}

.yd-autocomplete {
    width: 100%;
}

.yd-autocomplete-item {
    padding: 4px 8px;
    font-size: 14px;
    cursor: pointer;
}

.yd-autocomplete-item:hover {
    background-color: #f5f7fa;
}
</style>
