<template>
    <div class="upload-box">
        <el-upload
            action="#"
            :id="uuid"
            :class="['upload', self_disabled ? 'disabled' : '', drag ? 'no-border' : '']"
            :multiple="false"
            :disabled="self_disabled"
            :show-file-list="false"
            :before-upload="beforeUpload"
            :http-request="handleHttpUpload"
            :on-error="uploadError"
            :drag="drag"
            :accept="fileType.join(',')"
        >
            <template v-if="_filePath.fullPath">
                <img
                    class="upload-image"
                    :src="_filePath.fullPath.includes('http') ? _filePath.fullPath : baseURL + _filePath.fullPath"
                />
                <div class="upload-handle" @click.stop>
                    <div v-if="!self_disabled" class="handle-icon" @click="editImg">
                        <el-icon :size="props.iconSize">
                            <Edit />
                        </el-icon>
                        <span v-if="!props.iconSize">{{ $t('common.editBtn') }}</span>
                    </div>
                    <div class="handle-icon" @click="imgViewVisible = true">
                        <el-icon :size="props.iconSize">
                            <ZoomIn />
                        </el-icon>
                        <span v-if="!props.iconSize">{{ $t('common.viewBtn') }}</span>
                    </div>
                    <div v-if="!self_disabled" class="handle-icon" @click="deleteImg">
                        <el-icon :size="props.iconSize">
                            <Delete />
                        </el-icon>
                        <span v-if="!props.iconSize">{{ $t('common.delBtn') }}</span>
                    </div>
                </div>
            </template>
            <template v-else-if="uploading">
                <div class="absolute top-0 left-0 w-full h-full" v-loading="true" />
            </template>
            <template v-else>
                <div class="upload-empty">
                    <slot name="empty">
                        <el-icon>
                            <Plus />
                        </el-icon>
                    </slot>
                </div>
            </template>
        </el-upload>
        <div class="text-gray-500 text-[13px] leading-[18px] mt5">
            <div>
                <template v-if="fileType.length">支持格式: {{ fileType.join(', ') }}</template>
                <template v-if="fileType.length && showFileSizeLimit">, </template>
                <template v-if="showFileSizeLimit">文件不能超过{{ fileSize }}MB</template>
            </div>
            <slot name="tip" />
        </div>
        <el-image-viewer
            v-if="imgViewVisible"
            :teleported="true"
            :url-list="[_filePath.fullPath.includes('http') ? _filePath.fullPath : baseURL + _filePath.fullPath]"
            @close="imgViewVisible = false"
        />
    </div>
</template>

<script setup lang="ts" name="UploadImg">
import { ref, computed, inject } from 'vue';
import { ElNotification, formContextKey } from 'element-plus';
import type { UploadProps, UploadRequestOptions } from 'element-plus';
import { generateUUID } from '/@/utils/other';
import { getUpdateSign } from '/@/api/common/index';
import OSS from 'ali-oss';

interface UploadFileProps {
    serviceName: string; // 服务名, 后续直接加在类型里 'teaching' | 'xxxxx' | 'xxxxxxxxx' ==> 必传
    moduleName: string; // 模块名 ==> 必传 随便传
    filePath?: {
        relativePath: string; // 相对路径
        fullPath: string; // 全路径
    }; // 文件路径
    relativePath?: string; // 相对路径
    fullPath?: string; // 全路径
    drag?: boolean; // 是否支持拖拽上传 ==> 非必传（默认为 true）
    disabled?: boolean; // 是否禁用上传组件 ==> 非必传（默认为 false）
    fileSize?: number; // 大小限制 ==> 非必传（默认为 5M）
    fileType?: string[]; // 文件类型限制 ==> 非必传
    width?: string; // 组件宽度 ==> 非必传（默认为 120px）
    height?: string; // 组件高度 ==> 非必传（默认为 120px）
    borderRadius?: string; // 组件边框圆角 ==> 非必传（默认为 8px）
    iconSize?: number; // 图标大小 ==> 非必传（默认为 24px）
}

// 接受父组件参数
const props = withDefaults(defineProps<UploadFileProps>(), {
    serviceName: 'teaching',
    moduleName: 'courseware',
    filePath: undefined,
    relativePath: undefined,
    fullPath: undefined,
    drag: true,
    disabled: false,
    fileSize: 5,
    fileType: () => ['png', 'jpg', 'jpeg', 'doc', 'xls', 'ppt', 'txt', 'pdf', 'docx', 'xlsx', 'pptx'],
    width: '120px',
    height: '120px',
    borderRadius: '8px',
    iconSize: 24,
});

const imageFileTypeMaps = {
    jpg: 'image/jpeg',
    png: 'image/png',
    jpeg: 'image/jpeg',
    gif: 'image/gif',
    bmp: 'image/bmp',
    svg: 'image/svg+xml',
    webp: 'image/webp',
    ico: 'image/x-icon',
    tiff: 'image/tiff',
    tif: 'image/tiff',
    psd: 'image/vnd.adobe.photoshop',
};

// 生成组件唯一id
const uuid = ref('id-' + generateUUID());
// 查看图片
const imgViewVisible = ref(false);
// 获取 el-form 组件上下文
const formContext = inject(formContextKey, void 0);
// 计算是否显示文件大小限制提示
const showFileSizeLimit = computed(() => {
    return props.fileSize !== 0 && props.fileSize !== Infinity;
});

// 判断是否禁用上传和删除
const self_disabled = computed(() => {
    return props.disabled || formContext?.disabled;
});
const uploading = ref(false);

interface UploadEmits {
    (e: 'update:filePath', value: typeof props.filePath): void;
    (e: 'update:relativePath', value: string): void;
    (e: 'update:fullPath', value: string): void;
}
const emit = defineEmits<UploadEmits>();
const _filePath = computed(() => {
    return props.filePath || { relativePath: props.relativePath || '', fullPath: props.fullPath || '' };
});

// 获取允许上传的图片类型
const getAllowImageTypes = (fileType: string[], imageFileTypeMaps: Record<string, string>): string[] => {
    const fileTypeSet = new Set(fileType);
    return Object.keys(imageFileTypeMaps)
        .filter((key) => fileTypeSet.has(key) && imageFileTypeMaps.hasOwnProperty(key))
        .map((key) => imageFileTypeMaps[key]);
};
// 文件上传之前
const beforeUpload: UploadProps['beforeUpload'] = (rawFile) => {
    const imgSize = rawFile.size / 1024 / 1024 < props.fileSize;
    let allowImageType: string[] = [];
    if (props.fileType && rawFile?.type) {
        allowImageType = getAllowImageTypes(props.fileType, imageFileTypeMaps);
    }
    const isTypeOk = allowImageType.includes(rawFile.type);
    if (!isTypeOk)
        ElNotification({
            title: '温馨提示',
            message: '上传图片不符合所需的格式！',
            type: 'warning',
        });
    if (!imgSize)
        setTimeout(() => {
            ElNotification({
                title: '温馨提示',
                message: `上传图片大小不能超过 ${props.fileSize}M！`,
                type: 'warning',
            });
        }, 0);
    return isTypeOk && imgSize;
};

const handleHttpUpload = async (options: UploadRequestOptions) => {
    try {
        const fileName = options.file.name.split('.');
        const fileExt = fileName[fileName.length - 1];
        const res = await getUpdateSign(props.serviceName, props.moduleName, fileExt);
        if (res.data == null) return uploadError('图片上传失败！');
        uploading.value = true;
        const client = new OSS({
            bucket: res.data.bucket,
            region: 'oss-' + res.data.endpoint,
            accessKeyId: res.data.accessKeyId,
            accessKeySecret: res.data.accessKeySecret,
            stsToken: res.data.securityToken,
        });
        const result = await client.put(res.data.uploadPath, options.file);
        emit('update:filePath', { relativePath: result.name, fullPath: result.url });
        emit('update:relativePath', result.name);
        emit('update:fullPath', result.url);
        uploadSuccess();
    } catch (err) {
        uploadError(err);
    }
};

// 删除图片
const deleteImg = () => {
    emit('update:filePath', { relativePath: '', fullPath: '' });
    emit('update:relativePath', '');
    emit('update:fullPath', '');
};

// 编辑图片
const editImg = () => {
    const dom = document.querySelector(`#${uuid.value} .el-upload__input`);
    dom && dom.dispatchEvent(new MouseEvent('click'));
};

//  图片上传成功
const uploadSuccess = () => {
    uploading.value = false;
    ElNotification({
        title: '温馨提示',
        message: '图片上传成功！',
        type: 'success',
    });
};

// 图片上传错误
const uploadError = (err: any) => {
    uploading.value = false;
    ElNotification({
        title: '温馨提示',
        message: err.msg ?? '图片上传失败，请您重新上传！',
        type: 'error',
    });
};
</script>

<style scoped lang="scss">
.is-error {
    .upload {
        :deep(.el-upload),
        :deep(.el-upload-dragger) {
            border: 1px dashed var(--el-color-danger) !important;

            &:hover {
                border-color: var(--el-color-primary) !important;
            }
        }
    }
}

:deep(.disabled) {
    .el-upload,
    .el-upload-dragger {
        cursor: not-allowed !important;
        background: var(--el-disabled-bg-color);
        border: 1px dashed var(--el-border-color-darker) !important;

        &:hover {
            border: 1px dashed var(--el-border-color-darker) !important;
        }
    }
}

.upload-box {
    .no-border {
        :deep(.el-upload) {
            border: none !important;
        }
    }

    :deep(.upload) {
        .el-upload {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            width: v-bind(width);
            height: v-bind(height);
            overflow: hidden;
            border: 1px dashed var(--el-border-color-darker);
            border-radius: v-bind(borderRadius);
            transition: var(--el-transition-duration-fast);

            &:hover {
                border-color: var(--el-color-primary);

                .upload-handle {
                    opacity: 1;
                }
            }

            .el-upload-dragger {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 100%;
                height: 100%;
                padding: 0;
                overflow: hidden;
                background-color: transparent;
                border: 1px dashed var(--el-border-color-darker);
                border-radius: v-bind(borderRadius);

                &:hover {
                    border: 1px dashed var(--el-color-primary);
                }
            }

            .el-upload-dragger.is-dragover {
                background-color: var(--el-color-primary-light-9);
                border: 2px dashed var(--el-color-primary) !important;
            }

            .upload-image {
                width: 100%;
                height: 100%;
                object-fit: contain;
            }

            .upload-empty {
                position: relative;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                font-size: 12px;
                line-height: 30px;
                color: var(--el-color-info);

                .el-icon {
                    font-size: 28px;
                    color: var(--el-text-color-secondary);
                }
            }

            .upload-handle {
                position: absolute;
                top: 0;
                right: 0;
                box-sizing: border-box;
                display: flex;
                align-items: center;
                justify-content: center;
                width: 100%;
                height: 100%;
                cursor: pointer;
                background: rgb(0 0 0 / 60%);
                opacity: 0;
                transition: var(--el-transition-duration-fast);

                .handle-icon {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    padding: 0 6%;
                    color: aliceblue;

                    .el-icon {
                        margin-bottom: 40%;
                        font-size: 130%;
                        line-height: 130%;
                    }

                    span {
                        font-size: 85%;
                        line-height: 85%;
                    }
                }
            }
        }
    }
}
</style>
