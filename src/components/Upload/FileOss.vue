<template>
    <el-upload
        action="#"
        :multiple="props.multiple"
        :disabled="self_disabled"
        :show-file-list="props.showFileList"
        :file-list="computedFileList"
        :limit="props.maxFiles"
        :before-upload="beforeUpload"
        :http-request="handleHttpUpload"
        :on-error="uploadError"
        :accept="fileType.join(',')"
    >
        <template v-if="uploading">
            <el-button text type="primary" loading>上传中...</el-button>
        </template>
        <template v-else>
            <slot name="empty">
                <el-button icon="upload" text type="primary"> 上传 </el-button>
            </slot>
        </template>
    </el-upload>
    <div class="text-gray-500 text-[13px] leading-[18px] mt5">
        <div>
            <template v-if="fileType.length">支持格式: {{ fileType.join(', ') }}</template>
            <template v-if="fileType.length && showFileSizeLimit">, </template>
            <template v-if="showFileSizeLimit">文件不能超过{{ fileSize }}MB</template>
        </div>
        <slot name="tip" />
    </div>
</template>

<script setup lang="ts" name="UploadImg">
import { ref, computed, inject } from 'vue';
import { ElNotification, formContextKey } from 'element-plus';
import type { UploadProps, UploadRequestOptions } from 'element-plus';
import { getUpdateSign } from '/@/api/common/index';
import OSS from 'ali-oss';

interface UploadFileProps {
    serviceName: 'teaching'; // 服务名, 后续直接加在类型里 'teaching' | 'xxxxx' | 'xxxxxxxxx' ==> 必传
    moduleName: string; // 模块名 ==> 必传 随便传
    filePath?: {
        fileName: string;
        relativePath: string; // 相对路径
        fullPath: string; // 全路径
    }; // 文件路径 ==> 必传
    disabled?: boolean; // 是否禁用上传组件 ==> 非必传（默认为 false）
    fileSize?: number; // 大小限制 ==> 非必传（默认为 1000M）
    fileType?: string[]; // 文件类型限制 ==> 非必传
    multiple?: boolean; // 是否支持多文件上传，默认false
    maxFiles?: number; // 最大文件数量，默认1
    existingFiles?: Array<{ fileName: string; url: string; relativePath?: string }>; // 已存在的文件列表
    showFileList?: boolean; // 是否显示el-upload自带的文件列表，默认false
}

// 接受父组件参数
const props = withDefaults(defineProps<UploadFileProps>(), {
    serviceName: 'teaching',
    moduleName: '',
    filePath: () => ({ fileName: '', relativePath: '', fullPath: '' }),
    disabled: false,
    fileSize: 1000,
    fileType: () => ['png', 'jpg', 'jpeg', 'doc', 'xls', 'ppt', 'txt', 'pdf', 'docx', 'xlsx', 'pptx', 'mp4'],
    multiple: false,
    maxFiles: 1,
    existingFiles: () => [],
    showFileList: false,
});

// 获取 el-form 组件上下文
const formContext = inject(formContextKey, void 0);
// 计算是否显示文件大小限制提示
const showFileSizeLimit = computed(() => {
    return props.fileSize !== 0 && props.fileSize !== Infinity;
});
// 判断是否禁用上传和删除
const self_disabled = computed(() => {
    return props.disabled || formContext?.disabled;
});
const uploading = ref(false);
const computedFileList = computed(() => {
    const files = [];
    // 添加已存在的文件
    if (props.existingFiles?.length > 0) {
        files.push(
            ...props.existingFiles.map((file) => ({
                name: file.fileName,
                url: file.url,
            }))
        );
    }
    // 添加当前上传的文件（如果存在且不在已存在列表中）
    if (props.filePath?.fullPath && !props.existingFiles?.some((f) => f.url === props.filePath.fullPath)) {
        files.push({
            name: props.filePath.fileName || props.filePath.fullPath,
            url: props.filePath.fullPath,
        });
    }
    return files;
});

interface UploadEmits {
    (e: 'update:filePath', value: typeof props.filePath): void;
    (e: 'success', value: typeof props.filePath): void;
}
const emit = defineEmits<UploadEmits>();
// 文件上传之前
const beforeUpload: UploadProps['beforeUpload'] = (rawFile) => {
    // 检查文件数量限制
    if (computedFileList.value.length >= props.maxFiles) {
        ElNotification({
            title: '温馨提示',
            message: `最多只能上传${props.maxFiles}个文件！`,
            type: 'warning',
        });
        return false;
    }

    // 检查文件类型
    const fileName = rawFile.name.split('.');
    const fileExt = fileName[fileName.length - 1].toLowerCase();
    const isTypeOk = props.fileType.indexOf(fileExt) >= 0;

    if (!isTypeOk) {
        ElNotification({
            title: '温馨提示',
            message: '上传文件不符合所需的格式！',
            type: 'warning',
        });
        return false;
    }

    // 只在设置了文件大小限制时检查大小
    if (showFileSizeLimit.value) {
        const imgSize = rawFile.size / 1024 / 1024 < props.fileSize;
        if (!imgSize) {
            setTimeout(() => {
                ElNotification({
                    title: '温馨提示',
                    message: `上传文件大小不能超过 ${props.fileSize}M！`,
                    type: 'warning',
                });
            }, 0);
            return false;
        }
    }
    return true;
};

const handleHttpUpload = async (options: UploadRequestOptions) => {
    try {
        const fileName = options.file.name.split('.');
        const fileExt = fileName[fileName.length - 1];
        const res = await getUpdateSign(props.serviceName, props.moduleName, fileExt);
        if (res.data == null) return uploadError('文件上传失败！');
        uploading.value = true;
        const client = new OSS({
            bucket: res.data.bucket,
            region: 'oss-' + res.data.endpoint,
            accessKeyId: res.data.accessKeyId,
            accessKeySecret: res.data.accessKeySecret,
            stsToken: res.data.securityToken,
        });
        const result = await client.put(res.data.uploadPath, options.file);
        emit('update:filePath', { fileName: options.file.name, relativePath: result.name, fullPath: result.url });
        emit('success', { fileName: options.file.name, relativePath: result.name, fullPath: result.url });
        uploadSuccess();
    } catch (err) {
        emit('update:filePath', { fileName: '', relativePath: '', fullPath: '' });
        uploadError(err);
    }
};

//  上传成功
const uploadSuccess = () => {
    uploading.value = false;
    ElNotification({
        title: '温馨提示',
        message: '文件上传成功！',
        type: 'success',
    });
};

// 上传错误
const uploadError = (err: any) => {
    uploading.value = false;
    ElNotification({
        title: '温馨提示',
        message: err.msg ?? '文件上传失败，请重新上传！',
        type: 'error',
    });
};
</script>

<style scoped lang="scss"></style>
