<template>
    <el-upload
        class="upload-demo"
        drag
        action="#"
        multiple
        :http-request="handleHttpUpload"
        :before-upload="beforeUpload"
        ref="uploadRef"
    >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div>点击或将文件拖拽到这里上传</div>
        <!-- 自定义文件显示，显示进度 -->
        <template #file="{ file }">
            <div class="mb-2">
                <div class="flex items-center justify-between">
                    <span class="text-sm flex-1 truncate">{{ file.name }}</span>
                    <el-button
                        type="danger"
                        size="small"
                        icon="el-icon-delete"
                        @click="handleRemoveFile(file)"
                        class="ml-2"
                        v-if="file.status === 'success'"
                    />
                </div>
                <div class="w-full flex items-center">
                    <div class="relative flex-1 h-[6px] bg-gray-200 rounded-full overflow-hidden">
                        <div
                            class="absolute left-0 top-0 h-full bg-primary transition-all duration-300"
                            :style="{ width: (uploadProgressMap[file.uid] || 0) + '%' }"
                        ></div>
                    </div>
                    <span class="ml-2 w-10 text-xs text-gray-700 text-right">
                        {{ uploadProgressMap[file.uid] || 0 }}%
                    </span>
                </div>
            </div>
        </template>

        <template #tip>
            <div>
                <p>
                    1.资料格式规格要求：zip、rar、jpg、jpeg、png、mp4、pdf、mp3、pptx、ppt、docx、doc、xlsx、xls、txt、psd、ai
                </p>
                <p>2.资料大小规格要求：{{ fileSize }}MB以内(含{{ fileSize }}MB)；</p>
            </div>
        </template>
    </el-upload>
</template>

<script setup lang="ts">
import { UploadFilled } from '@element-plus/icons-vue';
import { ElNotification, UploadProps, UploadRequestOptions } from 'element-plus';
import { uploadInformationTokenApi } from '/@/api/eduConnect/information';
import { useRoute } from 'vue-router';
import OSS from 'ali-oss';
const route = useRoute();

interface UploadFileProps {
    filePath?: {
        fileName: string;
        relativePath: string; // 相对路径
    }; // 文件路径 ==> 必传
    disabled?: boolean; // 是否禁用上传组件 ==> 非必传（默认为 false）
    fileSize?: number; // 大小限制 ==> 非必传（默认为 1000M）
    fileType?: string[]; // 文件类型限制 ==> 非必传
    multiple?: boolean; // 是否支持多文件上传，默认false
}
interface UploadEmits {
    (e: 'update:filePath', value: typeof props.filePath): void;
    (e: 'success', value: typeof props.filePath): void;
    (e: 'remove', value: any): void; // 新增
}
const emit = defineEmits<UploadEmits>();
const uploading = ref(false);
// 接受父组件参数
const props = withDefaults(defineProps<UploadFileProps>(), {
    filePath: () => ({ fileName: '', relativePath: '' }),
    disabled: false,
    fileSize: 1000,
    fileType: () => [
        'zip',
        'rar',
        'jpg',
        'jpeg',
        'png',
        'mp4',
        'pdf',
        'mp3',
        'pptx',
        'ppt',
        'docx',
        'doc',
        'xlsx',
        'xls',
        'txt',
        'psd',
        'ai',
    ],
    multiple: true,
});
const showFileSizeLimit = computed(() => {
    return props.fileSize !== 0 && props.fileSize !== Infinity;
});
const uploadProgressMap = reactive<Record<number, number>>({});
const uploadRef = ref();
const handleHttpUpload = async (options: UploadRequestOptions) => {
    try {
        const file = options.file;
        const fileNameArr = file.name.split('.');
        const fileExt = fileNameArr[fileNameArr.length - 1];
        const originalName = file.name;
        const hasChinese = /[\u4e00-\u9fa5]/.test(originalName);
        const newFileName = hasChinese
            ? `${Date.now()}_${Math.random().toString(36).slice(2)}.${fileExt}`
            : originalName;

        const res = await uploadInformationTokenApi({
            id: route.query.id as string,
        });
        if (!res.data) return uploadError('文件上传失败！');

        uploading.value = true;
        uploadProgressMap[file.uid] = 0; // 初始化进度

        const client = new OSS({
            bucket: res.data.bucket,
            region: res.data.region,
            accessKeyId: res.data.accessKeyId,
            accessKeySecret: res.data.accessKeySecret,
            stsToken: res.data.securityToken,
        });

        const result = await client.multipartUpload(res.data.uploadPathPrefix + '/' + newFileName, file, {
            progress: function (percentage) {
                uploadProgressMap[file.uid] = Math.floor(percentage * 100);
            },
        });

        emit('update:filePath', {
            fileName: originalName,
            relativePath: result.name,
        });
        emit('success', {
            fileName: originalName,
            relativePath: result.name,
        });
        uploadSuccess(file.uid);
    } catch (err) {
        if (options.file?.uid) {
            delete uploadProgressMap[options.file.uid];
        }
        uploadError(err);
    }
};
// 文件上传之前
const beforeUpload: UploadProps['beforeUpload'] = (rawFile) => {
    // 检查文件类型
    const fileName = rawFile.name.split('.');
    const fileExt = fileName[fileName.length - 1].toLowerCase();
    const isTypeOk = props.fileType.indexOf(fileExt) >= 0;

    if (!isTypeOk) {
        setTimeout(() => {
            ElNotification({
                title: '温馨提示',
                message: '上传文件不符合所需的格式！',
                type: 'warning',
            });
        }, 0);
        return false;
    }

    // 只在设置了文件大小限制时检查大小
    if (showFileSizeLimit.value) {
        const imgSize = rawFile.size / 1024 / 1024 < props.fileSize;
        if (!imgSize) {
            setTimeout(() => {
                ElNotification({
                    title: '温馨提示',
                    message: `上传文件大小不能超过 ${props.fileSize}M！`,
                    type: 'warning',
                });
            }, 0);
            return false;
        }
    }
    return true;
};
const uploadSuccess = (uid: number) => {
    uploading.value = false;
    uploadProgressMap[uid] = 100;
    ElNotification({
        title: '温馨提示',
        message: '文件上传成功！',
        type: 'success',
    });
};

const uploadError = (err: any) => {
    uploading.value = false;
    ElNotification({
        title: '温馨提示',
        message: err?.msg ?? '文件上传失败，请重新上传！',
        type: 'error',
    });
    if (err.uid) {
        delete uploadProgressMap[err.uid];
    }
};

// 删除文件方法
const handleRemoveFile = (file: any) => {
    uploadRef.value.handleRemove(file);
    delete uploadProgressMap[file.uid];
    emit('remove', file); // 新增
};

const clearUploadList = () => {
    uploadRef.value.clearFiles();
    uploadRef.value.abort();
};
onUnmounted(() => {
    clearUploadList();
});
defineExpose({
    clearUploadList,
});
</script>
