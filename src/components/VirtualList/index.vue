<template>
    <div ref="container" class="virtual-list-container" @scroll="handleScroll">
        <div class="virtual-list-phantom" :style="{ height: totalHeight + 'px' }"></div>
        <div class="virtual-list-content" :style="{ transform: `translateY(${offsetY}px)` }">
            <div
                v-for="(item, index) in visibleData"
                :key="getItemKey(item, index)"
                class="virtual-list-item"
                :style="{ height: itemHeight + 'px', lineHeight: itemHeight + 'px' }"
            >
                <slot :item="item" :index="startIndex + index"></slot>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, nextTick, defineExpose } from 'vue';

const props = defineProps({
    data: {
        type: Array,
        required: true,
    },
    itemHeight: {
        type: Number,
        default: 40,
    },
    buffer: {
        type: Number,
        default: 5,
    },
    keyField: {
        type: String,
        default: '',
    },
});

const container = ref<HTMLElement | null>(null);
const scrollTop = ref(0);
const viewportHeight = ref(0);
const forceRender = ref(0); // 强制渲染的计数器

// 计算总高度
const totalHeight = computed(() => props.data.length * props.itemHeight);

// 计算可见区域内应该渲染的数据起始索引
const startIndex = computed(() => {
    return Math.max(0, Math.floor(scrollTop.value / props.itemHeight) - props.buffer);
});

// 计算可见数据的结束索引
const endIndex = computed(() => {
    const visibleCount = Math.ceil(viewportHeight.value / props.itemHeight);
    return Math.min(props.data.length - 1, startIndex.value + visibleCount + props.buffer);
});

// 计算偏移量
const offsetY = computed(() => {
    return startIndex.value * props.itemHeight;
});

// 计算可见数据
const visibleData = computed(() => {
    // 使用forceRender作为依赖，当它变化时重新计算
    forceRender.value;
    return props.data.slice(startIndex.value, endIndex.value + 1);
});

// 获取列表项的唯一键
const getItemKey = (item: any, index: number): string => {
    if (props.keyField && item[props.keyField] !== undefined) {
        return item[props.keyField];
    }
    // 使用项目索引和一个随机后缀，确保键的唯一性
    return `item-${startIndex.value + index}-${forceRender.value}`;
};

// 滚动事件处理
const handleScroll = (e: Event) => {
    const target = e.target as HTMLElement;
    scrollTop.value = target.scrollTop;
};

// 强制重新渲染所有项目
const forceUpdate = () => {
    forceRender.value++;

    // 确保DOM更新
    nextTick(() => {
        if (container.value) {
            // 触发一个小的滚动来更新视图
            const currentScroll = container.value.scrollTop;
            container.value.scrollTop = currentScroll + 1;
            setTimeout(() => {
                container.value!.scrollTop = currentScroll;
            }, 10);
        }
    });
};

// 重置滚动位置
const resetScroll = () => {
    if (container.value) {
        container.value.scrollTop = 0;
    }
};

// 组件挂载后测量容器高度
onMounted(() => {
    if (container.value) {
        viewportHeight.value = container.value.clientHeight;

        // 监听容器大小变化
        const resizeObserver = new ResizeObserver((entries) => {
            for (const entry of entries) {
                viewportHeight.value = entry.contentRect.height;
                forceUpdate(); // 当大小变化时强制更新
            }
        });

        resizeObserver.observe(container.value);
    }
});

// 监听数据变化
watch(
    () => props.data,
    (newData, oldData) => {
        if (newData !== oldData) {
            // 数据变化时强制更新视图
            forceUpdate();

            // 如果数据长度显著变化，可能需要重置滚动位置
            if (oldData.length === 0 && newData.length > 0) {
                resetScroll();
            }
        }
    },
    { deep: true }
);

// 向父组件暴露方法
defineExpose({
    forceUpdate,
    resetScroll,
});
</script>

<style scoped>
.virtual-list-container {
    position: relative;
    overflow-y: auto;
    height: 100%;
}

.virtual-list-phantom {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    z-index: -1;
}

.virtual-list-content {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    overflow: hidden;
}

.virtual-list-item {
    padding: 0;
    box-sizing: border-box;
}
</style>
