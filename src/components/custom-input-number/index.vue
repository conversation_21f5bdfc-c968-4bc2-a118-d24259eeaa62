<template>
    <el-input
        v-model="inputValue"
        style="width: 240px"
        :formatter="formatter"
        :parser="parser"
        @blur="onBlur"
        v-on="$attrs"
    />
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue';

const props = defineProps<{
    modelValue: string | number;
}>();

const emit = defineEmits<{
    (e: 'update:modelValue', value: string): void;
}>();

const inputValue = ref(String(props.modelValue ?? ''));

watch(
    () => props.modelValue,
    (val) => {
        inputValue.value = String(val ?? '');
    }
);

watch(inputValue, (val) => {
    emit('update:modelValue', val);
});

// 输入过程中限制
const formatter = (value: string) => formatToTwoDecimals(value, false);
const parser = (value: string) => formatToTwoDecimals(value, false);

// 失焦时补齐两位小数
const onBlur = () => {
    inputValue.value = formatToTwoDecimals(inputValue.value, true);
};

// 格式化函数
function formatToTwoDecimals(value: string, padZero: boolean): string {
    let val = value.replace(/[^\d.]/g, '');
    val = val.replace(/^\.{1,}/, '0.');
    val = val.replace(/^0+(\d)/, '$1');
    val = val.replace(/\.{2,}/g, '.');
    val = val.replace('.', '#').replace(/\./g, '').replace('#', '.');

    const parts = val.split('.');
    if (parts.length === 1) {
        return padZero ? `${parts[0]}.00` : parts[0];
    }
    const integer = parts[0];
    const decimal = parts[1].slice(0, 2);
    return padZero ? `${integer}.${decimal.padEnd(2, '0')}` : `${integer}.${decimal}`;
}
</script>

<style lang="scss" scoped></style>
