<template>
    <el-page-header @back="goBack">
        <template #content>
            <div class="flex items-center">
                <slot name="title" />
                <span class="text-lg font-600 line-clamp-1" v-if="title"> {{ title }} </span>
                <span class="text-base ml-3 line-clamp-1" v-if="subTitle" style="color: var(--el-text-color-regular)">
                    {{ subTitle }}
                </span>
            </div>
        </template>
        <template #extra>
            <slot name="extra" />
        </template>
    </el-page-header>
</template>

<script setup lang="ts">
import mittBus from '/@/utils/mitt';
import { useTagsViewRoutes } from '/@/stores/tagsViewRoutes';

const tagsViewRoutesStore = useTagsViewRoutes();
const router = useRouter();
const route = useRoute();

const props = defineProps({
    title: { type: String },
    subTitle: { type: String },
    destroy: { type: <PERSON><PERSON><PERSON>, default: false },
    from: { type: String },
});

const goBack = () => {
    tagsViewRoutesStore.setCurrenFullscreen(false);
    if (props.from) {
        router.replace(props.from);
    } else {
        router.back();
    }
    if (props.destroy) {
        mittBus.emit('onCurrentContextmenuClick', { contextMenuClickId: 1, ...route });
    }
};

// 暴露变量
defineExpose({
    back: goBack,
});
</script>

<style lang="scss" scoped></style>
