<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title>Document</title>
	</head>
	<body style="padding: 0; margin: 0">
		<img id="image" style="display: block; width: 100vw; height: 100vh; object-fit: contain; background-color: black" />
		<script>
			var image = document.querySelector('#image');
			const queryString = window.location.search.substring(1);
			const queryParams = new URLSearchParams(queryString);
			const url = queryParams.get('url');
			image.src = url;
		</script>
	</body>
</html>
