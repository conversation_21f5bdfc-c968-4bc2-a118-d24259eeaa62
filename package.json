{"name": "ydsf-ui", "version": "1.6.0", "description": "约读书房双师管理后台", "author": "FrontendTeam", "license": "Proprietary", "scripts": {"dev": "vite --force", "serve": "vite --force --mode .env.local", "dev:test": "vite --force --mode test", "dev:prod": "vite --force --mode production", "build": "cross-env NODE_OPTIONS=--max-old-space-size=4096 vite build --mode production", "build:dev": "cross-env NODE_OPTIONS=--max-old-space-size=4096 vite build --mode development", "build:test": "cross-env NODE_OPTIONS=--max-old-space-size=4096 vite build --mode test", "build:docker": "cross-env NODE_OPTIONS=--max-old-space-size=4096 vite build --mode production --outDir ./docker/dist/", "build:docker:dev": "cross-env NODE_OPTIONS=--max-old-space-size=4096 vite build --mode development --outDir ./docker/dist/", "build:docker:test": "cross-env NODE_OPTIONS=--max-old-space-size=4096 vite build --mode test --outDir ./docker/dist/", "preview": "vite preview", "preview:dist": "vite preview --outDir dist", "analyze": "cross-env ANALYZE=true NODE_OPTIONS=--max-old-space-size=4096 vite build --mode production", "lint": "eslint --fix --ext .js,.ts,.vue ./src", "lint:style": "stylelint \"src/**/*.{vue,scss}\" --fix", "format": "prettier --write \"src/**/*.{js,ts,jsx,tsx,vue,css,scss,less,json,md}\"", "format:check": "prettier --check \"src/**/*.{js,ts,jsx,tsx,vue,css,scss,less,json,md}\"", "format:all": "prettier --write .", "clean": "rimraf dist node_modules", "typecheck": "vue-tsc --noEmit", "prepare": "husky", "test": "echo \"No tests specified\" && exit 0"}, "dependencies": {"@axolo/json-editor-vue": "^0.3.2", "@chenfengyuan/vue-qrcode": "^2.0.0", "@element-plus/icons-vue": "^2.0.10", "@popperjs/core": "2.11.8", "@vueuse/core": "^10.4.1", "@wangeditor/editor": "5.1.23", "@wangeditor/editor-for-vue": "5.1.12", "ali-oss": "^6.21.0", "aliyun-aliplayer": "^2.28.0", "aliyun-upload-vod": "^1.0.6", "autoprefixer": "^10.4.7", "axios": "^1.3.3", "codemirror": "5.65.5", "courseware-player": "^1.2.2", "crypto-js": "^4.2.0", "driver.js": "^0.9.8", "echarts": "^5.4.1", "element-plus": "2.5.5", "form-designer-plus": "^0.1.5", "highlight.js": "^11.7.0", "js-base64": "^3.7.7", "js-cookie": "^3.0.1", "json-editor-vue3": "^1.1.1", "lodash": "^4.17.21", "mitt": "^3.0.0", "moment": "^2.30.1", "nprogress": "^0.2.0", "pinia": "2.0.32", "postcss": "8.4.40", "qrcode": "1.5.1", "qs": "^6.11.0", "screenfull": "^6.0.2", "sm-crypto": "^0.3.12", "sortablejs": "^1.15.0", "splitpanes": "^3.1.5", "streamsaver": "^2.0.6", "tailwindcss": "3.4.6", "v-calendar": "3.1.2", "viewerjs": "^1.11.6", "vue": "3.4.15", "vue-clipboard3": "^2.0.0", "vue-draggable-plus": "^0.6.0", "vue-echarts": "6.6.1", "vue-i18n": "9.2.2", "vue-router": "^4.1.6", "vue3-tree-org": "^4.2.2", "vue3-video-play": "1.3.1-beta.6", "vuedraggable": "^4.1.0"}, "devDependencies": {"@commitlint/cli": "^19.5.0", "@commitlint/config-conventional": "^19.5.0", "@types/ali-oss": "^6.16.11", "@types/crypto-js": "^4.2.2", "@types/markdown-it": "^14.1.1", "@types/node": "^18.14.0", "@types/nprogress": "^0.2.0", "@types/sm-crypto": "^0.3.4", "@types/sortablejs": "^1.15.0", "@typescript-eslint/eslint-plugin": "^5.53.0", "@typescript-eslint/parser": "^5.53.0", "@vitejs/plugin-vue": "^4.0.0", "@vue/compiler-sfc": "^3.2.47", "consola": "^2.15.3", "cross-env": "7.0.3", "daisyui": "4.11.1", "eslint": "^8.34.0", "eslint-config-prettier": "^8.10.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-vue": "^9.9.0", "husky": "^9.1.6", "pinia-plugin-persist": "^1.0.0", "prettier": "2.8.4", "rollup-plugin-copy": "^3.5.0", "sass": "^1.58.3", "terser": "^5.31.1", "typescript": "^4.9.5", "unplugin-auto-import": "^0.13.0", "vite": "^4.3.3", "vite-plugin-compression": "^0.5.1", "vite-plugin-style-import": "^2.0.0", "vite-plugin-top-level-await": "^1.3.0", "vite-plugin-vue-setup-extend": "^0.4.0", "vue-eslint-parser": "^9.1.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "engines": {"node": ">=16.0.0", "npm": ">= 7.0.0"}, "keywords": ["vue", "vue3", "vuejs/vue-next", "element-ui", "element-plus"], "repository": {"type": "git", "url": "http://gitlab.yuedushufang.com/web/integrate-project-all.git"}}