import vue from '@vitejs/plugin-vue';
import { resolve } from 'path';
import { defineConfig, loadEnv, ConfigEnv } from 'vite';
import vueSetupExtend from 'vite-plugin-vue-setup-extend';
import AutoImport from 'unplugin-auto-import/vite';
import topLevelAwait from 'vite-plugin-top-level-await';
import { createStyleImportPlugin, VxeTableResolve } from 'vite-plugin-style-import';
import viteCompression from 'vite-plugin-compression';
// @ts-ignore
import { svgBuilder } from '/@/components/IconSelector/index';
import copy from 'rollup-plugin-copy';
import dns from 'node:dns';
dns.setDefaultResultOrder('verbatim');

const pathResolve = (dir: string) => {
    return resolve(__dirname, '.', dir);
};

const alias: Record<string, string> = {
    '/@': pathResolve('./src/'),
    'vue-i18n': 'vue-i18n/dist/vue-i18n.cjs.js',
};

const replaceEnvVars = (content: string, env: Record<string, string>) => {
    let result = content;
    // 直接查找和替换 __VITE_XXX__ 格式的变量
    Object.keys(env).forEach((key) => {
        if (key.startsWith('VITE_')) {
            const placeholder = `__${key}__`;
            // 使用全局替换
            result = result.replace(new RegExp(placeholder, 'g'), env[key]);
        }
    });
    return result;
};

const viteConfig = defineConfig((mode: ConfigEnv) => {
    const env = loadEnv(mode.mode, process.cwd());
    // 判断是否开发环境
    const isDev = env.ENV === 'development';
    return {
        plugins: [
            vue(), // Vue 插件
            svgBuilder('./src/assets/icons/'), // 将 SVG 文件转换成 Vue 组件
            vueSetupExtend(), // setup语法糖增强插件
            AutoImport({
                imports: ['vue', 'vue-router', 'pinia'], // 自动导入的依赖库数组
                dts: './auto-imports.d.ts', // 自动导入类型定义文件路径
            }),
            createStyleImportPlugin({
                resolves: [VxeTableResolve()], // 配置vxetable 按需加载
            }),
            topLevelAwait({
                promiseExportName: '__tla', // TLA Promise 变量名
                promiseImportName: (i) => `__tla_${i}`, // TLA Promise 导入名
            }),
            viteCompression({
                deleteOriginFile: false, // 不删除原文件
                threshold: 10240, // 只压缩大于10KB的文件
                algorithm: 'gzip', // 使用gzip压缩
                ext: '.gz',
                filter: /\.(js|mjs|json|css|html)$/i, // 只压缩这些类型的文件
            }),
            copy({
                targets: [
                    {
                        src: 'room.html',
                        dest: 'dist',
                        transform: (contents) => {
                            return replaceEnvVars(contents.toString(), env);
                        },
                    }, // 将 room.html 复制到 dist
                ],
                hook: 'writeBundle', // 确保在打包完成后执行复制
            }),
        ],
        root: process.cwd(), // 项目根目录
        resolve: {
            alias, // 路径别名配置
            extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json', '.vue'], // 解析扩展名
        },
        base: mode.command === 'serve' ? './' : '/', // 简化基础路径配置，避免使用环境变量可能导致的问题
        optimizeDeps: {
            include: [
                // 常用UI框架
                'element-plus',
                'element-plus/es/locale/lang/zh-cn',
                'element-plus/es/locale/lang/en',
                '@element-plus/icons-vue',

                // 常用工具库
                'lodash',
                'axios',
                'moment',
                'crypto-js',
                'vue-i18n',
                'js-cookie',
                'js-base64',
                'qs',

                // Vue生态
                'vue',
                'vue-router',
                'pinia',
                '@vueuse/core',

                // 可视化和媒体相关
                'echarts',
                'vue-echarts',
                'vue3-video-play',

                // 拖拽和交互
                'sortablejs',
                'vuedraggable',
                'vue-draggable-plus',

                // 编辑器
                '@wangeditor/editor',
                '@wangeditor/editor-for-vue',

                // 阿里云相关
                'ali-oss',

                // 其他常用组件
                'screenfull',
                'qrcode',
                'splitpanes',
                'viewerjs',
                'vue3-tree-org',
                'v-calendar',

                // 特殊功能
                'sm-crypto',
                'nprogress',
            ],
            // 强制预构建这些依赖
            force: true,
        },
        server: {
            host: '0.0.0.0', // 服务器地址
            port: env.VITE_PORT as unknown as number, // 服务器端口号
            open: env.VITE_OPEN === 'true', // 是否自动打开浏览器
            hmr: true, // 启用热更新
            cors: true, // 允许跨域
            watch: {
                ignored: ['**/node_modules/**', '**/dist/**', '**/.git/**'], // 忽略监视这些目录，提高性能
            },
            proxy: {
                '/api/gen': {
                    //单体架构下特殊处理代码生成模块代理
                    target: env.VITE_IS_MICRO === 'true' ? env.VITE_ADMIN_PROXY_PATH : env.VITE_GEN_PROXY_PATH,
                    changeOrigin: true,
                    rewrite: (path) => path.replace(/^\/api/, ''),
                },
                '/api': {
                    target: env.VITE_ADMIN_PROXY_PATH, // 目标服务器地址
                    ws: true, // 是否启用 WebSocket
                    changeOrigin: true, // 是否修改请求头中的 Origin 字段
                    rewrite: (path) => path.replace(/^\/api/, ''),
                },
                '^/ws/info/.*': {
                    target: env.VITE_ADMIN_PROXY_PATH, // 目标服务器地址
                    ws: true, // 是否启用 WebSocket
                    changeOrigin: true,
                },
            },
        },
        build: {
            outDir: 'dist', // 打包输出目录
            chunkSizeWarningLimit: 2048, // 代码分包阈值
            // 限制worker数量，避免内存占用过高
            reportCompressedSize: false, // 禁用压缩大小报告，提高速度
            // 使用esbuild压缩，但只在开发环境下使用
            minify: isDev ? 'esbuild' : 'terser', // 生产环境使用terser，更加稳定
            // 配置esbuild压缩选项
            terserOptions: {
                compress: {
                    drop_console: true,
                    drop_debugger: true,
                },
                format: {
                    comments: false,
                },
            },
            // 不设置target，使用默认配置，避免兼容性问题
            assetsInlineLimit: 0, // 禁用资源内联，保持原有的资源加载方式
            rollupOptions: {
                output: {
                    entryFileNames: `assets/[name].[hash:8].js`,
                    chunkFileNames: `assets/[name].[hash:8].js`,
                    assetFileNames: `assets/[name].[hash:8].[ext]`,
                    compact: true,
                    manualChunks: {
                        vue: ['vue', 'vue-router', 'pinia'],
                        echarts: ['echarts'],
                        elementPlus: ['element-plus', '@element-plus/icons-vue'],
                        aliyunSdk: ['ali-oss', 'aliyun-aliplayer', 'aliyun-upload-vod'],
                        vendors: ['lodash', 'moment', 'crypto-js', 'axios', 'js-cookie'],
                        ui: ['viewerjs', 'splitpanes', 'sortablejs', 'vue-draggable-plus', 'vuedraggable'],
                    },
                },
                treeshake: {
                    // 不要禁用模块副作用，避免误删除有副作用的模块
                    moduleSideEffects: true,
                },
            },
            cache: true,
        },
        css: { preprocessorOptions: { css: { charset: false } } },
        define: {
            __VUE_I18N_LEGACY_API__: JSON.stringify(false),
            __VUE_I18N_FULL_INSTALL__: JSON.stringify(false),
            __INTLIFY_PROD_DEVTOOLS__: JSON.stringify(false),
            __VERSION__: JSON.stringify(process.env.npm_package_version),
            __NEXT_NAME__: JSON.stringify(process.env.npm_package_name),
        },
    };
});

export default viteConfig;
