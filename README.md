# integrate-project-all

约读书房后台集成系统

## 项目运行

- Get the project code

```bash
<NAME_EMAIL>:web/integrate-project-all.git
```

```bash
cd integrate-project-all

npm install
```

- run

```bash
npm run dev
```

- build

```bash
npm run build
```

## 代码提交规范

git add [file]

git commit -m "[type]: 提交信息"

type可选值(小写)：

1. build 与构建相关的提交，可能包括构建系统的配置更改、构建流程的优化等。例如：build: 调整构建脚本以支持新的环境变量。
2. chore 其他杂项任务，通常是一些不直接影响功能、文档、测试等方面的小改动，如更新依赖、调整配置文件等。例如：chore: 更新项目依赖包。
3. ci 与持续集成（Continuous Integration）相关的提交，比如配置 CI/CD 流水线、修复 CI 中的错误等。例如：ci: 修复持续集成脚本中的路径错误。
4. docs 文档更新，包括项目文档、API 文档、用户手册等的修改和完善。例如：docs: 更新项目 README 文件。
5. feat 新功能的添加。例如：feat: 实现用户个性化推荐功能。
6. fix 修复 bug，解决代码中的错误。例如：fix: 修复页面加载失败的问题。
7. perf 性能优化相关的提交，旨在提高代码的执行效率、减少资源占用等。例如：perf: 优化加载速度。
8. refactor 代码重构，对现有代码进行结构调整以提高可读性、可维护性或性能，但不改变其外部行为。例如：refactor: 提取重复代码为公共函数。
9. revert 回滚提交，用于撤销之前的某个提交。例如：revert: 回滚上一个有问题的提交。
10. style 代码格式方面的更改，如调整缩进、添加空格、统一代码风格等，不影响功能。例如：style: 统一代码缩进风格。
11. test 与测试相关的提交，包括添加测试用例、修复测试中的错误等。例如：test: 增加单元测试覆盖度。

> 注意
> git commit -m "[type]: 提交信息"   冒号后面有空格

## 分支命名规范

1. master 分支
master 为主分支，也是用于部署生产环境的分支，需要确保master分支稳定性。master 分支一般由 release 以及 hotfix 分支合并，任何时间都不能直接修改代码。

2. develop(dev) 分支
develop 为开发环境分支，始终保持最新完成以及bug修复后的代码，用于前后端联调。一般开发的新功能时，feature分支都是基于develop分支创建的。

3. feature 分支
开发新功能时，以dev为基础创建feature分支。
分支命名时以 feature/ 开头，后面可以加上开发的功能模块， 命名示例：feature/user_module、feature/cart_module

4. test分支
test为测试环境分支，外部用户无法访问，专门给测试人员使用，版本相对稳定。

5. hotfix 分支
线上出现紧急问题时，需要及时修复，以master分支为基线，创建hotfix分支。修复完成后，需要合并到 master 分支和 dev 分支。

分支命名以hotfix/ 开头的为修复分支，它的命名规则与 feature 分支类似。

## 项目开发规范

### 命名规范

- camelCase（小驼峰式命名法 —— 首字母小写）
- PascalCase（大驼峰式命名法 —— 首字母大写）
- kebab-case（短横线连接式）
- Snake（下划线连接式）

#### 1.1 项目文件命名

![项目根目录](https://ydsf-prod.oss-cn-beijing.aliyuncs.com/web_rules/file_name_1.png)
![项目views目录](https://ydsf-prod.oss-cn-beijing.aliyuncs.com/web_rules/file_name_2.png)

##### 1.1.1 项目命名

全部采用小写方式， 以短横线分隔。如 integrate-project-all

##### 1.1.2 目录名

参照项目命名规则，有复数结构时，要采用复数命名法。如：assets、images、icons、components、directive、enums、hooks、utils、views

##### 1.1.3 图像文件名: Snake方式

全部采用Snake命名方式，命名必须按照模块名拼接， 多个单词命名以下划线连接，首单词为模块名
如 双师系统模块名为 eduConnect ，用到的图片则名为 eduConnect_name.png

##### 1.1.4 HTML、CSS、js、ts、模块名（即服务名) 文件名: camelCase方式

全部采用camelCase方式
如 eduConnect 双师系统模块  eduConnectIndex.html eduConnectIndex.css等

#### 1.2 Vue组件名

![组件目录](https://ydsf-prod.oss-cn-beijing.aliyuncs.com/web_rules/componemts_name.png)

##### 1.2.1 单文件组件名

全部采用PascalCase方式，如 MyComponents

##### 1.2.2 紧密耦合的组件名

和父组件紧密耦合的子组件应该以父组件名作为前缀命名，放到同以文件夹下。
components/
|- RoleList
| |- RoleList.vue
| |- RoleListItem.vue
| |- RoleListItemButton.vue

##### 1.2.3 组件名中单词顺序

组件名应该以高级别的 (通常是一般化描述的) 单词开头，以描述性的修饰词结尾。
components/
|- Search
| |- SearchButtonClear.vue
| |- SearchButtonRun.vue
| |- SearchInputQuery.vue
| |- SearchInputExcludeGlob.vue
| |- SettingsCheckboxTerms.vue
| |- SettingsCheckboxLaunchOnStartup.vue

##### 1.2.4 完整单词的组件名

组件名应该倾向于完整而不是缩写。

#### 1.3 代码参数命名

##### 1.3.1 组件name

组件名应该始终是多个单词，应该始终是 PascalCase 的。 根组件 App 以及 <transition>、<component> 之类的 Vue 内置组件除外。

```vue
<script setup lang="ts" name="ProTable"></script>
```

##### 1.3.2 prop

在声明 prop 的时候，其命名应该始终使用 camelCase，而在模板和 JSX 中应该始终使用 kebab-case。

```vue
<my-component col-setting="colSetting"/>
```

##### 1.3.3 模板中组件

在 单文件组件 和 字符串模板 中 组件名 应该总是 kebab-case  的

```vue
<template>
    <my-component></my-component>
    </my-component>
</template>
```

##### 1.3.4 变量、方法（function）

```js
var myName = ""
function myFunc(){}
```

##### 1.3.5 常量

●1.命名方法：全部大写下划线分割，用于环境变量

```js
VITE_ADMIN_PROXY_PATH =  http://*************:9999
```

●2.命名方法：camelCase,vue3用法

```js
const myName = ref('')
const myFunc=()=>{}
```

##### 1.3.6 自定义事件 及 事件方法

自定义事件使用 kebab-case或 camelCase事件名 ，事件方法使用 camelCase 的事件名

```vue
<my-component @my-event="handleDoSomething" />
<my-component @myEvent="handleDoSomething" />
```

##### 1.3.7 api命名

●命名方法：camelCase
●pigx中的api名可能是后台生成的，自己写的时候注意

```js
export function addObj(obj?: Object) {
  return request({
    url: '/eduConnect/ssDevice',
    method: 'post',
    data: obj
  })
}
或
export const addObj(obj?: Object)=>{
    return request({
        url: '/eduConnect/ssDevice',
        method: 'post',
        data: obj
    })
}

```

##### 1.3.8 TS接口命名

●类型使用 PascalCase 命名。接口成员使用 camelCase 方式命名.

```ts
interface TestInter {
  name: string;
  myFunc(): number;
}
```

枚举值使用 PascalCase 命名。

```ts
enum StatusEnum {
 success = 'success'
}

```

##### 1.3.9 router

●命名方法：camelCase
●注意：router层级与文件目录必须相同，api应该对应模块名

![路由写法](https://ydsf-prod.oss-cn-beijing.aliyuncs.com/web_rules/router_name_1.png)
![路由写法](https://ydsf-prod.oss-cn-beijing.aliyuncs.com/web_rules/router_name_2.png)
![路由写法](https://ydsf-prod.oss-cn-beijing.aliyuncs.com/web_rules/router_name_3.png)
![路由写法](https://ydsf-prod.oss-cn-beijing.aliyuncs.com/web_rules/router_name_4.png)
