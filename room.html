<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <title>直播间</title>
        <!-- 引入 SDK， 2.9.10 为示例版本号，你可以通过发版说明或 GitHub 仓库分支查看最新版本号。 -->
        <!-- <script src="https://download.yuedushufang.com/resources/js/ss-live/agora-sdk/prod/edu_sdk.bundle.js"></script> -->
        <!-- <script src="https://download.yuedushufang.com/resources/js/ss-live/agora-sdk/edu_sdk.bundle.js?"+ new Date().getTime()></script> -->
        <!-- 引入 Widget，2.9.10 为示例版本号，你可以通过发版说明或 GitHub 仓库分支查看最新版本号。（2.9.0 之前版本的 Widget 与 SDK 一起打包，无需引入此库） -->
        <!-- <script src="https://download.agora.io/edu-apaas/release/<EMAIL>"></script> -->
        <style>
            #root {
                width: 100%;
                height: 100%;
            }

            .first-loading-wrp {
                display: flex;
                justify-content: center;
                align-items: center;
                flex-direction: column;
                min-height: 420px;
                height: 100%;
            }

            .first-loading-wrp > h1 {
                font-size: 128px;
            }

            .first-loading-wrp .loading-wrp {
                padding: 98px;
                display: flex;
                justify-content: center;
                align-items: center;
            }

            .dot {
                animation: antRotate 1.2s infinite linear;
                transform: rotate(45deg);
                position: relative;
                display: inline-block;
                font-size: 32px;
                width: 32px;
                height: 32px;
                box-sizing: border-box;
            }

            .dot i {
                width: 14px;
                height: 14px;
                position: absolute;
                display: block;
                background-color: #4ea259;
                border-radius: 100%;
                transform: scale(0.75);
                transform-origin: 50% 50%;
                opacity: 0.3;
                animation: antSpinMove 1s infinite linear alternate;
            }

            .dot i:nth-child(1) {
                top: 0;
                left: 0;
            }

            .dot i:nth-child(2) {
                top: 0;
                right: 0;
                -webkit-animation-delay: 0.4s;
                animation-delay: 0.4s;
            }

            .dot i:nth-child(3) {
                right: 0;
                bottom: 0;
                -webkit-animation-delay: 0.8s;
                animation-delay: 0.8s;
            }

            .dot i:nth-child(4) {
                bottom: 0;
                left: 0;
                -webkit-animation-delay: 1.2s;
                animation-delay: 1.2s;
            }

            @keyframes antRotate {
                to {
                    -webkit-transform: rotate(405deg);
                    transform: rotate(405deg);
                }
            }

            @-webkit-keyframes antRotate {
                to {
                    -webkit-transform: rotate(405deg);
                    transform: rotate(405deg);
                }
            }

            @keyframes antSpinMove {
                to {
                    opacity: 1;
                }
            }

            @-webkit-keyframes antSpinMove {
                to {
                    opacity: 1;
                }
            }
        </style>
    </head>

    <body>
        <div id="root">
            <div class="first-loading-wrp" id="firstLoading">
                <div class="loading-wrp">
                    <span class="dot dot-spin"><i></i><i></i><i></i><i></i></span>
                </div>
                <div style="display: flex; justify-content: center; align-items: center">正在进入直播间...</div>
            </div>
        </div>
        <script type="text/javascript">
            var firstLoading = document.getElementById('firstLoading');
            var script = document.createElement('script');
            // let url = localStorage.getItem('agoraSdkUrl')
            // let url = 'https://download.yuedushufang.com/resources/js/ss-live/agora-sdk/new-prod/edu_sdk.bundle.js';

            let url = '__VITE_LIVE_SDK_URL__';
            script.src = url + '?v=1.0.7&time=' + +new Date();
            document.head.appendChild(script);
            script.onload = function () {
                firstLoading.style.display = 'none';
                setAgoraEdu();
            };
            function setAgoraEdu() {
                const params = window.location.search.substring(1, window.location.search.length);
                let detail = params.split('&');
                let param = {};
                detail.forEach((item) => {
                    let list = item.split('=');
                    param[list[0]] = decodeURIComponent(list[1]);
                });
                // 配置 SDK。
                // 把 Your App ID 替换为你的 App ID。
                // const appId = window.localStorage.getItem('agoraAppId')
                AgoraEduSDK.config({
                    appId: param.appId,
                    region: 'cn',
                });
                let childrenCameraEncoderConfiguration = { width: 320, height: 240, frameRate: 15, bitrate: 200 };
                let teacherCameraEncoderConfiguration = { width: 1920, height: 1080, frameRate: 30, bitrate: 3000 };
                let launch_options = {
                    userUuid: param.userUuid,
                    userName: param.userName,
                    roomUuid: param.roomUuid,
                    roleType: Number(param.roleType), // 用户角色：1 为老师，2 为学生。
                    roomType: 4, // 房间类型：0 为一对一，2 为大班课，4 为小班课。
                    roomName: param.roomName,
                    pretest: true, // 是否开启课前设备检测。
                    rtmToken: param.rtmToken, // 测试环境下，你可以使用临时 RTM Token；生产或安全环境下，强烈建议你使用服务器生成的 RTM Token。
                    language: 'zh', // 课堂界面的语言。如需界面为英文，设为 'en' 即可。
                    duration: param.duration, // 课程时间，单位为秒。
                    startTime: param.startTime,
                    // recordUrl:
                    //   "https://solutions-apaas.agora.io/apaas/record/dev/2.8.0/record_page.html",
                    courseWareList: [],
                    virtualBackgroundImages: [], // 虚拟背景图片资源列表
                    // webrtcExtensionBaseUrl: "https://solutions-apaas.agora.io/static", // WebRTC 插件部署地址
                };
                window.localStorage.setItem('launch_options', JSON.stringify(launch_options));
                // 启动在线课堂。
                AgoraEduSDK.launch(document.querySelector('#root'), {
                    ...launch_options,
                    uiMode: 'light', // 设置课堂界面为明亮模式。如需界面为暗黑模式，设为 'dark' 即可。
                    widgets: {
                        // popupQuiz: AgoraSelector, // 答题器widgeet
                        // countdownTimer: AgoraCountdown, // 倒计时widget
                        // easemobIM: AgoraHXChatWidget, // IM widget
                        // mediaPlayer: FcrStreamMediaPlayerWidget, // 视频同步播放器widget
                        // poll: AgoraPolling, // 投票器widget
                        // watermark: FcrWatermarkWidget, // 水印widget
                        // webView: FcrWebviewWidget, // 内嵌浏览器widget
                        // netlessBoard: FcrBoardWidget, // 互动白板widget
                    },
                    mediaOptions: {
                        cameraEncoderConfiguration:
                            Number(param.roleType) === 2
                                ? childrenCameraEncoderConfiguration
                                : teacherCameraEncoderConfiguration,
                        // screenShareEncoderConfiguration: { width: 1920, height: 1080, frameRate: 30, bitrate: 3000 },
                    },
                    listener: (evt, args) => {
                        if (evt == 2) {
                            window.close();
                        }
                    },
                });
            }
        </script>
    </body>
</html>
